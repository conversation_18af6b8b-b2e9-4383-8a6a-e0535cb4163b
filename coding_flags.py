from pymongo import MongoClient
from collections import Counter

# ===== CONFIG =====
mongo_uri = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
db_name = "Cat2"
notes_collection = "Doctors_Notes"
encounters_collection = "Encounters"
flags_collection = "coding_flags"
index_name = "notes"
# ===================

client = MongoClient(mongo_uri)
notes_col = client[db_name][notes_collection]
encounters_col = client[db_name][encounters_collection]
flags_col = client[db_name][flags_collection]

# Process each note with an embedding
notes_cursor = notes_col.find({"embedding": {"$exists": True}})

for note in notes_cursor:
    note_id = note.get("noteId")
    encounter_id = note.get("encounterId")
    patient_id = note.get("patientId")
    embedding = note["embedding"]

    # Get actual codes from Encounter record
    encounter = encounters_col.find_one({"encounterId": encounter_id})
    actual_codes = encounter.get("icd10", []) if encounter else []

    # Run vector search to find similar notes
    pipeline = [
        {
            "$vectorSearch": {
                "index": index_name,
                "path": "embedding",
                "queryVector": embedding,
                "numCandidates": 100,
                "limit": 10
            }
        },
        {
            "$project": {
                "encounterId": 1,
                "similarityScore": { "$meta": "vectorSearchScore" }
            }
        }
    ]

    similar_notes = list(notes_col.aggregate(pipeline))

    # Collect codes from similar encounters
    suggested_codes = []
    for sim in similar_notes:
        sim_enc_id = sim.get("encounterId")
        if sim_enc_id == encounter_id:
            continue  # Skip self
        sim_enc = encounters_col.find_one({"encounterId": sim_enc_id})
        if sim_enc:
            suggested_codes.extend(sim_enc.get("icd10", []))

    # Most common code in similar notes
    if suggested_codes:
        top_code, count = Counter(suggested_codes).most_common(1)[0]

        if top_code not in actual_codes:
            flags_col.insert_one({
                "noteId": note_id,
                "encounterId": encounter_id,
                "patientId": patient_id,
                "suggestedCode": top_code,
                "confidence": round(count / len(similar_notes), 2),
                "source": "vector + rule",
                "reason": f"Top {count} similar notes coded as {top_code}, but not present in this encounter."
            })

print("✅ Coding flags generated.")

