import streamlit as st
from pymongo import MongoClient
import pandas as pd

# MongoDB Config
MONGO_URI = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
DB_NAME = "Cat2"
NOTES_COLLECTION = "Doctors_Notes"
FLAGS_COLLECTION = "Coding_Flags"

# Connect to MongoDB
client = MongoClient(MONGO_URI)
db = client[DB_NAME]
notes_col = db[NOTES_COLLECTION]
flags_col = db[FLAGS_COLLECTION]

# Streamlit App
st.title("Clinical Coding Flag Explorer")

# Fetch unique encounterIds
encounter_ids = notes_col.distinct("encounterId")
selected_encounter = st.selectbox("Select Encounter ID", sorted(encounter_ids))

if selected_encounter:
    # Show Doctor's Note
    note = notes_col.find_one({"encounterId": selected_encounter})
    st.subheader("Doctor's Note")
    st.write(note["text"] if note else "No note found.")

    # Show Coding Flag
    flag = flags_col.find_one({"encounterId": selected_encounter})
    st.subheader("Coding Flag")
    if flag:
        st.markdown(f"**Suggested Code:** `{flag['suggestedCode']}`")
        st.markdown(f"**Confidence:** `{flag['confidence']}`")
        st.markdown(f"**Reason:** {flag['reason']}")
        st.markdown(f"**Source:** _{flag['source']}_")
    else:
        st.info("No coding flag found for this encounter.")

