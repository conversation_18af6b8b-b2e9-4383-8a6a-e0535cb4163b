import streamlit as st
from pymongo import MongoClient
import re

# MongoDB Config
MONGO_URI = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
DB_NAME = "Cat2"
NOTES_COLLECTION = "Doctors_Notes"
FLAGS_COLLECTION = "Coding_Flags"

# Connect to MongoDB
client = MongoClient(MONGO_URI)
db = client[DB_NAME]
notes_col = db[NOTES_COLLECTION]
flags_col = db[FLAGS_COLLECTION]

# Simple keyword-based code suggestion engine
SIMPLE_CODE_MAP = {
    "diabetes": "E11.9",
    "hypertension": "I10",
    "chest pain": "R07.9",
    "fever": "R50.9",
    "shortness of breath": "R06.02",
    "asthma": "J45.909",
    "back pain": "M54.5"
}

def suggest_code(note_text):
    for keyword, code in SIMPLE_CODE_MAP.items():
        if re.search(rf"\b{keyword}\b", note_text, re.IGNORECASE):
            return {
                "suggestedCode": code,
                "confidence": "0.85",
                "reason": f"Matched keyword: '{keyword}'",
                "source": "Simple NLP Engine"
            }
    return None

# Streamlit UI
st.title("Clinical Coding Flag Explorer")

# Sidebar: show total stored flags
total_flags = flags_col.count_documents({})
st.sidebar.metric("Stored Coding Flags", total_flags)

# Mode switch: Stored vs Live (NLP)
mode = st.radio("Flagging Mode", ["Stored", "Live (NLP)"])

# Fetch encounter IDs
all_ids = sorted(notes_col.distinct("encounterId"))

# Label logic based on mode
if mode == "Stored":
    flagged_ids = set(flags_col.distinct("encounterId"))
    labeled_ids = [
        (f"{eid} ✅" if eid in flagged_ids else f"{eid} ❌", eid)
        for eid in all_ids
    ]
else:
    labeled_ids = [(eid, eid) for eid in all_ids]

# Create select box with correct labels
label_options = [label for label, _ in labeled_ids]
selected_label = st.selectbox("Select Encounter ID", label_options)

# Look up actual encounter ID from selection
selected_encounter = dict(labeled_ids)[selected_label]

# Show doctor’s note
note = notes_col.find_one({"encounterId": selected_encounter})
st.subheader("Doctor's Note")
st.write(note["text"] if note else "No note found.")

# Show flag (stored or live NLP)
st.subheader("Coding Flag")

if mode == "Stored":
    flag = flags_col.find_one({"encounterId": selected_encounter})
elif note:
    flag = suggest_code(note["text"])
else:
    flag = None

if flag:
    st.markdown(f"**Suggested Code:** `{flag['suggestedCode']}`")
    st.markdown(f"**Confidence:** `{flag['confidence']}`")
    st.markdown(f"**Reason:** {flag['reason']}")
    st.markdown(f"**Source:** _{flag['source']}_")
else:
    st.info("No coding flag found for this encounter.")

