import streamlit as st
import pymongo
import openai
import logging
from voyageai import Client as VoyageClient
import html

# --- Custom CSS for layout and style ---
st.markdown("""
    <style>
        .block-container { padding-top: 1.5rem; padding-bottom: 1.5rem; }
        .stTextInput>div>div>input { margin-bottom: 0; }
        .history-box { background: #f7f7fa; border-radius: 8px; padding: 1rem; margin-bottom: 1rem; }
        .answer-box { background: #e8f5e9; border-radius: 6px; padding: 0.75rem; margin-top: 0.5rem; margin-bottom: 0.5rem; }
        .question { margin-bottom: 0.25rem; font-weight: bold; color: #1a237e; }
        code { font-size: 0.9em; color: #333; }
    </style>
""", unsafe_allow_html=True)

# --- Setup ---
logging.basicConfig(level=logging.INFO)
MONGO_URI = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
OPENAI_KEY = "***********************************************************************************************"       # ✅ Replace with your OpenAI key
VOYAGE_KEY = "pa-7dduYV1BTZVrUAFXlQqmkAhFk90TWQcL4Cah5I7oh9H"       # ✅ Replace with your Voyage AI key

client = pymongo.MongoClient(MONGO_URI)
db = client.bpe_demo_structured
drug_pricing = db.drug_pricing

openai.api_key = OPENAI_KEY
voyage_client = VoyageClient(api_key=VOYAGE_KEY)

def find_cheapest_drug(drug_name, city_name):
    query = {
        "drug": {"$regex": drug_name, "$options": "i"},
        "location": {"$regex": city_name, "$options": "i"}
    }
    sort = [("price", 1)]
    result = drug_pricing.find(query).sort(sort).limit(1)
    query_str = f"db.drug_pricing.find({query}).sort({sort}).limit(1)"
    return list(result), query_str

def search_documents(query, customer=None, top_k=3):
    try:
        response = voyage_client.embed([query], model="voyage-lite-02-instruct")
        query_vector = response.embeddings[0]
        pipeline = [
            {
                "$vectorSearch": {
                    "queryVector": "[query_vector]",
                    "path": "embedding",
                    "numCandidates": 50,
                    "limit": top_k,
                    "index": "drug_pricing"
                }
            }
        ]
        if customer:
            pipeline.append({"$match": {"customer": customer}})
        query_str = f"db.customer_pricing.aggregate({pipeline})"
        exec_pipeline = [
            {
                "$vectorSearch": {
                    "queryVector": query_vector,
                    "path": "embedding",
                    "numCandidates": 50,
                    "limit": top_k,
                    "index": "drug_pricing"
                }
            }
        ]
        if customer:
            exec_pipeline.append({"$match": {"customer": customer}})
        return list(db.customer_pricing.aggregate(exec_pipeline)), query_str
    except Exception as e:
        logging.error(f"Search error: {e}")
        return [], "Error generating query"

def generate_recommendation(user_query, customer=None):
    results, query_str = search_documents(user_query, customer)
    if not results:
        return "No relevant pricing information found.", query_str
    context = "\n".join(
        f"- {doc.get('drug', 'Unknown Drug')} at {doc.get('pharmacy', 'Unknown Pharmacy')} in {doc.get('location', 'Unknown Location')} for ${doc.get('price', 'N/A')}"
        for doc in results
    )
    prompt = f"""
    You are a helpful assistant in a pharmaceutical pricing recommendation system.
    The user asked: "{user_query}"
    Here is relevant context from internal pricing records:
    {context}
    Please generate a concise and helpful pricing recommendation.
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a helpful pricing assistant."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=300
        )
        return response.choices[0].message["content"].strip(), query_str
    except Exception as e:
        logging.error(f"OpenAI generation error: {e}")
        return "An error occurred while generating a recommendation.", query_str

# --- Streamlit UI ---
st.title("💊 BPE Pricing Assistant")
st.subheader("Structured Data Edition")
st.markdown("Ask your pharmaceutical pricing questions and get instant recommendations!")

customers = ["AcmeHealth", "BestCare"]
selected_customer = st.selectbox("Select your customer:", customers, key="customer")

if "history" not in st.session_state:
    st.session_state.history = []

user_input = st.text_input("Ask your drug pricing question:", key="user_input")

if st.button("Get Recommendation") or user_input:
    matched = False
    results = []
    query_str = ""
    if "metformin" in user_input.lower() and "houston" in user_input.lower():
        results, query_str = find_cheapest_drug("Metformin", "Houston")
        matched = True
    elif "lipitor" in user_input.lower() and "chicago" in user_input.lower():
        results, query_str = find_cheapest_drug("Lipitor", "Chicago")
        matched = True
    elif "atorvastatin" in user_input.lower() and "new york" in user_input.lower():
        results, query_str = find_cheapest_drug("Atorvastatin", "New York City")
        matched = True
    elif "ozempic" in user_input.lower() and "los angeles" in user_input.lower():
        results, query_str = find_cheapest_drug("Ozempic", "Los Angeles")
        matched = True
    elif "trulicity" in user_input.lower() and "atlanta" in user_input.lower():
        results, query_str = find_cheapest_drug("Trulicity", "Atlanta")
        matched = True

    if matched and results:
        doc = results[0]
        answer = f"{doc['drug']} is available at {doc['pharmacy']} in {doc['location']} for ${doc['price']}."
    else:
        answer, query_str = generate_recommendation(user_input, customer=selected_customer)

    # Display current answer + query (escaped for safety)
    safe_answer = html.escape(answer)
    safe_query = html.escape(query_str)
    st.markdown(f"<div class='answer-box'>{safe_answer}</div>", unsafe_allow_html=True)
    st.markdown(f"<div style='font-size: 0.85em; color: #666;'>Query: <code>{safe_query}</code></div>", unsafe_allow_html=True)

    # 🔧 Store raw values in history
    st.session_state.history.append({
        "question": user_input,
        "answer": answer,
        "query": query_str
    })

# --- Show Historical Q&A ---
if st.session_state.history:
    st.markdown("### 🕑 Previous Questions, Answers & Queries")
    for entry in reversed(st.session_state.history):
        st.markdown(f"""
        <div class="history-box">
            <div class="question"><strong>Q:</strong> {html.escape(entry['question'])}</div>
            <div class="answer-box"><strong>A:</strong> {html.escape(entry['answer'])}</div>
            <div style="font-size: 0.85em; color: #666;">
                Query: <code>{html.escape(entry['query'])}</code>
            </div>
        </div>
        """, unsafe_allow_html=True)
