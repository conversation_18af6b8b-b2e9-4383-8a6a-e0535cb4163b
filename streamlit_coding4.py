import streamlit as st
from pymongo import MongoClient
import re

# MongoDB Config
MONGO_URI = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
DB_NAME = "Cat2"
NOTES_COLLECTION = "Doctors_Notes"
FLAGS_COLLECTION = "Coding_Flags"

# Connect to MongoDB
client = MongoClient(MONGO_URI)
db = client[DB_NAME]
notes_col = db[NOTES_COLLECTION]
flags_col = db[FLAGS_COLLECTION]

# Simple keyword-based code suggestion engine
SIMPLE_CODE_MAP = {
    "diabetes": "E11.9",
    "hypertension": "I10",
    "chest pain": "R07.9",
    "fever": "R50.9",
    "shortness of breath": "R06.02",
    "asthma": "J45.909",
    "back pain": "M54.5"
}

def suggest_code(note_text):
    for keyword, code in SIMPLE_CODE_MAP.items():
        if re.search(rf"\b{keyword}\b", note_text, re.IGNORECASE):
            return {
                "suggestedCode": code,
                "confidence": "0.85",
                "reason": f"Matched keyword: '{keyword}'",
                "source": "Simple NLP Engine"
            }
    return None

def highlight_keywords(text):
    for keyword in SIMPLE_CODE_MAP:
        text = re.sub(
            rf"(?i)\b({keyword})\b",
            r"**🟡\1**",
            text
        )
    return text

# --- UI Starts Here ---
st.markdown("<h1 style='color:#333;'>🩺 Clinical Coding Flag Explorer</h1>", unsafe_allow_html=True)

# Sidebar Metrics
st.sidebar.header("📊 System Metrics")
total_flags = flags_col.count_documents({})
st.sidebar.metric("Stored Coding Flags", total_flags)

# Flagging Mode
mode = st.radio("🛠️ Flagging Mode", ["Stored", "Live (NLP)"])

# Fetch encounter IDs and label them
all_ids = sorted(notes_col.distinct("encounterId"))

if mode == "Stored":
    flagged_ids = set(flags_col.distinct("encounterId"))
    labeled_ids = [
        (f"{eid} ✅" if eid in flagged_ids else f"{eid} ❌", eid)
        for eid in all_ids
    ]
else:
    labeled_ids = [(eid, eid) for eid in all_ids]

label_options = [label for label, _ in labeled_ids]
selected_label = st.selectbox("🔍 Select Encounter ID", label_options)
selected_encounter = dict(labeled_ids)[selected_label]

# Doctor's Note Section
note = notes_col.find_one({"encounterId": selected_encounter})

st.markdown("---")
with st.container():
    st.subheader("📝 Doctor's Note")
    if note:
        with st.expander("View Full Note", expanded=True):
            note_text = note["text"]
            if mode == "Live (NLP)":
                st.markdown(highlight_keywords(note_text), unsafe_allow_html=True)
            else:
                st.write(note_text)
    else:
        st.warning("No note found for this encounter.")

# Flag Section
st.markdown("---")
with st.container():
    st.subheader("🚩 Coding Flag")
    
    if mode == "Stored":
        flag = flags_col.find_one({"encounterId": selected_encounter})
    elif note:
        flag = suggest_code(note["text"])
    else:
        flag = None

    if flag:
        st.success(f"**Code Suggestion:** `{flag['suggestedCode']}`")
        st.markdown(f"**Confidence:** `{flag['confidence']}`")
        st.markdown(f"**Reason:** 💡 {flag['reason']}")
        st.markdown(f"**Source:** _{flag['source']}_")
    else:
        st.info("⚠️ No coding flag found for this encounter.")

# Footer
st.markdown("---")
st.markdown(
    "<p style='text-align:center; color:gray;'>Made with ❤️ for medical coding teams · Powered by MongoDB + Streamlit</p>",
    unsafe_allow_html=True
)

