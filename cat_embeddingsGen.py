import openai
import json
from tqdm import tqdm
from pymongo import MongoClient

# ====== CONFIG ======
openai.api_key = "***********************************************************************************************"
mongo_uri = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
db_name = "Cat2"
collection_name = "Doctors_Notes"
# ====================

# Connect to MongoDB
client = MongoClient(mongo_uri)
collection = client[db_name][collection_name]

# Find notes that don't yet have embeddings
notes_cursor = collection.find({})

# Generate embeddings and update docs
for note in tqdm(notes_cursor, desc="Embedding and updating"):
    response = openai.Embedding.create(
        input=note["text"],
        model="text-embedding-ada-002"
    )
    embedding = response["data"][0]["embedding"]
    collection.update_one(
        {"_id": note["_id"]},
        {"$set": {"embedding": embedding}}
    )

print("✅ Embedding generation and MongoDB updates complete.")
