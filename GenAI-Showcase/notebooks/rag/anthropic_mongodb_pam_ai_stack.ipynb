{"cells": [{"cell_type": "markdown", "metadata": {"id": "hDasTgMlxZHc"}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/anthropic_mongodb_pam_ai_stack.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/rag_with_claude_opus_mongodb/)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zkePkAfZLQ_R"}, "outputs": [], "source": ["!pip install --quiet pymongo datasets pandas anthropic voyageai"]}, {"cell_type": "markdown", "metadata": {"id": "txDmQ7RR2gWm"}, "source": ["# Set Environment Variables\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "LtmMk2A92d9o"}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"ANTHROPIC_API_KEY\"] = \"\"\n", "ANTHROPIC_API_KEY = os.environ.get(\"ANTHROPIC_API_KEY\")\n", "\n", "os.environ[\"VOYAGE_API_KEY\"] = \"\"\n", "VOYAGE_API_KEY = os.environ.get(\"VOYAGE_API_KEY\")\n", "\n", "os.environ[\"HF_TOKEN\"] = \"\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 81, "referenced_widgets": ["93ba311c6b234a2290aceea9feb78724", "42007e0c1109462e8bd86f63963f2a16", "4d38a495f91b4bcebd8521f352c0c35b", "4799a1a3d6d942f38cd9ceeffdc75785", "0df9f44cdf86468c9ecf366211ea28d3", "615baa098da94d7eb1268e131b2252ca", "3837937686aa4c3ab660dd66b3af0945", "25a8805f3cd642fca27e2131bfdb9c7e", "51ce12d7fe0749dbb09f29e3755b7b3d", "8936e093b6444c68947c238c038c5ecb", "db60c85a7a0c430e979965faef0765bc", "1066eb335f0f4efb906a707286620108", "2f95f48e3481444ebc9cf2ab16aae37a", "273d566230dd4e4493c49e8218713643", "037b066da4e44ac6a98b7c6f7bcc9364", "4c547afc742c404f8264c847a9ea58a1", "c8a7e70a4e6d4a55af052696b42875f8", "e33162fbc46e4d8aba40171f65d9ae51", "1390de2fe16d46f0af5c4c4865c8247c", "322b8870b5274fe69a8d53abf6239798", "117ac3154bac4d4cb37722668ef12fdc", "8e4a721d13b242b2a85cee01f00752cb"]}, "id": "VoY9qBGJNo2F", "outputId": "64525b4a-3728-4ed4-ef49-4957d1eb44e8"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "93ba311c6b234a2290aceea9feb78724", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading readme:   0%|          | 0.00/7.04k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1066eb335f0f4efb906a707286620108", "version_major": 2, "version_minor": 0}, "text/plain": ["Resolving data files:   0%|          | 0/42 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# Make sure you have an Hugging Face token(HF_TOKEN) in your development environemnt before running the code below\n", "# How to get a token: https://huggingface.co/docs/hub/en/security-tokens\n", "\n", "# https://huggingface.co/datasets/MongoDB/tech-news-embeddings\n", "dataset = load_dataset(\"MongoDB/tech-news-embeddings\", split=\"train\", streaming=True)\n", "combined_df = dataset.take(500)\n", "\n", "# Convert the dataset to a pandas dataframe\n", "combined_df = pd.DataFrame(combined_df)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 380}, "id": "GEx4i1ehRliI", "outputId": "d4288352-db7a-4d97-b76d-1e935ca1eaa3"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"combined_df\",\n  \"rows\": 500,\n  \"fields\": [\n    {\n      \"column\": \"companyName\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"10Clouds\",\n          \"01Synergy\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"companyUrl\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"https://hackernoon.com/company/10clouds\",\n          \"https://hackernoon.com/company/01synergy\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"published_at\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 416,\n        \"samples\": [\n          \"2023-04-01 04:00:00\",\n          \"2023-04-03 01:04:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"url\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 403,\n        \"samples\": [\n          \"https://www.waketech.edu/programs-courses/credit/credit-programs/human-services-technology/degrees-programs/a4538c\",\n          \"https://www.businesswire.com/news/home/<USER>/en/\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 390,\n        \"samples\": [\n          \"K2 Joins Salas O\\u2019Brien Expands Technology and Acoustics Services\",\n          \"Information Technology and Telecoms\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"main_image\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 71,\n        \"samples\": [\n          \"https://www.bing.com/th?id=OVFT.E_Ci1nwQFC92529qB8yreC&pid=News\",\n          \"https://firebasestorage.googleapis.com/v0/b/hackernoon-app.appspot.com/o/images%2Fimageedit_25_7084755369.gif?alt=media&token=ca7527b0-a214-46d4-af72-1062b3df1458\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"description\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 409,\n        \"samples\": [\n          \"and fiber internet services to small and medium sized businesses (20-500 seats) throughout the Midwest and Western United States. For more information please visit www.itsasap.com. About Tower Arch Capital Headquartered in Salt Lake City Utah ...\",\n          \"Applications specific services and practicing sound digital hygiene all contribute to the ease with which malicious entities can access your information and determine the extent of data accessible for potential theft. Think of data broker sites as ...\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "combined_df"}, "text/html": ["\n", "  <div id=\"df-504d39a0-c59c-4646-92f5-8ecce3da7f22\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>companyName</th>\n", "      <th>companyUrl</th>\n", "      <th>published_at</th>\n", "      <th>url</th>\n", "      <th>title</th>\n", "      <th>main_image</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-16 02:09:00</td>\n", "      <td>https://www.businesswire.com/news/home/<USER>/td>\n", "      <td><PERSON><PERSON><PERSON> and Sineng Electric Spearhead the Devel...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>(Nasdaq: ON) a leader in intelligent power and...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-02 00:07:00</td>\n", "      <td>https://elkodaily.com/news/local/adobe-student...</td>\n", "      <td>Adobe student receives national Information an...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td><PERSON><PERSON><PERSON> — An eighth grader at Adobe Middle School...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-01 22:22:00</td>\n", "      <td>https://www.aei.org/technology-and-innovation/...</td>\n", "      <td>Modernizing State Services: Harnessing Technol...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>To deliver 21st-century government services Go...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-02 13:12:00</td>\n", "      <td>https://www.crn.com/news/managed-services/terr...</td>\n", "      <td><PERSON> On Why He Left AMD GreenPages...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>In February GreenPages acquired Toronto-based ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-15 20:01:00</td>\n", "      <td>https://www.benzinga.com/pressreleases/23/05/3...</td>\n", "      <td>Synex Renewable Energy Corporation (Formerly S...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>The conference will bring together growth orie...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-504d39a0-c59c-4646-92f5-8ecce3da7f22')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -***********\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-504d39a0-c59c-4646-92f5-8ecce3da7f22 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-504d39a0-c59c-4646-92f5-8ecce3da7f22');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-a71b0cd3-6e6f-4552-bdb7-47305a60ee11\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-a71b0cd3-6e6f-4552-bdb7-47305a60ee11')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-a71b0cd3-6e6f-4552-bdb7-47305a60ee11 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["  companyName                                companyUrl         published_at  \\\n", "0   01Synergy  https://hackernoon.com/company/01synergy  2023-05-16 02:09:00   \n", "1   01Synergy  https://hackernoon.com/company/01synergy  2023-05-02 00:07:00   \n", "2   01Synergy  https://hackernoon.com/company/01synergy  2023-05-01 22:22:00   \n", "3   01Synergy  https://hackernoon.com/company/01synergy  2023-05-02 13:12:00   \n", "4   01Synergy  https://hackernoon.com/company/01synergy  2023-05-15 20:01:00   \n", "\n", "                                                 url  \\\n", "0  https://www.businesswire.com/news/home/<USER>\n", "1  https://elkodaily.com/news/local/adobe-student...   \n", "2  https://www.aei.org/technology-and-innovation/...   \n", "3  https://www.crn.com/news/managed-services/terr...   \n", "4  https://www.benzinga.com/pressreleases/23/05/3...   \n", "\n", "                                               title  \\\n", "0  onsemi and Sineng Electric Spearhead the Devel...   \n", "1  Adobe student receives national Information an...   \n", "2  Modernizing State Services: Harnessing Technol...   \n", "3  <PERSON> On Why He Left AMD GreenPages...   \n", "4  Synex Renewable Energy Corporation (Formerly S...   \n", "\n", "                                          main_image  \\\n", "0  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "1  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "2  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "3  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "4  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "\n", "                                         description  \n", "0  (Nasdaq: ON) a leader in intelligent power and...  \n", "1  EL<PERSON> — An eighth grader at Adobe Middle School...  \n", "2  To deliver 21st-century government services Go...  \n", "3  In February GreenPages acquired Toronto-based ...  \n", "4  The conference will bring together growth orie...  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Remove the _id column from the initial dataset\n", "# We do this because MongoDB will automatically generate new unique _id fields\n", "# when inserting documents. Removing existing _id values ensures that MongoDB\n", "# creates fresh, unique identifiers for each document, avoiding potential\n", "# conflicts with pre-existing IDs and maintaining data integrity in the database.\n", "combined_df = combined_df.drop(columns=[\"_id\"])\n", "\n", "# Remove the initial embedding coloumn as we are going to create new embeddings with VoyageAI embedding model\n", "combined_df = combined_df.drop(columns=[\"embedding\"])\n", "\n", "combined_df.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 590}, "id": "yU2Od28s2Kyg", "outputId": "dbbda131-0db1-46b8-9dae-bc7f02b38d4b"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"combined_df\",\n  \"rows\": 500,\n  \"fields\": [\n    {\n      \"column\": \"companyName\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"10Clouds\",\n          \"01Synergy\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"companyUrl\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"https://hackernoon.com/company/10clouds\",\n          \"https://hackernoon.com/company/01synergy\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"published_at\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 416,\n        \"samples\": [\n          \"2023-04-01 04:00:00\",\n          \"2023-04-03 01:04:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"url\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 403,\n        \"samples\": [\n          \"https://www.waketech.edu/programs-courses/credit/credit-programs/human-services-technology/degrees-programs/a4538c\",\n          \"https://www.businesswire.com/news/home/<USER>/en/\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 390,\n        \"samples\": [\n          \"K2 Joins Salas O\\u2019Brien Expands Technology and Acoustics Services\",\n          \"Information Technology and Telecoms\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"main_image\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 71,\n        \"samples\": [\n          \"https://www.bing.com/th?id=OVFT.E_Ci1nwQFC92529qB8yreC&pid=News\",\n          \"https://firebasestorage.googleapis.com/v0/b/hackernoon-app.appspot.com/o/images%2Fimageedit_25_7084755369.gif?alt=media&token=ca7527b0-a214-46d4-af72-1062b3df1458\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"description\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 409,\n        \"samples\": [\n          \"and fiber internet services to small and medium sized businesses (20-500 seats) throughout the Midwest and Western United States. For more information please visit www.itsasap.com. About Tower Arch Capital Headquartered in Salt Lake City Utah ...\",\n          \"Applications specific services and practicing sound digital hygiene all contribute to the ease with which malicious entities can access your information and determine the extent of data accessible for potential theft. Think of data broker sites as ...\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "combined_df"}, "text/html": ["\n", "  <div id=\"df-cfd16107-f993-4630-a021-6d379d8c2823\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>companyName</th>\n", "      <th>companyUrl</th>\n", "      <th>published_at</th>\n", "      <th>url</th>\n", "      <th>title</th>\n", "      <th>main_image</th>\n", "      <th>description</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-16 02:09:00</td>\n", "      <td>https://www.businesswire.com/news/home/<USER>/td>\n", "      <td><PERSON><PERSON><PERSON> and Sineng Electric Spearhead the Devel...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>(Nasdaq: ON) a leader in intelligent power and...</td>\n", "      <td>[0.01778620295226574, 0.010809645056724548, 0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-02 00:07:00</td>\n", "      <td>https://elkodaily.com/news/local/adobe-student...</td>\n", "      <td>Adobe student receives national Information an...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td><PERSON><PERSON><PERSON> — An eighth grader at Adobe Middle School...</td>\n", "      <td>[0.013895523734390736, 0.004011738579720259, 0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-01 22:22:00</td>\n", "      <td>https://www.aei.org/technology-and-innovation/...</td>\n", "      <td>Modernizing State Services: Harnessing Technol...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>To deliver 21st-century government services Go...</td>\n", "      <td>[-0.01352707389742136, -0.0019957569893449545,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-02 13:12:00</td>\n", "      <td>https://www.crn.com/news/managed-services/terr...</td>\n", "      <td><PERSON> On Why He Left AMD GreenPages...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>In February GreenPages acquired Toronto-based ...</td>\n", "      <td>[0.008943582884967327, -0.016207382082939148, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01Synergy</td>\n", "      <td>https://hackernoon.com/company/01synergy</td>\n", "      <td>2023-05-15 20:01:00</td>\n", "      <td>https://www.benzinga.com/pressreleases/23/05/3...</td>\n", "      <td>Synex Renewable Energy Corporation (Formerly S...</td>\n", "      <td>https://firebasestorage.googleapis.com/v0/b/ha...</td>\n", "      <td>The conference will bring together growth orie...</td>\n", "      <td>[0.02538326568901539, 0.0015419272240251303, 0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-cfd16107-f993-4630-a021-6d379d8c2823')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -***********\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-cfd16107-f993-4630-a021-6d379d8c2823 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-cfd16107-f993-4630-a021-6d379d8c2823');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-4d51af6e-60a5-4405-9286-b07a9e32c691\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-4d51af6e-60a5-4405-9286-b07a9e32c691')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-4d51af6e-60a5-4405-9286-b07a9e32c691 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["  companyName                                companyUrl         published_at  \\\n", "0   01Synergy  https://hackernoon.com/company/01synergy  2023-05-16 02:09:00   \n", "1   01Synergy  https://hackernoon.com/company/01synergy  2023-05-02 00:07:00   \n", "2   01Synergy  https://hackernoon.com/company/01synergy  2023-05-01 22:22:00   \n", "3   01Synergy  https://hackernoon.com/company/01synergy  2023-05-02 13:12:00   \n", "4   01Synergy  https://hackernoon.com/company/01synergy  2023-05-15 20:01:00   \n", "\n", "                                                 url  \\\n", "0  https://www.businesswire.com/news/home/<USER>\n", "1  https://elkodaily.com/news/local/adobe-student...   \n", "2  https://www.aei.org/technology-and-innovation/...   \n", "3  https://www.crn.com/news/managed-services/terr...   \n", "4  https://www.benzinga.com/pressreleases/23/05/3...   \n", "\n", "                                               title  \\\n", "0  onsemi and Sineng Electric Spearhead the Devel...   \n", "1  Adobe student receives national Information an...   \n", "2  Modernizing State Services: Harnessing Technol...   \n", "3  <PERSON> On Why He Left AMD GreenPages...   \n", "4  Synex Renewable Energy Corporation (Formerly S...   \n", "\n", "                                          main_image  \\\n", "0  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "1  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "2  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "3  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "4  https://firebasestorage.googleapis.com/v0/b/ha...   \n", "\n", "                                         description  \\\n", "0  (Nasdaq: ON) a leader in intelligent power and...   \n", "1  EL<PERSON> — An eighth grader at Adobe Middle School...   \n", "2  To deliver 21st-century government services Go...   \n", "3  In February GreenPages acquired Toronto-based ...   \n", "4  The conference will bring together growth orie...   \n", "\n", "                                           embedding  \n", "0  [0.01778620295226574, 0.010809645056724548, 0....  \n", "1  [0.013895523734390736, 0.004011738579720259, 0...  \n", "2  [-0.01352707389742136, -0.0019957569893449545,...  \n", "3  [0.008943582884967327, -0.016207382082939148, ...  \n", "4  [0.02538326568901539, 0.0015419272240251303, 0...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import voyageai\n", "\n", "vo = voyageai.Client(api_key=VOYAGE_API_KEY)\n", "\n", "\n", "def get_embedding(text: str) -> list[float]:\n", "    if not text.strip():\n", "        print(\"Attempted to get embedding for empty text.\")\n", "        return []\n", "\n", "    embedding = vo.embed(text, model=\"voyage-large-2\", input_type=\"document\")\n", "\n", "    return embedding.embeddings[0]\n", "\n", "\n", "combined_df[\"embedding\"] = combined_df[\"description\"].apply(get_embedding)\n", "\n", "combined_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "GXv9MrqLSZw8"}, "source": ["Create Database and Collection\n", "Create Vector Search Index"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "67LE7qj_4wnL"}, "outputs": [], "source": ["os.environ[\"MONGO_URI\"] = \"\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Yv0dMtWnQhDy", "outputId": "d2ca8f1e-1a53-459e-c57c-0c665e734c6b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["import pymongo\n", "\n", "\n", "def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish and validate connection to the MongoDB.\"\"\"\n", "\n", "    client = pymongo.MongoClient(\n", "        mongo_uri, appname=\"devrel.showcase.anthropic_rag.python\"\n", "    )\n", "\n", "    # Validate the connection\n", "    ping_result = client.admin.command(\"ping\")\n", "    if ping_result.get(\"ok\") == 1.0:\n", "        # Connection successful\n", "        print(\"Connection to MongoDB successful\")\n", "        return client\n", "    print(\"Connection to MongoDB failed\")\n", "    return None\n", "\n", "\n", "mongo_uri = os.environ[\"MONGO_URI\"]\n", "\n", "if not mongo_uri:\n", "    print(\"MONGO_URI not set in environment variables\")\n", "\n", "mongo_client = get_mongo_client(mongo_uri)\n", "\n", "DB_NAME = \"knowledge\"\n", "COLLECTION_NAME = \"research_papers\"\n", "\n", "db = mongo_client.get_database(DB_NAME)\n", "collection = db.get_collection(COLLECTION_NAME)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4NowJ2mlQrfT", "outputId": "cc98c49d-43ee-485c-8dae-a47f166b4f41"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 4000, 'electionId': ObjectId('7fffffff000000000000002b'), 'opTime': {'ts': Timestamp(1721843443, 1788), 't': 43}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1721843443, 1788), 'signature': {'hash': b'3\\xc9e\\r\\xf3\\x0f\\xc1\\x9d\\xee\\x04J(\\xa2\\xd0\\xabZW\\x19l\\x88', 'keyId': 7353740577831124994}}, 'operationTime': Timestamp(1721843443, 1788)}, acknowledged=True)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# To ensure we are working with a fresh collection\n", "# delete any existing records in the collection\n", "collection.delete_many({})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tdFsJXXRjEZe"}, "outputs": [], "source": ["# Data Ingestion\n", "combined_df_json = combined_df.to_dict(orient=\"records\")\n", "collection.insert_many(combined_df_json)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "QsSBrAu4nSJC"}, "outputs": [], "source": ["def vector_search(user_query, collection):\n", "    \"\"\"\n", "    Perform a vector search in the MongoDB collection based on the user query.\n", "\n", "    Args:\n", "    user_query (str): The user's query string.\n", "    collection (MongoCollection): The MongoDB collection to search.\n", "\n", "    Returns:\n", "    list: A list of matching documents.\n", "    \"\"\"\n", "\n", "    # Generate embedding for the user query\n", "    query_embedding = get_embedding(user_query)\n", "\n", "    if query_embedding is None:\n", "        return \"Invalid query or embedding generation failed.\"\n", "\n", "    # Define the vector search pipeline\n", "    pipeline = [\n", "        {\n", "            \"$vectorSearch\": {\n", "                \"index\": \"vector_index\",\n", "                \"queryVector\": query_embedding,\n", "                \"path\": \"embedding\",\n", "                \"numCandidates\": 150,  # Number of candidate matches to consider\n", "                \"limit\": 5,  # Return top 5 matches\n", "            }\n", "        },\n", "        {\n", "            \"$project\": {\n", "                \"_id\": 0,  # Exclude the _id field\n", "                \"embedding\": 0,  # Exclude the embedding field\n", "                \"score\": {\n", "                    \"$meta\": \"vectorSearchScore\"  # Include the search score\n", "                },\n", "            }\n", "        },\n", "    ]\n", "\n", "    # Execute the search\n", "    results = collection.aggregate(pipeline)\n", "    return list(results)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "wADfSOFOnw0s"}, "outputs": [], "source": ["import anthropic\n", "\n", "client = anthropic.Client(api_key=ANTHROPIC_API_KEY)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "JrG8Ho50nhVR"}, "outputs": [], "source": ["def handle_user_query(query, collection):\n", "    get_knowledge = vector_search(query, collection)\n", "\n", "    search_result = \"\"\n", "    for result in get_knowledge:\n", "        search_result += (\n", "            f\"Title: {result.get('title', 'N/A')}, \"\n", "            f\"Company Name: {result.get('companyName', 'N/A')}, \"\n", "            f\"Company URL: {result.get('companyUrl', 'N/A')}, \"\n", "            f\"Date Published: {result.get('published_at', 'N/A')}, \"\n", "            f\"Article URL: {result.get('url', 'N/A')}, \"\n", "            f\"Description: {result.get('description', 'N/A')}, \\n\"\n", "        )\n", "\n", "    response = client.messages.create(\n", "        model=\"claude-3-opus-20240229\",\n", "        max_tokens=1024,\n", "        system=\"You are Venture Captital Tech Analyst with access to some tech company articles and information. You use the information you are given to provide advice.\",\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Answer this user query: \"\n", "                + query\n", "                + \" with the following context: \"\n", "                + search_result,\n", "            }\n", "        ],\n", "    )\n", "\n", "    return (response.content[0].text), search_result"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_3OcaBbmoY2H", "outputId": "339e3db1-7a3f-4593-f804-3056a3efc6f1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response: Based on the limited information provided in the article titles and descriptions, it's difficult to recommend a single \"best\" tech stock to invest in. Investing always carries risk, and it's important to do thorough research before making any investment decisions. That said, here are a few thoughts on the companies mentioned:\n", "\n", "01Synergy is listed in a couple of the articles as a top information technology services stock. The IT services sector can be promising, as many businesses rely on these companies for critical tech infrastructure and support. However, without more details on 01Synergy's financials, competitive advantages, growth prospects etc., it's hard to say if it's the best investment.\n", "\n", "The article on Seeking Alpha about ranking IT stocks by value could be insightful - finding stocks that are undervalued by the market can lead to good returns. But the description doesn't specify which stocks scored well.\n", "\n", "The roundup article mentions larger, well-known tech companies like Uber and HP. Large, established companies can provide more stability but may have less growth upside than smaller firms. \n", "\n", "Overall, the information here can provide some ideas for further research, but it's not enough to definitively choose the single best tech stock. I'd recommend looking into the fundamentals, financials and future outlooks of companies that pique your interest, as well as ensuring any investment fits with your overall financial goals and risk tolerance. Diversifying investments across multiple companies and industries is also a wise strategy. Consulting expert sources like financial advisors can provide more tailored guidance as well.\n", "\n", "Source Information: \n", "Title: 12 Best Information Technology Services Stocks to Buy, Company Name: 01Synergy, Company URL: https://hackernoon.com/company/01synergy, Date Published: 2023-05-12 17:46:00, Article URL: https://finance.yahoo.com/news/12-best-information-technology-services-*********.html, Description: In this article we will take a look at the 12 best information technology services stocks to buy. To see more such companies go directly to 5 Best Information Technology Services Stocks to Buy., \n", "Title: Selected Information Technology Stocks Ranked By Value, Company Name: 01Synergy, Company URL: https://hackernoon.com/company/01synergy, Date Published: 2023-04-13 12:07:00, Article URL: https://seekingalpha.com/article/4593865-selected-information-technology-stocks-ranked-by-value, Description: 24 highly rated Information Technology stocks were evaluated by means of a multi-factor value matrix. The value matrix is a broad effort to identify those Information Technology stocks that are ..., \n", "Title: Tech Media & Telecom Roundup: Market Talk, Company Name: 10Clouds, Company URL: https://hackernoon.com/company/10clouds, Date Published: 2023-10-04 09:34:00, Article URL: https://www.wsj.com/finance/investing/tech-media-telecom-roundup-market-talk-22d055db, Description: Uber HP and more in the latest Market Talks covering Technology Media and Telecom., \n", "Title: Best VoIP Services (April 2023), Company Name: 01Synergy, Company URL: https://hackernoon.com/company/01synergy, Date Published: 2023-04-03 11:24:00, Article URL: https://www.forbes.com/advisor/business/software/best-voip-service/, Description: Editorial Note: We earn a commission from partner links on Forbes Advisor. Commissions do not affect our editors' opinions or evaluations. As technology advances and companies adapt to ever ..., \n", "Title: 10 Biggest Technology Companies, Company Name: 01Synergy, Company URL: https://hackernoon.com/company/01synergy, Date Published: 2023-05-23 06:09:00, Article URL: https://www.investopedia.com/articles/markets/030816/worlds-top-10-technology-companies-aapl-googl.asp, Description: They create design and manufacture computers mobile devices and home appliances and provide products and services related to information technology. Below we look at the 10 biggest companies ..., \n", "\n"]}], "source": ["# Conduct query with retrieval of sources\n", "query = \"Give me the best tech stock to invest in and tell me why\"\n", "response, source_information = handle_user_query(query, collection)\n", "\n", "print(f\"Response: {response}\")\n", "print(f\"\\nSource Information: \\n{source_information}\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"037b066da4e44ac6a98b7c6f7bcc9364": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_117ac3154bac4d4cb37722668ef12fdc", "placeholder": "​", "style": "IPY_MODEL_8e4a721d13b242b2a85cee01f00752cb", "value": " 42/42 [00:00&lt;00:00,  9.27it/s]"}}, "0df9f44cdf86468c9ecf366211ea28d3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1066eb335f0f4efb906a707286620108": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2f95f48e3481444ebc9cf2ab16aae37a", "IPY_MODEL_273d566230dd4e4493c49e8218713643", "IPY_MODEL_037b066da4e44ac6a98b7c6f7bcc9364"], "layout": "IPY_MODEL_4c547afc742c404f8264c847a9ea58a1"}}, "117ac3154bac4d4cb37722668ef12fdc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1390de2fe16d46f0af5c4c4865c8247c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "25a8805f3cd642fca27e2131bfdb9c7e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "273d566230dd4e4493c49e8218713643": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1390de2fe16d46f0af5c4c4865c8247c", "max": 42, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_322b8870b5274fe69a8d53abf6239798", "value": 42}}, "2f95f48e3481444ebc9cf2ab16aae37a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c8a7e70a4e6d4a55af052696b42875f8", "placeholder": "​", "style": "IPY_MODEL_e33162fbc46e4d8aba40171f65d9ae51", "value": "Resolving data files: 100%"}}, "322b8870b5274fe69a8d53abf6239798": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3837937686aa4c3ab660dd66b3af0945": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "42007e0c1109462e8bd86f63963f2a16": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_615baa098da94d7eb1268e131b2252ca", "placeholder": "​", "style": "IPY_MODEL_3837937686aa4c3ab660dd66b3af0945", "value": "Downloading readme: 100%"}}, "4799a1a3d6d942f38cd9ceeffdc75785": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8936e093b6444c68947c238c038c5ecb", "placeholder": "​", "style": "IPY_MODEL_db60c85a7a0c430e979965faef0765bc", "value": " 7.04k/7.04k [00:00&lt;00:00, 9.29kB/s]"}}, "4c547afc742c404f8264c847a9ea58a1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d38a495f91b4bcebd8521f352c0c35b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_25a8805f3cd642fca27e2131bfdb9c7e", "max": 7044, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_51ce12d7fe0749dbb09f29e3755b7b3d", "value": 7044}}, "51ce12d7fe0749dbb09f29e3755b7b3d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "615baa098da94d7eb1268e131b2252ca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8936e093b6444c68947c238c038c5ecb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8e4a721d13b242b2a85cee01f00752cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "93ba311c6b234a2290aceea9feb78724": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_42007e0c1109462e8bd86f63963f2a16", "IPY_MODEL_4d38a495f91b4bcebd8521f352c0c35b", "IPY_MODEL_4799a1a3d6d942f38cd9ceeffdc75785"], "layout": "IPY_MODEL_0df9f44cdf86468c9ecf366211ea28d3"}}, "c8a7e70a4e6d4a55af052696b42875f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "db60c85a7a0c430e979965faef0765bc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e33162fbc46e4d8aba40171f65d9ae51": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}