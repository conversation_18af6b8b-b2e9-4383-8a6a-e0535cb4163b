{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_with_hugging_face_gemma_mongodb.ipynb) \n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/gemma-mongodb-huggingface-rag/)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gVSo_nNOUsdn", "outputId": "907f4738-a3b0-4c0f-b293-eff65c665c07"}, "outputs": [], "source": ["!pip install datasets pandas pymongo sentence_transformers\n", "!pip install -U transformers\n", "# Install below if using GPU\n", "!pip install accelerate"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 747}, "id": "5gCzss27UwWw", "outputId": "212cca18-a0d7-4289-bce0-ee6259fc2dba"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1500,\n  \"fields\": [\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134,\n          124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.09038552453906,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 139,\n        \"samples\": [\n          152.0,\n          127.0,\n          96.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.861995960390892,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1368,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BNWE5MzAwMjQtNzI1YS00YjZhLTkxNDItM2JjNjM3ZjI5NzBjXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTgwNjIyNTczMF5BMl5BanBnXkFtZTcwODI5MDkyMQ@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1429,\n        \"samples\": [\n          \"A New York City architect becomes a one-man vigilante squad after his wife is murdered by street punks in which he randomly goes out and kills would-be muggers on the mean streets after dark.\",\n          \"As the daring thief Ars\\u00e8ne Lupin (Duris) ransacks the homes of wealthy Parisians, the police, with a secret weapon in their arsenal, attempt to ferret him out.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot_embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1435,\n        \"samples\": [\n          \"Turbo: A Power Rangers Movie\",\n          \"Neon Genesis Evangelion: Death & Rebirth\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-118a7a23-2c34-4ec1-8ca1-64fc1e6d9cb6\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_mflix_comments</th>\n", "      <th>genres</th>\n", "      <th>countries</th>\n", "      <th>directors</th>\n", "      <th>fullplot</th>\n", "      <th>writers</th>\n", "      <th>awards</th>\n", "      <th>runtime</th>\n", "      <th>type</th>\n", "      <th>rated</th>\n", "      <th>metacritic</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>imdb</th>\n", "      <th>plot</th>\n", "      <th>cast</th>\n", "      <th>plot_embedding</th>\n", "      <th>title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>[Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>199.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>[0.00072939653, -0.026834568, 0.013515796, -0....</td>\n", "      <td>The Perils of Pauline</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>22.0</td>\n", "      <td>movie</td>\n", "      <td>TV-G</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>[-0.022837115, -0.022941574, 0.014937485, -0.0...</td>\n", "      <td>From Hand to Mouth</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>101.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td>[0.00023330493, -0.028511643, 0.014653289, -0....</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>88.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>[-0.005927917, -0.033394486, 0.0015323418, -0....</td>\n", "      <td>The Black Pirate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>58.0</td>\n", "      <td>movie</td>\n", "      <td>PASSED</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>[-0.0059373598, -0.026604708, -0.0070914757, -...</td>\n", "      <td>For Heaven's Sake</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-118a7a23-2c34-4ec1-8ca1-64fc1e6d9cb6')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-118a7a23-2c34-4ec1-8ca1-64fc1e6d9cb6 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-118a7a23-2c34-4ec1-8ca1-64fc1e6d9cb6');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-06d19b20-9726-438e-9b5a-2c46b4402907\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-06d19b20-9726-438e-9b5a-2c46b4402907')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-06d19b20-9726-438e-9b5a-2c46b4402907 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["   num_mflix_comments                      genres countries  \\\n", "0                   0                    [Action]     [USA]   \n", "1                   0     [Comedy, Short, Action]     [USA]   \n", "2                   0  [Action, Adventure, Drama]     [USA]   \n", "3                   1         [Adventure, Action]     [USA]   \n", "4                   0   [Action, Comedy, Romance]     [USA]   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                                             writers  \\\n", "0  [<PERSON> (screenplay), <PERSON>...   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...   \n", "3  [<PERSON> (story), <PERSON> (a...   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.   \n", "\n", "                                              awards  runtime   type   rated  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}    199.0  movie    None   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...     22.0  movie    TV-G   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}    101.0  movie    None   \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}     88.0  movie    None   \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...     58.0  movie  PASSED   \n", "\n", "   metacritic                                             poster  languages  \\\n", "0         NaN  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1         NaN  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2         NaN                                               None  [English]   \n", "3         NaN  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4         NaN  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                          imdb  \\\n", "0    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                                plot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  A penniless young man tries to save an heiress...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  Seeking revenge, an athletic young man joins t...   \n", "4  An irresponsible young millionaire changes his...   \n", "\n", "                                                cast  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...   \n", "2  [<PERSON>, <PERSON>, <PERSON>, A<PERSON><PERSON>.   \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...   \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>..   \n", "\n", "                                      plot_embedding                  title  \n", "0  [0.00072939653, -0.026834568, 0.013515796, -0....  The Perils of Pauline  \n", "1  [-0.022837115, -0.022941574, 0.014937485, -0.0...     From Hand to Mouth  \n", "2  [0.00023330493, -0.028511643, 0.014653289, -0....             <PERSON>  \n", "3  [-0.005927917, -0.033394486, 0.0015323418, -0....       The Black Pirate  \n", "4  [-0.0059373598, -0.026604708, -0.0070914757, -...      For Heaven's Sake  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load Dataset\n", "import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# https://huggingface.co/datasets/MongoDB/embedded_movies\n", "dataset = load_dataset(\"MongoDB/embedded_movies\")\n", "\n", "# Convert the dataset to a pandas dataframe\n", "dataset_df = pd.DataFrame(dataset[\"train\"])\n", "\n", "dataset_df.head(5)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "ARdz6j7SUxqi", "outputId": "c53c458a-512d-4b7e-93b4-514f6de9d497"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Number of missing values in each column after removal:\n", "num_mflix_comments      0\n", "genres                  0\n", "countries               0\n", "directors              12\n", "fullplot                0\n", "writers                13\n", "awards                  0\n", "runtime                14\n", "type                    0\n", "rated                 279\n", "metacritic            893\n", "poster                 78\n", "languages               1\n", "imdb                    0\n", "plot                    0\n", "cast                    1\n", "plot_embedding          1\n", "title                   0\n", "dtype: int64\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1452,\n  \"fields\": [\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134,\n          124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.5693352357647,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 137,\n        \"samples\": [\n          60.0,\n          151.0,\n          110.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.855402595666057,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1332,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BMTQ2NTMxODEyNV5BMl5BanBnXkFtZTcwMDgxMjA0MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTY5OTg1ODk0MV5BMl5BanBnXkFtZTcwMTEwMjU1MQ@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla saves Tokyo from a flying saucer that transforms into the beast Orga.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1391,\n        \"samples\": [\n          \"Superhero Movie\",\n          \"Hooper\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-56c78a25-7af3-48a8-9646-89180429bec7\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_mflix_comments</th>\n", "      <th>genres</th>\n", "      <th>countries</th>\n", "      <th>directors</th>\n", "      <th>fullplot</th>\n", "      <th>writers</th>\n", "      <th>awards</th>\n", "      <th>runtime</th>\n", "      <th>type</th>\n", "      <th>rated</th>\n", "      <th>metacritic</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>imdb</th>\n", "      <th>plot</th>\n", "      <th>cast</th>\n", "      <th>title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>[Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>199.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>The Perils of Pauline</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>22.0</td>\n", "      <td>movie</td>\n", "      <td>TV-G</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>From Hand to Mouth</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>101.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>88.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>The Black Pirate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>58.0</td>\n", "      <td>movie</td>\n", "      <td>PASSED</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>For Heaven's Sake</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-56c78a25-7af3-48a8-9646-89180429bec7')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-56c78a25-7af3-48a8-9646-89180429bec7 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-56c78a25-7af3-48a8-9646-89180429bec7');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-769fc921-3101-4a3d-bed8-418038cd12cf\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-769fc921-3101-4a3d-bed8-418038cd12cf')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-769fc921-3101-4a3d-bed8-418038cd12cf button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["   num_mflix_comments                      genres countries  \\\n", "0                   0                    [Action]     [USA]   \n", "1                   0     [Comedy, Short, Action]     [USA]   \n", "2                   0  [Action, Adventure, Drama]     [USA]   \n", "3                   1         [Adventure, Action]     [USA]   \n", "4                   0   [Action, Comedy, Romance]     [USA]   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                                             writers  \\\n", "0  [<PERSON> (screenplay), <PERSON>...   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...   \n", "3  [<PERSON> (story), <PERSON> (a...   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.   \n", "\n", "                                              awards  runtime   type   rated  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}    199.0  movie    None   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...     22.0  movie    TV-G   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}    101.0  movie    None   \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}     88.0  movie    None   \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...     58.0  movie  PASSED   \n", "\n", "   metacritic                                             poster  languages  \\\n", "0         NaN  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1         NaN  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2         NaN                                               None  [English]   \n", "3         NaN  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4         NaN  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                          imdb  \\\n", "0    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                                plot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  A penniless young man tries to save an heiress...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  Seeking revenge, an athletic young man joins t...   \n", "4  An irresponsible young millionaire changes his...   \n", "\n", "                                                cast                  title  \n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...  The Perils of Pauline  \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     From Hand to Mouth  \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>             <PERSON>  \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>..       The Black Pirate  \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.      For Heaven's Sake  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data Preparation\n", "\n", "# Remove data point where plot coloumn is missing\n", "dataset_df = dataset_df.dropna(subset=[\"fullplot\"])\n", "print(\"\\nNumber of missing values in each column after removal:\")\n", "print(dataset_df.isnull().sum())\n", "\n", "# Remove the plot_embedding from each data point in the dataset as we are going to create new embeddings with an open source embedding model from Hugging Face\n", "dataset_df = dataset_df.drop(columns=[\"plot_embedding\"])\n", "dataset_df.head(5)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 747}, "id": "ZX8zJNN5UzPK", "outputId": "81bc1a57-7d96-4311-ba94-4748c34c20e3"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1452,\n  \"fields\": [\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134,\n          124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.5693352357647,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 137,\n        \"samples\": [\n          60.0,\n          151.0,\n          110.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.855402595666057,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1332,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BMTQ2NTMxODEyNV5BMl5BanBnXkFtZTcwMDgxMjA0MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTY5OTg1ODk0MV5BMl5BanBnXkFtZTcwMTEwMjU1MQ@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla saves Tokyo from a flying saucer that transforms into the beast Orga.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1391,\n        \"samples\": [\n          \"Superhero Movie\",\n          \"Hooper\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-7d414211-23c5-47a5-a236-cd4624c5e770\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_mflix_comments</th>\n", "      <th>genres</th>\n", "      <th>countries</th>\n", "      <th>directors</th>\n", "      <th>fullplot</th>\n", "      <th>writers</th>\n", "      <th>awards</th>\n", "      <th>runtime</th>\n", "      <th>type</th>\n", "      <th>rated</th>\n", "      <th>metacritic</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>imdb</th>\n", "      <th>plot</th>\n", "      <th>cast</th>\n", "      <th>title</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>[Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>199.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>[-0.009285838343203068, -0.005062104668468237,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>22.0</td>\n", "      <td>movie</td>\n", "      <td>TV-G</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>[-0.0024393785279244184, 0.02309592440724373, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>101.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td><PERSON></td>\n", "      <td>[0.012204292230308056, -0.01145575474947691, -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>88.0</td>\n", "      <td>movie</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>The Black Pirate</td>\n", "      <td>[0.004541348200291395, -0.0006100579630583525,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>[USA]</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>58.0</td>\n", "      <td>movie</td>\n", "      <td>PASSED</td>\n", "      <td>NaN</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>For Heaven's Sake</td>\n", "      <td>[-0.0022256041411310434, 0.011567804962396622,...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7d414211-23c5-47a5-a236-cd4624c5e770')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-7d414211-23c5-47a5-a236-cd4624c5e770 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-7d414211-23c5-47a5-a236-cd4624c5e770');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-c25e49ae-d01e-4541-bdcb-96d7c7e7b067\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-c25e49ae-d01e-4541-bdcb-96d7c7e7b067')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-c25e49ae-d01e-4541-bdcb-96d7c7e7b067 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["   num_mflix_comments                      genres countries  \\\n", "0                   0                    [Action]     [USA]   \n", "1                   0     [Comedy, Short, Action]     [USA]   \n", "2                   0  [Action, Adventure, Drama]     [USA]   \n", "3                   1         [Adventure, Action]     [USA]   \n", "4                   0   [Action, Comedy, Romance]     [USA]   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                                             writers  \\\n", "0  [<PERSON> (screenplay), <PERSON>...   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...   \n", "3  [<PERSON> (story), <PERSON> (a...   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.   \n", "\n", "                                              awards  runtime   type   rated  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}    199.0  movie    None   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...     22.0  movie    TV-G   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}    101.0  movie    None   \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}     88.0  movie    None   \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...     58.0  movie  PASSED   \n", "\n", "   metacritic                                             poster  languages  \\\n", "0         NaN  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1         NaN  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2         NaN                                               None  [English]   \n", "3         NaN  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4         NaN  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                          imdb  \\\n", "0    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                                plot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  A penniless young man tries to save an heiress...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  Seeking revenge, an athletic young man joins t...   \n", "4  An irresponsible young millionaire changes his...   \n", "\n", "                                                cast                  title  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...  The Perils of Pauline   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     From Hand to Mouth   \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>             <PERSON>   \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>..       The Black Pirate   \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.      For Heaven's Sake   \n", "\n", "                                           embedding  \n", "0  [-0.009285838343203068, -0.005062104668468237,...  \n", "1  [-0.0024393785279244184, 0.02309592440724373, ...  \n", "2  [0.012204292230308056, -0.01145575474947691, -...  \n", "3  [0.004541348200291395, -0.0006100579630583525,...  \n", "4  [-0.0022256041411310434, 0.011567804962396622,...  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from sentence_transformers import SentenceTransformer\n", "\n", "# https://huggingface.co/thenlper/gte-large\n", "embedding_model = SentenceTransformer(\"thenlper/gte-large\")\n", "\n", "\n", "def get_embedding(text: str) -> list[float]:\n", "    if not text.strip():\n", "        print(\"Attempted to get embedding for empty text.\")\n", "        return []\n", "\n", "    embedding = embedding_model.encode(text)\n", "\n", "    return embedding.tolist()\n", "\n", "\n", "dataset_df[\"embedding\"] = dataset_df[\"fullplot\"].apply(get_embedding)\n", "\n", "dataset_df.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Oi0l9POtU0iP", "outputId": "d3fe3cc4-8c08-4435-ddfc-8cfcc5ada572"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["import pymongo\n", "from google.colab import userdata\n", "\n", "\n", "def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish connection to the MongoDB.\"\"\"\n", "    try:\n", "        client = pymongo.MongoClient(\n", "            mongo_uri, appname=\"devrel.showcase.rag_huggingface_gemma\"\n", "        )\n", "        print(\"Connection to MongoDB successful\")\n", "        return client\n", "    except pymongo.errors.ConnectionFailure as e:\n", "        print(f\"Connection failed: {e}\")\n", "        return None\n", "\n", "\n", "mongo_uri = userdata.get(\"MONGO_URI\")\n", "if not mongo_uri:\n", "    print(\"MONGO_URI not set in environment variables\")\n", "\n", "mongo_client = get_mongo_client(mongo_uri)\n", "\n", "# Ingest data into MongoDB\n", "db = mongo_client[\"movies\"]\n", "collection = db[\"movie_collection_2\"]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "F7XXXa-OU1u9", "outputId": "7bd1eb43-e933-4150-990a-fa20bad84e9a"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 1452, 'electionId': ObjectId('7fffffff000000000000000c'), 'opTime': {'ts': Timestamp(1708554945, 1452), 't': 12}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1708554945, 1452), 'signature': {'hash': b'\\x99\\x89\\xc0\\x00Cn!\\xd6\\xaf\\xb3\\x96\\xdf\\xc3\\xda\\x88\\x11\\xf5\\t\\xbd\\xc0', 'keyId': 7320226449804230661}}, 'operationTime': Timestamp(1708554945, 1452)}, acknowledged=True)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Delete any existing records in the collection\n", "collection.delete_many({})"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XrfMY4QBU2-l", "outputId": "e2b5c534-2ba0-4ffa-bca8-1e96bef14c54"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data ingestion into MongoDB completed\n"]}], "source": ["documents = dataset_df.to_dict(\"records\")\n", "collection.insert_many(documents)\n", "\n", "print(\"Data ingestion into MongoDB completed\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "kWucnQBEU35k"}, "outputs": [], "source": ["def vector_search(user_query, collection):\n", "    \"\"\"\n", "    Perform a vector search in the MongoDB collection based on the user query.\n", "\n", "    Args:\n", "    user_query (str): The user's query string.\n", "    collection (MongoCollection): The MongoDB collection to search.\n", "\n", "    Returns:\n", "    list: A list of matching documents.\n", "    \"\"\"\n", "\n", "    # Generate embedding for the user query\n", "    query_embedding = get_embedding(user_query)\n", "\n", "    if query_embedding is None:\n", "        return \"Invalid query or embedding generation failed.\"\n", "\n", "    # Define the vector search pipeline\n", "    vector_search_stage = {\n", "        \"$vectorSearch\": {\n", "            \"index\": \"vector_index\",\n", "            \"queryVector\": query_embedding,\n", "            \"path\": \"embedding\",\n", "            \"numCandidates\": 150,  # Number of candidate matches to consider\n", "            \"limit\": 4,  # Return top 4 matches\n", "        }\n", "    }\n", "\n", "    unset_stage = {\n", "        \"$unset\": \"embedding\"  # Exclude the 'embedding' field from the results\n", "    }\n", "\n", "    project_stage = {\n", "        \"$project\": {\n", "            \"_id\": 0,  # Exclude the _id field\n", "            \"fullplot\": 1,  # Include the plot field\n", "            \"title\": 1,  # Include the title field\n", "            \"genres\": 1,  # Include the genres field\n", "            \"score\": {\"$meta\": \"vectorSearchScore\"},  # Include the search score\n", "        }\n", "    }\n", "\n", "    pipeline = [vector_search_stage, unset_stage, project_stage]\n", "\n", "    # Execute the search\n", "    results = collection.aggregate(pipeline)\n", "    return list(results)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "0ka4WLTmU5L4"}, "outputs": [], "source": ["def get_search_result(query, collection):\n", "    get_knowledge = vector_search(query, collection)\n", "\n", "    search_result = \"\"\n", "    for result in get_knowledge:\n", "        search_result += f\"Title: {result.get('title', 'N/A')}, Plot: {result.get('fullplot', 'N/A')}\\n\"\n", "\n", "    return search_result"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Z4L4SfueU6PY", "outputId": "11ea30ca-8cac-4e4c-9ab6-780e043c6345"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: What is the best romantic movie to watch and why?\n", "Continue to answer the query by using the Search Results:\n", "Title: Shut Up and Kiss Me!, Plot: <PERSON> and <PERSON> are 27-year old best friends in Miami, born on the same day and each searching for the perfect woman. <PERSON> is a rookie stockbroker living with his psychic <PERSON>. <PERSON> is a slick surfer dude yet to find commitment. Each meets the women of their dreams on the same day. <PERSON> knocks heads in an elevator with the gorgeous <PERSON>, passing out before getting her number. <PERSON> falls for the insatiable <PERSON><PERSON>, but <PERSON><PERSON>'s uncle is mob boss <PERSON>, charged with her protection. This high-energy romantic comedy asks to what extent will you go for true love?\n", "Title: Pearl Harbor, Plot: Pearl Harbor is a classic tale of romance set during a war that complicates everything. It all starts when childhood friends <PERSON> and <PERSON> become Army Air Corps pilots and meet <PERSON>, a Navy nurse. <PERSON> falls head over heels and next thing you know <PERSON> and <PERSON> are hooking up. Then <PERSON> volunteers to go fight in Britain and <PERSON> and <PERSON> get transferred to Pearl Harbor. While <PERSON> is off fighting everything gets completely whack and next thing you know everybody is in the middle of an air raid we now know as \"Pearl Harbor.\"\n", "Title: Titanic, Plot: The plot focuses on the romances of two couples upon the doomed ship's maiden voyage. <PERSON> (<PERSON>) is a wealthy woman mourning the loss of her aunt, who reignites a romance with former flame <PERSON><PERSON> (<PERSON>). Meanwhile, a charming ne'er-do-well named <PERSON> (<PERSON>) steals a ticket for the ship, and falls for a sweet innocent Irish girl on board. But their romance is threatened by the villainous <PERSON> (<PERSON>), who has discovered about the ticket and makes <PERSON> his unwilling accomplice, as well as having sinister plans for the girl.\n", "Title: China Girl, Plot: A modern day Romeo & Juliet story is told in New York when an Italian boy and a Chinese girl become lovers, causing a tragic conflict between ethnic gangs.\n", ".\n"]}], "source": ["# Conduct query with retrival of sources\n", "query = \"What is the best romantic movie to watch and why?\"\n", "source_information = get_search_result(query, collection)\n", "combined_information = f\"Query: {query}\\nContinue to answer the query by using the Search Results:\\n{source_information}.\"\n", "\n", "print(combined_information)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 209, "referenced_widgets": ["60c4d6d5e7a84fa493f101cc47dadef9", "fa0c528cca744cff8da0a4fa21fdb4b5", "d7d4a9f444fe4ebb9135035e2166a3a5", "4e62b9ec821348cc94b34cfbc010c2a4", "9d9a247c6569458abd0dcd6e0d717079", "e5a6d300bbf441b8904aa9afb89e6f31", "88226abe35534278bbd427d8eff0f5f8", "2e081a17ddc04104882893a30c902265", "938f8f60901442f2902eb51e86c27961", "ce6a3a655d2f4ce2ab351c766568bed5", "19fd13ad5b2740aa8be2a7d62488fdaf", "c7c0c34a71954d6ea976c774573c49c5", "0d6ec3bab579406fa4e6fc2b3d6b6998", "a37f2164e11d4e5f851a4a09a12c663c", "ef32431228f24a5498810a36b9cf6506", "c06e354cb8294e66a3d7590a576571e0", "e2998c2c6b1f4d489a5e39f2076838e4", "4300755179d9465db871b14ae78dabc6", "f14106c7f60f411199acf47f530443fd", "bf88ee6dc83648d8a61d75bb4466b1e3", "5776e818d9d34e009f95833056522876", "06b1a069317041c8a9174c14fdc867bc", "0e27bfa4f64f427d9996de0451e9edd9", "d5e9f339fe7e4ab9955531cc125f071e", "fa9cf3e72280417d8711ef7227a95d34", "c3a1b520140444fbb40b7ac789f7ac0e", "2c84bc5c158641f49f421a7d28da1518", "6a15f1cf54a141fc9d6bb790366c6bdd", "8813b56cd89744b58ace2787206e1501", "edc37210db734d01a8afce596698bb27", "eba6048eb694485693656fcbf4a4f297", "30885be6a7c84f0f9f02bc2ea11679bc", "29178e51df9e47489fff623763b130ed", "5266bebcf8bb4b0798b14831a38e2a8c", "7c638aaf734c423fbe54daddff97040f", "4c6736981923464db2f754339c60cd0d", "57383c03fc854a92a2ff732cbdd80a70", "8a302ae0412b4393a17b50861afe36b5", "b2fdc502d6ee491bb826fd616e35d407", "2677891ce891475b8dc7c2ae287c58d7", "fddbae43ce7f477cafaff89b81e47fc7", "592d069be51e43f99212d47be9c11dcf", "9a4c90a767c746659ea535d7c36d40a5", "43fcf04b360f4a75be9fb99ab69fbe38", "b7c439aa6d584c5784b46980050e503d", "8aa8805651d34181b1851d302ccc47e2", "713f1d91e445411288e565a33ce4b271", "55941e08c602404c9342d00b7ee26918", "87da02f5606d404ea242c3bd1f9ac38c", "947f9b7e19dc4be4bd21b1b021e91f9d", "0b7f3d233b8f4912bef4deae2e395001", "6ccbd7b9ae924b5e843efd3114dfb2c5", "9e0bccbc6072461fbf96482b870ed8d5", "d7a00f1f114e4f008f4d5a48c1c69f53", "faf25fd219f24bdbaa2e3202548c97d9", "a0996675df13484aaa519e6ff45c5476", "0bfb4937ed5547b3ba464ca47ac77f1a", "7f59906980724a8b840dec85ce400f89", "80f3d29327bf429481ad191b1abe556f", "6d7c024126ac4c34825fae522234ebca", "a0600fb407034c2d8df6ae5830d601db", "c1d37ab1952b4d268d9786b74b6902d7", "e7f471604a5a42e095d35d8ad399c6fe", "feb438afda6b4c148a3a62ee7e03da74", "e68cf53b04a845ac9d6f4047600ebc21", "33fef11f829f49e2aa9555201d4a0e42"]}, "id": "OYGmKVv9mm8g", "outputId": "ff41bfed-daa0-4ed8-8cc4-0aa138e697a1"}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\")\n", "# CPU Enabled uncomment below 👇🏽\n", "# model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\")\n", "# GPU Enabled use below 👇🏽\n", "model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\", device_map=\"auto\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wDA9SdXhsFyM", "outputId": "c3300fa5-586c-48bd-9abb-b12a4390a294"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bos>Query: What is the best romantic movie to watch and why?\n", "Continue to answer the query by using the Search Results:\n", "Title: Shut Up and Kiss Me!, Plot: <PERSON> and <PERSON> are 27-year old best friends in Miami, born on the same day and each searching for the perfect woman. <PERSON> is a rookie stockbroker living with his psychic <PERSON>. <PERSON> is a slick surfer dude yet to find commitment. Each meets the women of their dreams on the same day. <PERSON> knocks heads in an elevator with the gorgeous <PERSON>, passing out before getting her number. <PERSON> falls for the insatiable <PERSON><PERSON>, but <PERSON><PERSON>'s uncle is mob boss <PERSON>, charged with her protection. This high-energy romantic comedy asks to what extent will you go for true love?\n", "Title: Pearl Harbor, Plot: Pearl Harbor is a classic tale of romance set during a war that complicates everything. It all starts when childhood friends <PERSON> and <PERSON> become Army Air Corps pilots and meet <PERSON>, a Navy nurse. <PERSON> falls head over heels and next thing you know <PERSON> and <PERSON> are hooking up. Then <PERSON> volunteers to go fight in Britain and <PERSON> and <PERSON> get transferred to Pearl Harbor. While <PERSON> is off fighting everything gets completely whack and next thing you know everybody is in the middle of an air raid we now know as \"Pearl Harbor.\"\n", "Title: Titanic, Plot: The plot focuses on the romances of two couples upon the doomed ship's maiden voyage. <PERSON> (<PERSON>) is a wealthy woman mourning the loss of her aunt, who reignites a romance with former flame <PERSON><PERSON> (<PERSON>). Meanwhile, a charming ne'er-do-well named <PERSON> (<PERSON>) steals a ticket for the ship, and falls for a sweet innocent Irish girl on board. But their romance is threatened by the villainous <PERSON> (<PERSON>), who has discovered about the ticket and makes <PERSON> his unwilling accomplice, as well as having sinister plans for the girl.\n", "Title: China Girl, Plot: A modern day Romeo & Juliet story is told in New York when an Italian boy and a Chinese girl become lovers, causing a tragic conflict between ethnic gangs.\n", ".\n", "\n", "Based on the search results, the best romantic movie to watch is **Shut Up and Kiss Me!** because it is a romantic comedy that explores the complexities of love and relationships. The movie is funny, heartwarming, and thought-provoking.<eos>\n"]}], "source": ["# Moving tensors to GPU\n", "input_ids = tokenizer(combined_information, return_tensors=\"pt\").to(\"cuda\")\n", "response = model.generate(**input_ids, max_new_tokens=500)\n", "print(tokenizer.decode(response[0]))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "FhMmFmUBwBcy"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "A100", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"06b1a069317041c8a9174c14fdc867bc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0b7f3d233b8f4912bef4deae2e395001": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0bfb4937ed5547b3ba464ca47ac77f1a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a0600fb407034c2d8df6ae5830d601db", "placeholder": "​", "style": "IPY_MODEL_c1d37ab1952b4d268d9786b74b6902d7", "value": "generation_config.json: 100%"}}, "0d6ec3bab579406fa4e6fc2b3d6b6998": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e2998c2c6b1f4d489a5e39f2076838e4", "placeholder": "​", "style": "IPY_MODEL_4300755179d9465db871b14ae78dabc6", "value": "Downloading shards: 100%"}}, "0e27bfa4f64f427d9996de0451e9edd9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d5e9f339fe7e4ab9955531cc125f071e", "IPY_MODEL_fa9cf3e72280417d8711ef7227a95d34", "IPY_MODEL_c3a1b520140444fbb40b7ac789f7ac0e"], "layout": "IPY_MODEL_2c84bc5c158641f49f421a7d28da1518"}}, "19fd13ad5b2740aa8be2a7d62488fdaf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2677891ce891475b8dc7c2ae287c58d7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "29178e51df9e47489fff623763b130ed": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2c84bc5c158641f49f421a7d28da1518": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2e081a17ddc04104882893a30c902265": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "30885be6a7c84f0f9f02bc2ea11679bc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33fef11f829f49e2aa9555201d4a0e42": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4300755179d9465db871b14ae78dabc6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "43fcf04b360f4a75be9fb99ab69fbe38": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4c6736981923464db2f754339c60cd0d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fddbae43ce7f477cafaff89b81e47fc7", "max": 67121608, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_592d069be51e43f99212d47be9c11dcf", "value": 67121608}}, "4e62b9ec821348cc94b34cfbc010c2a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ce6a3a655d2f4ce2ab351c766568bed5", "placeholder": "​", "style": "IPY_MODEL_19fd13ad5b2740aa8be2a7d62488fdaf", "value": " 13.5k/13.5k [00:00&lt;00:00, 1.10MB/s]"}}, "5266bebcf8bb4b0798b14831a38e2a8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7c638aaf734c423fbe54daddff97040f", "IPY_MODEL_4c6736981923464db2f754339c60cd0d", "IPY_MODEL_57383c03fc854a92a2ff732cbdd80a70"], "layout": "IPY_MODEL_8a302ae0412b4393a17b50861afe36b5"}}, "55941e08c602404c9342d00b7ee26918": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d7a00f1f114e4f008f4d5a48c1c69f53", "placeholder": "​", "style": "IPY_MODEL_faf25fd219f24bdbaa2e3202548c97d9", "value": " 2/2 [00:04&lt;00:00,  1.94s/it]"}}, "57383c03fc854a92a2ff732cbdd80a70": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9a4c90a767c746659ea535d7c36d40a5", "placeholder": "​", "style": "IPY_MODEL_43fcf04b360f4a75be9fb99ab69fbe38", "value": " 67.1M/67.1M [00:00&lt;00:00, 465MB/s]"}}, "5776e818d9d34e009f95833056522876": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "592d069be51e43f99212d47be9c11dcf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "60c4d6d5e7a84fa493f101cc47dadef9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fa0c528cca744cff8da0a4fa21fdb4b5", "IPY_MODEL_d7d4a9f444fe4ebb9135035e2166a3a5", "IPY_MODEL_4e62b9ec821348cc94b34cfbc010c2a4"], "layout": "IPY_MODEL_9d9a247c6569458abd0dcd6e0d717079"}}, "6a15f1cf54a141fc9d6bb790366c6bdd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ccbd7b9ae924b5e843efd3114dfb2c5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d7c024126ac4c34825fae522234ebca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "713f1d91e445411288e565a33ce4b271": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ccbd7b9ae924b5e843efd3114dfb2c5", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9e0bccbc6072461fbf96482b870ed8d5", "value": 2}}, "7c638aaf734c423fbe54daddff97040f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b2fdc502d6ee491bb826fd616e35d407", "placeholder": "​", "style": "IPY_MODEL_2677891ce891475b8dc7c2ae287c58d7", "value": "model-00002-of-00002.safetensors: 100%"}}, "7f59906980724a8b840dec85ce400f89": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e7f471604a5a42e095d35d8ad399c6fe", "max": 137, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_feb438afda6b4c148a3a62ee7e03da74", "value": 137}}, "80f3d29327bf429481ad191b1abe556f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e68cf53b04a845ac9d6f4047600ebc21", "placeholder": "​", "style": "IPY_MODEL_33fef11f829f49e2aa9555201d4a0e42", "value": " 137/137 [00:00&lt;00:00, 11.9kB/s]"}}, "87da02f5606d404ea242c3bd1f9ac38c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8813b56cd89744b58ace2787206e1501": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "88226abe35534278bbd427d8eff0f5f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8a302ae0412b4393a17b50861afe36b5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8aa8805651d34181b1851d302ccc47e2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_947f9b7e19dc4be4bd21b1b021e91f9d", "placeholder": "​", "style": "IPY_MODEL_0b7f3d233b8f4912bef4deae2e395001", "value": "Loading checkpoint shards: 100%"}}, "938f8f60901442f2902eb51e86c27961": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "947f9b7e19dc4be4bd21b1b021e91f9d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9a4c90a767c746659ea535d7c36d40a5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9d9a247c6569458abd0dcd6e0d717079": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9e0bccbc6072461fbf96482b870ed8d5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a0600fb407034c2d8df6ae5830d601db": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a0996675df13484aaa519e6ff45c5476": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0bfb4937ed5547b3ba464ca47ac77f1a", "IPY_MODEL_7f59906980724a8b840dec85ce400f89", "IPY_MODEL_80f3d29327bf429481ad191b1abe556f"], "layout": "IPY_MODEL_6d7c024126ac4c34825fae522234ebca"}}, "a37f2164e11d4e5f851a4a09a12c663c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f14106c7f60f411199acf47f530443fd", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bf88ee6dc83648d8a61d75bb4466b1e3", "value": 2}}, "b2fdc502d6ee491bb826fd616e35d407": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b7c439aa6d584c5784b46980050e503d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8aa8805651d34181b1851d302ccc47e2", "IPY_MODEL_713f1d91e445411288e565a33ce4b271", "IPY_MODEL_55941e08c602404c9342d00b7ee26918"], "layout": "IPY_MODEL_87da02f5606d404ea242c3bd1f9ac38c"}}, "bf88ee6dc83648d8a61d75bb4466b1e3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c06e354cb8294e66a3d7590a576571e0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c1d37ab1952b4d268d9786b74b6902d7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c3a1b520140444fbb40b7ac789f7ac0e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_30885be6a7c84f0f9f02bc2ea11679bc", "placeholder": "​", "style": "IPY_MODEL_29178e51df9e47489fff623763b130ed", "value": " 4.95G/4.95G [00:16&lt;00:00, 216MB/s]"}}, "c7c0c34a71954d6ea976c774573c49c5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0d6ec3bab579406fa4e6fc2b3d6b6998", "IPY_MODEL_a37f2164e11d4e5f851a4a09a12c663c", "IPY_MODEL_ef32431228f24a5498810a36b9cf6506"], "layout": "IPY_MODEL_c06e354cb8294e66a3d7590a576571e0"}}, "ce6a3a655d2f4ce2ab351c766568bed5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d5e9f339fe7e4ab9955531cc125f071e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6a15f1cf54a141fc9d6bb790366c6bdd", "placeholder": "​", "style": "IPY_MODEL_8813b56cd89744b58ace2787206e1501", "value": "model-00001-of-00002.safetensors: 100%"}}, "d7a00f1f114e4f008f4d5a48c1c69f53": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d7d4a9f444fe4ebb9135035e2166a3a5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2e081a17ddc04104882893a30c902265", "max": 13489, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_938f8f60901442f2902eb51e86c27961", "value": 13489}}, "e2998c2c6b1f4d489a5e39f2076838e4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e5a6d300bbf441b8904aa9afb89e6f31": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e68cf53b04a845ac9d6f4047600ebc21": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7f471604a5a42e095d35d8ad399c6fe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eba6048eb694485693656fcbf4a4f297": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "edc37210db734d01a8afce596698bb27": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ef32431228f24a5498810a36b9cf6506": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5776e818d9d34e009f95833056522876", "placeholder": "​", "style": "IPY_MODEL_06b1a069317041c8a9174c14fdc867bc", "value": " 2/2 [00:17&lt;00:00,  7.35s/it]"}}, "f14106c7f60f411199acf47f530443fd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fa0c528cca744cff8da0a4fa21fdb4b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e5a6d300bbf441b8904aa9afb89e6f31", "placeholder": "​", "style": "IPY_MODEL_88226abe35534278bbd427d8eff0f5f8", "value": "model.safetensors.index.json: 100%"}}, "fa9cf3e72280417d8711ef7227a95d34": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_edc37210db734d01a8afce596698bb27", "max": 4945242264, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_eba6048eb694485693656fcbf4a4f297", "value": 4945242264}}, "faf25fd219f24bdbaa2e3202548c97d9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fddbae43ce7f477cafaff89b81e47fc7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "feb438afda6b4c148a3a62ee7e03da74": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}