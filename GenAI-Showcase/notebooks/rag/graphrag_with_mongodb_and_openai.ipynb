{"cells": [{"cell_type": "markdown", "metadata": {"id": "GKMaKkd5W0wH"}, "source": ["# Enhancing HR Recruitment with MongoDB and OpenAI: A GraphRAG Approach"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ut1refDI6Bqw", "outputId": "b8cf013d-4558-4654-be3d-8f36a6aabf06"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/1.4 MB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m34.1 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m15.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m233.5/233.5 kB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m313.6/313.6 kB\u001b[0m \u001b[31m11.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m19.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m78.6/78.6 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "ipython-sql 0.5.0 requires sqlalchemy>=2.0, but you have sqlalchemy 1.4.54 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0m"]}], "source": ["!pip install --quiet pymongo dataset openai pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mBT0iO1JAh-d"}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "# Function to securely get and set environment variables\n", "def set_env_securely(var_name, prompt):\n", "    value = getpass.getpass(prompt)\n", "    os.environ[var_name] = value"]}, {"cell_type": "markdown", "metadata": {"id": "sXNSq47eIbR5"}, "source": ["## Data Loading and Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CepZsRpH6PsG"}, "outputs": [], "source": ["# load in csv with pandas\n", "import pandas as pd\n", "\n", "employee_df = pd.read_csv(\"employee_dataset_200.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4rxQNc94Do0r"}, "outputs": [], "source": ["import ast\n", "\n", "# Convert 'skills' and 'certifications' to actual arrays (list format)\n", "employee_df[\"skills\"] = employee_df[\"skills\"].apply(\n", "    lambda x: ast.literal_eval(x) if isinstance(x, str) else x\n", ")\n", "employee_df[\"certifications\"] = employee_df[\"certifications\"].apply(\n", "    lambda x: ast.literal_eval(x) if isinstance(x, str) else x\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 640}, "id": "wRRv2w01AGdF", "outputId": "e2135040-853c-4cbb-b700-920e06397c79"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"employee_df\",\n  \"rows\": 200,\n  \"fields\": [\n    {\n      \"column\": \"employee_id\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 57,\n        \"min\": 1,\n        \"max\": 200,\n        \"num_unique_values\": 200,\n        \"samples\": [\n          96,\n          16,\n          31\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"name\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 86,\n        \"samples\": [\n          \"<PERSON> Miller\",\n          \"Jack Brown\",\n          \"<PERSON> Miller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"position\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Backend Developer\",\n          \"Project Manager\",\n          \"Data Scientist\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"location\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"Berlin\",\n          \"New York\",\n          \"Tokyo\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"experience_years\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 4,\n        \"min\": 1,\n        \"max\": 15,\n        \"num_unique_values\": 15,\n        \"samples\": [\n          5,\n          1,\n          9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"employment_status\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Contract\",\n          \"Full-Time\",\n          \"Part-Time\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"salary_range\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 198,\n        \"samples\": [\n          \"102k-162k\",\n          \"57k-205k\",\n          \"122k-154k\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"team\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Infrastructure\",\n          \"AI & Analytics\",\n          \"Project Management\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"skills\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"certifications\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Experience\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"DevOps Engineer with 3 years of experience specializing in CI/CD, Kubernetes, HTML, Machine Learning, Data Analysis.\",\n          \"Data Scientist with 12 years of experience specializing in SQL, Java, Scrum, Docker, Data Analysis.\",\n          \"DevOps Engineer with 2 years of experience specializing in API Development, Kubernetes, Scrum.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Summary\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"Diana Brown excels at delivering results in backend services while leveraging skills in CI/CD, Kubernetes, HTML, Machine Learning, Data Analysis.\",\n          \"Diana Taylor excels at delivering results in project management while leveraging skills in SQL, Java, Scrum, Docker, Data Analysis.\",\n          \"Grace Brown excels at delivering results in backend services while leveraging skills in API Development, Kubernetes, Scrum.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "employee_df"}, "text/html": ["\n", "  <div id=\"df-12c5208c-1897-4fb8-9799-945b7b16301d\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>employee_id</th>\n", "      <th>name</th>\n", "      <th>position</th>\n", "      <th>location</th>\n", "      <th>experience_years</th>\n", "      <th>employment_status</th>\n", "      <th>salary_range</th>\n", "      <th>team</th>\n", "      <th>skills</th>\n", "      <th>certifications</th>\n", "      <th>Experience</th>\n", "      <th>Summary</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>DevOps Engineer</td>\n", "      <td>London</td>\n", "      <td>9</td>\n", "      <td>Contract</td>\n", "      <td>103k-208k</td>\n", "      <td>Frontend Development</td>\n", "      <td>[SQL, Agile, React, CI/CD]</td>\n", "      <td>[Certified Frontend Developer]</td>\n", "      <td><PERSON><PERSON>ps Engineer with 9 years of experience spe...</td>\n", "      <td><PERSON> excels at delivering results in fro...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>DevOps Engineer</td>\n", "      <td>New York</td>\n", "      <td>3</td>\n", "      <td>Full-Time</td>\n", "      <td>150k-206k</td>\n", "      <td>Infrastructure</td>\n", "      <td>[Data Analysis, Python, Docker, CSS]</td>\n", "      <td>[Oracle Certified, Google Cloud Certified]</td>\n", "      <td><PERSON><PERSON>ps Engineer with 3 years of experience spe...</td>\n", "      <td><PERSON> excels at delivering results in inf...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Backend Developer</td>\n", "      <td>Dubai</td>\n", "      <td>14</td>\n", "      <td>Contract</td>\n", "      <td>140k-179k</td>\n", "      <td>Frontend Development</td>\n", "      <td>[Python, Data Analysis, Java, API Development]</td>\n", "      <td>[Google Cloud Certified]</td>\n", "      <td>Backend Developer with 14 years of experience ...</td>\n", "      <td><PERSON> excels at delivering results in f...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON></td>\n", "      <td>Backend Developer</td>\n", "      <td>San Francisco</td>\n", "      <td>9</td>\n", "      <td>Full-Time</td>\n", "      <td>148k-198k</td>\n", "      <td>Project Management</td>\n", "      <td>[React, HTML, JavaScript]</td>\n", "      <td>[Oracle Certified, Certified Frontend Developer]</td>\n", "      <td>Backend Developer with 9 years of experience s...</td>\n", "      <td><PERSON> excels at delivering results in ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON></td>\n", "      <td>Data Scientist</td>\n", "      <td>Paris</td>\n", "      <td>6</td>\n", "      <td>Full-Time</td>\n", "      <td>138k-207k</td>\n", "      <td>Backend Services</td>\n", "      <td>[API Development, Java, Agile, Data Analysis, ...</td>\n", "      <td>[Certified Frontend Developer, Google Cloud Ce...</td>\n", "      <td>Data Scientist with 6 years of experience spec...</td>\n", "      <td><PERSON> excels at delivering results in ba...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-12c5208c-1897-4fb8-9799-945b7b16301d')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-12c5208c-1897-4fb8-9799-945b7b16301d button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-12c5208c-1897-4fb8-9799-945b7b16301d');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-6bcc9951-84b2-4c06-aaa9-33d20cd3bebe\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-6bcc9951-84b2-4c06-aaa9-33d20cd3bebe')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-6bcc9951-84b2-4c06-aaa9-33d20cd3bebe button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["   employee_id           name           position       location  \\\n", "0            1     <PERSON>Ops Engineer         London   \n", "1            2     <PERSON> Engineer       <PERSON> York   \n", "2            3   <PERSON>end Developer          Dubai   \n", "3            4  <PERSON>  Backend Developer  San Francisco   \n", "4            5    <PERSON>     Data Scientist          Paris   \n", "\n", "   experience_years employment_status salary_range                  team  \\\n", "0                 9          Contract    103k-208k  Frontend Development   \n", "1                 3         Full-Time    150k-206k        Infrastructure   \n", "2                14          Contract    140k-179k  Frontend Development   \n", "3                 9         Full-Time    148k-198k    Project Management   \n", "4                 6         Full-Time    138k-207k      Backend Services   \n", "\n", "                                              skills  \\\n", "0                         [SQL, Agile, React, CI/CD]   \n", "1               [Data Analysis, Python, Docker, CSS]   \n", "2     [Python, Data Analysis, Java, API Development]   \n", "3                          [React, HTML, JavaScript]   \n", "4  [API Development, Java, Agile, Data Analysis, ...   \n", "\n", "                                      certifications  \\\n", "0                     [Certified Frontend Developer]   \n", "1         [Oracle Certified, Google Cloud Certified]   \n", "2                           [Google Cloud Certified]   \n", "3   [Oracle Certified, Certified Frontend Developer]   \n", "4  [Certified Frontend Developer, Google Cloud Ce...   \n", "\n", "                                          Experience  \\\n", "0  DevOps Engineer with 9 years of experience spe...   \n", "1  DevOps Engineer with 3 years of experience spe...   \n", "2  Backend Developer with 14 years of experience ...   \n", "3  Backend Developer with 9 years of experience s...   \n", "4  Data Scientist with 6 years of experience spec...   \n", "\n", "                                             Summary  \n", "0  <PERSON> excels at delivering results in fro...  \n", "1  <PERSON> excels at delivering results in inf...  \n", "2  <PERSON> excels at delivering results in f...  \n", "3  <PERSON> excels at delivering results in ...  \n", "4  <PERSON> excels at delivering results in ba...  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["employee_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tVUX96xKMWlg", "outputId": "cac87243-1394-4a5c-9a65-18d8142fb7fc"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your OPENAI API KEY: ··········\n"]}], "source": ["set_env_securely(\"OPENAI_API_KEY\", \"Enter your OPENAI API KEY: \")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "quYe45WpK8Ee"}, "outputs": [], "source": ["import openai\n", "\n", "\n", "# Use OpenAI API to generate a summary for each data point\n", "def summarize_datapoint(data_point):\n", "    \"\"\"\n", "    Summarize the given data point using OpenAI's API.\n", "\n", "    Args:\n", "        data_point (str): The text to summarize.\n", "\n", "    Returns:\n", "        str: A concise summary of the input data.\n", "    \"\"\"\n", "    # Ensure the input is valid\n", "    if not data_point or not isinstance(data_point, str):\n", "        raise ValueError(\"Invalid data point. Please provide a non-empty string.\")\n", "\n", "    try:\n", "        # Call the OpenAI API\n", "        response = openai.chat.completions.create(\n", "            model=\"gpt-3.5-turbo\",\n", "            messages=[\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": \"You are an expert summarizer. Focus on the key points, removing unnecessary details. Write in a concise and clear manner.\",\n", "                },\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": f\"Please summarize the following data: {data_point}\",\n", "                },\n", "            ],\n", "        )\n", "\n", "        # Extract the summary\n", "        summary = response.choices[0].message.content\n", "\n", "        return summary\n", "    except Exception as e:\n", "        return f\"Error summarizing data: {e!s}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VGAfBdQBL3gn"}, "outputs": [], "source": ["def create_datapoint_summary(row):\n", "    \"\"\"Concatenates all attributes of a row and generates a summary.\"\"\"\n", "    # All columns except 'datapoint_summary' should be concatenated\n", "    attributes = [\n", "        str(value) for key, value in row.items() if key != \"datapoint_summary\"\n", "    ]\n", "    data_point = \" \".join(attributes)\n", "\n", "    summary = summarize_datapoint(data_point)  # Call the summarization function\n", "    return summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4MZtK8ttL5Pa"}, "outputs": [], "source": ["import tqdm\n", "\n", "# Apply the function to create the 'datapoint_summary' column\n", "employee_df[\"datapoint_summary\"] = employee_df.apply(create_datapoint_summary, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 660}, "id": "K_f7eO5HMMjq", "outputId": "26f6cf3e-98e2-4328-8ecf-29b556e4c36f"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"employee_df\",\n  \"rows\": 200,\n  \"fields\": [\n    {\n      \"column\": \"employee_id\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 57,\n        \"min\": 1,\n        \"max\": 200,\n        \"num_unique_values\": 200,\n        \"samples\": [\n          96,\n          16,\n          31\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"name\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 86,\n        \"samples\": [\n          \"<PERSON> Miller\",\n          \"Jack Brown\",\n          \"<PERSON> Miller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"position\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Backend Developer\",\n          \"Project Manager\",\n          \"Data Scientist\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"location\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"Berlin\",\n          \"New York\",\n          \"Tokyo\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"experience_years\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 4,\n        \"min\": 1,\n        \"max\": 15,\n        \"num_unique_values\": 15,\n        \"samples\": [\n          5,\n          1,\n          9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"employment_status\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Contract\",\n          \"Full-Time\",\n          \"Part-Time\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"salary_range\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 198,\n        \"samples\": [\n          \"102k-162k\",\n          \"57k-205k\",\n          \"122k-154k\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"team\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Infrastructure\",\n          \"AI & Analytics\",\n          \"Project Management\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"skills\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"certifications\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Experience\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"DevOps Engineer with 3 years of experience specializing in CI/CD, Kubernetes, HTML, Machine Learning, Data Analysis.\",\n          \"Data Scientist with 12 years of experience specializing in SQL, Java, Scrum, Docker, Data Analysis.\",\n          \"DevOps Engineer with 2 years of experience specializing in API Development, Kubernetes, Scrum.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Summary\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"Diana Brown excels at delivering results in backend services while leveraging skills in CI/CD, Kubernetes, HTML, Machine Learning, Data Analysis.\",\n          \"Diana Taylor excels at delivering results in project management while leveraging skills in SQL, Java, Scrum, Docker, Data Analysis.\",\n          \"Grace Brown excels at delivering results in backend services while leveraging skills in API Development, Kubernetes, Scrum.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"datapoint_summary\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"Diana Brown is a DevOps Engineer in London with 3 years of experience. She specializes in backend services and is skilled in CI/CD, Kubernetes, HTML, Machine Learning, and Data Analysis. Diana is certified as a Data Scientist and AWS Certified.\",\n          \"Diana Taylor is a Data Scientist in Toronto with 12 years of experience, specializing in SQL, Java, Scrum, Docker, and Data Analysis. She is proficient in project management, and her skills include SQL, Java, Scrum, Docker, and Data Analysis. She holds certifications in AWS, Data Science, and PMP.\",\n          \"Grace Brown is a DevOps Engineer in San Francisco with 2 years of experience, specializing in API Development, Kubernetes, and Scrum. She excels at delivering results in backend services. Grace is also PMP certified.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "employee_df"}, "text/html": ["\n", "  <div id=\"df-bd4970ed-629e-4832-adb5-18fbd8bc164e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>employee_id</th>\n", "      <th>name</th>\n", "      <th>position</th>\n", "      <th>location</th>\n", "      <th>experience_years</th>\n", "      <th>employment_status</th>\n", "      <th>salary_range</th>\n", "      <th>team</th>\n", "      <th>skills</th>\n", "      <th>certifications</th>\n", "      <th>Experience</th>\n", "      <th>Summary</th>\n", "      <th>datapoint_summary</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>DevOps Engineer</td>\n", "      <td>London</td>\n", "      <td>9</td>\n", "      <td>Contract</td>\n", "      <td>103k-208k</td>\n", "      <td>Frontend Development</td>\n", "      <td>[SQL, Agile, React, CI/CD]</td>\n", "      <td>[Certified Frontend Developer]</td>\n", "      <td><PERSON><PERSON>ps Engineer with 9 years of experience spe...</td>\n", "      <td><PERSON> excels at delivering results in fro...</td>\n", "      <td><PERSON> is a DevOps Engineer in London with...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>DevOps Engineer</td>\n", "      <td>New York</td>\n", "      <td>3</td>\n", "      <td>Full-Time</td>\n", "      <td>150k-206k</td>\n", "      <td>Infrastructure</td>\n", "      <td>[Data Analysis, Python, Docker, CSS]</td>\n", "      <td>[Oracle Certified, Google Cloud Certified]</td>\n", "      <td><PERSON><PERSON>ps Engineer with 3 years of experience spe...</td>\n", "      <td><PERSON> excels at delivering results in inf...</td>\n", "      <td><PERSON> is a Full-Time DevOps Engineer in N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Backend Developer</td>\n", "      <td>Dubai</td>\n", "      <td>14</td>\n", "      <td>Contract</td>\n", "      <td>140k-179k</td>\n", "      <td>Frontend Development</td>\n", "      <td>[Python, Data Analysis, Java, API Development]</td>\n", "      <td>[Google Cloud Certified]</td>\n", "      <td>Backend Developer with 14 years of experience ...</td>\n", "      <td><PERSON> excels at delivering results in f...</td>\n", "      <td><PERSON> is a Backend Developer in Dubai w...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON></td>\n", "      <td>Backend Developer</td>\n", "      <td>San Francisco</td>\n", "      <td>9</td>\n", "      <td>Full-Time</td>\n", "      <td>148k-198k</td>\n", "      <td>Project Management</td>\n", "      <td>[React, HTML, JavaScript]</td>\n", "      <td>[Oracle Certified, Certified Frontend Developer]</td>\n", "      <td>Backend Developer with 9 years of experience s...</td>\n", "      <td><PERSON> excels at delivering results in ...</td>\n", "      <td><PERSON> is a Backend Developer based in ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON></td>\n", "      <td>Data Scientist</td>\n", "      <td>Paris</td>\n", "      <td>6</td>\n", "      <td>Full-Time</td>\n", "      <td>138k-207k</td>\n", "      <td>Backend Services</td>\n", "      <td>[API Development, Java, Agile, Data Analysis, ...</td>\n", "      <td>[Certified Frontend Developer, Google Cloud Ce...</td>\n", "      <td>Data Scientist with 6 years of experience spec...</td>\n", "      <td><PERSON> excels at delivering results in ba...</td>\n", "      <td><PERSON> is a Data Scientist in Paris with ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-bd4970ed-629e-4832-adb5-18fbd8bc164e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-bd4970ed-629e-4832-adb5-18fbd8bc164e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-bd4970ed-629e-4832-adb5-18fbd8bc164e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-842463c7-08d8-46e6-9ad3-7457c8b13be6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-842463c7-08d8-46e6-9ad3-7457c8b13be6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-842463c7-08d8-46e6-9ad3-7457c8b13be6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["   employee_id           name           position       location  \\\n", "0            1     <PERSON>Ops Engineer         London   \n", "1            2     <PERSON> Engineer       <PERSON> York   \n", "2            3   <PERSON>end Developer          Dubai   \n", "3            4  <PERSON>  Backend Developer  San Francisco   \n", "4            5    <PERSON>     Data Scientist          Paris   \n", "\n", "   experience_years employment_status salary_range                  team  \\\n", "0                 9          Contract    103k-208k  Frontend Development   \n", "1                 3         Full-Time    150k-206k        Infrastructure   \n", "2                14          Contract    140k-179k  Frontend Development   \n", "3                 9         Full-Time    148k-198k    Project Management   \n", "4                 6         Full-Time    138k-207k      Backend Services   \n", "\n", "                                              skills  \\\n", "0                         [SQL, Agile, React, CI/CD]   \n", "1               [Data Analysis, Python, Docker, CSS]   \n", "2     [Python, Data Analysis, Java, API Development]   \n", "3                          [React, HTML, JavaScript]   \n", "4  [API Development, Java, Agile, Data Analysis, ...   \n", "\n", "                                      certifications  \\\n", "0                     [Certified Frontend Developer]   \n", "1         [Oracle Certified, Google Cloud Certified]   \n", "2                           [Google Cloud Certified]   \n", "3   [Oracle Certified, Certified Frontend Developer]   \n", "4  [Certified Frontend Developer, Google Cloud Ce...   \n", "\n", "                                          Experience  \\\n", "0  DevOps Engineer with 9 years of experience spe...   \n", "1  DevOps Engineer with 3 years of experience spe...   \n", "2  Backend Developer with 14 years of experience ...   \n", "3  Backend Developer with 9 years of experience s...   \n", "4  Data Scientist with 6 years of experience spec...   \n", "\n", "                                             Summary  \\\n", "0  <PERSON> excels at delivering results in fro...   \n", "1  <PERSON> excels at delivering results in inf...   \n", "2  <PERSON> excels at delivering results in f...   \n", "3  <PERSON> excels at delivering results in ...   \n", "4  <PERSON> excels at delivering results in ba...   \n", "\n", "                                   datapoint_summary  \n", "0  <PERSON> is a DevOps Engineer in London with...  \n", "1  <PERSON> is a Full-Time DevOps Engineer in N...  \n", "2  <PERSON> is a Backend Developer in Dubai w...  \n", "3  <PERSON> is a Backend Developer based in ...  \n", "4  <PERSON> is a Data Scientist in Paris with ...  "]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["employee_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "hD3KrUAXIuhu"}, "source": ["## Embedding Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "E6qFvAqqJDbC"}, "outputs": [], "source": ["OPENAI_EMBEDDING_MODEL = \"text-embedding-3-small\"\n", "OPENAI_EMBEDDING_MODEL_DIMENSION = 1536"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kMaM_aHcIxYh"}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "\n", "# Generate an embedding using OpenAI's API\n", "def get_embedding(text):\n", "    \"\"\"Generate an embedding for the given text using OpenAI's API.\"\"\"\n", "\n", "    # Check for valid input\n", "    if not text or not isinstance(text, str):\n", "        return None\n", "\n", "    try:\n", "        # Call OpenAI API to get the embedding\n", "        embedding = (\n", "            openai.embeddings.create(\n", "                input=text,\n", "                model=OPENAI_EMBEDDING_MODEL,\n", "                dimensions=OPENAI_EMBEDDING_MODEL_DIMENSION,\n", "            )\n", "            .data[0]\n", "            .embedding\n", "        )\n", "        return embedding\n", "    except Exception as e:\n", "        print(f\"Error in get_embedding: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xFdCLmXxNikJ", "outputId": "e934697f-aec6-4a26-a0c6-501f27e7f2d3"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 200/200 [00:00<00:00, 839700.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Embeddings generated for employees\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Apply the function to generate embeddings for all employees with error handling and progress tracking\n", "try:\n", "    employee_df[\"embedding\"] = [\n", "        x\n", "        for x in tqdm(\n", "            employee_df[\"datapoint_summary\"].apply(get_embedding),\n", "            total=len(employee_df),\n", "        )\n", "    ]\n", "    print(\"Embeddings generated for employees\")\n", "except Exception as e:\n", "    print(f\"Error applying embedding function to DataFrame: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 660}, "id": "r2y662UGN42-", "outputId": "2a1be515-0808-4852-af0e-51cccc971650"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"employee_df\",\n  \"rows\": 200,\n  \"fields\": [\n    {\n      \"column\": \"employee_id\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 57,\n        \"min\": 1,\n        \"max\": 200,\n        \"num_unique_values\": 200,\n        \"samples\": [\n          96,\n          16,\n          31\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"name\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 86,\n        \"samples\": [\n          \"<PERSON> Miller\",\n          \"Jack Brown\",\n          \"<PERSON> Miller\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"position\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Backend Developer\",\n          \"Project Manager\",\n          \"Data Scientist\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"location\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"Berlin\",\n          \"New York\",\n          \"Tokyo\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"experience_years\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 4,\n        \"min\": 1,\n        \"max\": 15,\n        \"num_unique_values\": 15,\n        \"samples\": [\n          5,\n          1,\n          9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"employment_status\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Contract\",\n          \"Full-Time\",\n          \"Part-Time\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"salary_range\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 198,\n        \"samples\": [\n          \"102k-162k\",\n          \"57k-205k\",\n          \"122k-154k\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"team\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"Infrastructure\",\n          \"AI & Analytics\",\n          \"Project Management\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"skills\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"certifications\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Experience\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"DevOps Engineer with 3 years of experience specializing in CI/CD, Kubernetes, HTML, Machine Learning, Data Analysis.\",\n          \"Data Scientist with 12 years of experience specializing in SQL, Java, Scrum, Docker, Data Analysis.\",\n          \"DevOps Engineer with 2 years of experience specializing in API Development, Kubernetes, Scrum.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Summary\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"Diana Brown excels at delivering results in backend services while leveraging skills in CI/CD, Kubernetes, HTML, Machine Learning, Data Analysis.\",\n          \"Diana Taylor excels at delivering results in project management while leveraging skills in SQL, Java, Scrum, Docker, Data Analysis.\",\n          \"Grace Brown excels at delivering results in backend services while leveraging skills in API Development, Kubernetes, Scrum.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"datapoint_summary\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 200,\n        \"samples\": [\n          \"Diana Brown is a DevOps Engineer in London with 3 years of experience. She specializes in backend services and is skilled in CI/CD, Kubernetes, HTML, Machine Learning, and Data Analysis. Diana is certified as a Data Scientist and AWS Certified.\",\n          \"Diana Taylor is a Data Scientist in Toronto with 12 years of experience, specializing in SQL, Java, Scrum, Docker, and Data Analysis. She is proficient in project management, and her skills include SQL, Java, Scrum, Docker, and Data Analysis. She holds certifications in AWS, Data Science, and PMP.\",\n          \"Grace Brown is a DevOps Engineer in San Francisco with 2 years of experience, specializing in API Development, Kubernetes, and Scrum. She excels at delivering results in backend services. Grace is also PMP certified.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "employee_df"}, "text/html": ["\n", "  <div id=\"df-db434e18-848a-405c-bff5-31f8b0a9dda4\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>employee_id</th>\n", "      <th>name</th>\n", "      <th>position</th>\n", "      <th>location</th>\n", "      <th>experience_years</th>\n", "      <th>employment_status</th>\n", "      <th>salary_range</th>\n", "      <th>team</th>\n", "      <th>skills</th>\n", "      <th>certifications</th>\n", "      <th>Experience</th>\n", "      <th>Summary</th>\n", "      <th>datapoint_summary</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>DevOps Engineer</td>\n", "      <td>London</td>\n", "      <td>9</td>\n", "      <td>Contract</td>\n", "      <td>103k-208k</td>\n", "      <td>Frontend Development</td>\n", "      <td>[SQL, Agile, React, CI/CD]</td>\n", "      <td>[Certified Frontend Developer]</td>\n", "      <td><PERSON><PERSON>ps Engineer with 9 years of experience spe...</td>\n", "      <td><PERSON> excels at delivering results in fro...</td>\n", "      <td><PERSON> is a DevOps Engineer in London with...</td>\n", "      <td>[-0.033142998814582825, -0.016862226650118828,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>DevOps Engineer</td>\n", "      <td>New York</td>\n", "      <td>3</td>\n", "      <td>Full-Time</td>\n", "      <td>150k-206k</td>\n", "      <td>Infrastructure</td>\n", "      <td>[Data Analysis, Python, Docker, CSS]</td>\n", "      <td>[Oracle Certified, Google Cloud Certified]</td>\n", "      <td><PERSON><PERSON>ps Engineer with 3 years of experience spe...</td>\n", "      <td><PERSON> excels at delivering results in inf...</td>\n", "      <td><PERSON> is a Full-Time DevOps Engineer in N...</td>\n", "      <td>[-0.061603933572769165, -0.039547309279441833,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Backend Developer</td>\n", "      <td>Dubai</td>\n", "      <td>14</td>\n", "      <td>Contract</td>\n", "      <td>140k-179k</td>\n", "      <td>Frontend Development</td>\n", "      <td>[Python, Data Analysis, Java, API Development]</td>\n", "      <td>[Google Cloud Certified]</td>\n", "      <td>Backend Developer with 14 years of experience ...</td>\n", "      <td><PERSON> excels at delivering results in f...</td>\n", "      <td><PERSON> is a Backend Developer in Dubai w...</td>\n", "      <td>[-0.025056499987840652, 0.0008755207527428865,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON></td>\n", "      <td>Backend Developer</td>\n", "      <td>San Francisco</td>\n", "      <td>9</td>\n", "      <td>Full-Time</td>\n", "      <td>148k-198k</td>\n", "      <td>Project Management</td>\n", "      <td>[React, HTML, JavaScript]</td>\n", "      <td>[Oracle Certified, Certified Frontend Developer]</td>\n", "      <td>Backend Developer with 9 years of experience s...</td>\n", "      <td><PERSON> excels at delivering results in ...</td>\n", "      <td><PERSON> is a Backend Developer based in ...</td>\n", "      <td>[-0.046922024339437485, 0.006648077629506588, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON></td>\n", "      <td>Data Scientist</td>\n", "      <td>Paris</td>\n", "      <td>6</td>\n", "      <td>Full-Time</td>\n", "      <td>138k-207k</td>\n", "      <td>Backend Services</td>\n", "      <td>[API Development, Java, Agile, Data Analysis, ...</td>\n", "      <td>[Certified Frontend Developer, Google Cloud Ce...</td>\n", "      <td>Data Scientist with 6 years of experience spec...</td>\n", "      <td><PERSON> excels at delivering results in ba...</td>\n", "      <td><PERSON> is a Data Scientist in Paris with ...</td>\n", "      <td>[-0.06926461309194565, -0.003424275666475296, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-db434e18-848a-405c-bff5-31f8b0a9dda4')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-db434e18-848a-405c-bff5-31f8b0a9dda4 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-db434e18-848a-405c-bff5-31f8b0a9dda4');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-054a2b1e-85a7-4019-9264-199234c0865f\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-054a2b1e-85a7-4019-9264-199234c0865f')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-054a2b1e-85a7-4019-9264-199234c0865f button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["   employee_id           name           position       location  \\\n", "0            1     <PERSON>Ops Engineer         London   \n", "1            2     <PERSON> Engineer       <PERSON> York   \n", "2            3   <PERSON>end Developer          Dubai   \n", "3            4  <PERSON>  Backend Developer  San Francisco   \n", "4            5    <PERSON>     Data Scientist          Paris   \n", "\n", "   experience_years employment_status salary_range                  team  \\\n", "0                 9          Contract    103k-208k  Frontend Development   \n", "1                 3         Full-Time    150k-206k        Infrastructure   \n", "2                14          Contract    140k-179k  Frontend Development   \n", "3                 9         Full-Time    148k-198k    Project Management   \n", "4                 6         Full-Time    138k-207k      Backend Services   \n", "\n", "                                              skills  \\\n", "0                         [SQL, Agile, React, CI/CD]   \n", "1               [Data Analysis, Python, Docker, CSS]   \n", "2     [Python, Data Analysis, Java, API Development]   \n", "3                          [React, HTML, JavaScript]   \n", "4  [API Development, Java, Agile, Data Analysis, ...   \n", "\n", "                                      certifications  \\\n", "0                     [Certified Frontend Developer]   \n", "1         [Oracle Certified, Google Cloud Certified]   \n", "2                           [Google Cloud Certified]   \n", "3   [Oracle Certified, Certified Frontend Developer]   \n", "4  [Certified Frontend Developer, Google Cloud Ce...   \n", "\n", "                                          Experience  \\\n", "0  DevOps Engineer with 9 years of experience spe...   \n", "1  DevOps Engineer with 3 years of experience spe...   \n", "2  Backend Developer with 14 years of experience ...   \n", "3  Backend Developer with 9 years of experience s...   \n", "4  Data Scientist with 6 years of experience spec...   \n", "\n", "                                             Summary  \\\n", "0  <PERSON> excels at delivering results in fro...   \n", "1  <PERSON> excels at delivering results in inf...   \n", "2  <PERSON> excels at delivering results in f...   \n", "3  <PERSON> excels at delivering results in ...   \n", "4  <PERSON> excels at delivering results in ba...   \n", "\n", "                                   datapoint_summary  \\\n", "0  <PERSON> is a DevOps Engineer in London with...   \n", "1  <PERSON> is a Full-Time DevOps Engineer in N...   \n", "2  <PERSON> is a Backend Developer in Dubai w...   \n", "3  <PERSON> is a Backend Developer based in ...   \n", "4  <PERSON> is a Data Scientist in Paris with ...   \n", "\n", "                                           embedding  \n", "0  [-0.033142998814582825, -0.016862226650118828,...  \n", "1  [-0.061603933572769165, -0.039547309279441833,...  \n", "2  [-0.025056499987840652, 0.0008755207527428865,...  \n", "3  [-0.046922024339437485, 0.006648077629506588, ...  \n", "4  [-0.06926461309194565, -0.003424275666475296, ...  "]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["employee_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "n8nuRx77If0i"}, "source": ["## Connecting to MongoDB"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZleROLOIAuZ8", "outputId": "73a935a7-bd6f-49e4-879e-a33b0890dd48"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your MONGO URI: ··········\n"]}], "source": ["# Set MongoDB URI\n", "set_env_securely(\"MONGO_URI\", \"Enter your MONGO URI: \")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "DSufLXRDAHsX"}, "outputs": [], "source": ["import pymongo\n", "\n", "\n", "def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish and validate connection to the MongoDB.\"\"\"\n", "\n", "    client = pymongo.MongoClient(\n", "        mongo_uri, appname=\"devrel.showcase.rag.graphrag.employees.python\"\n", "    )\n", "\n", "    # Validate the connection\n", "    ping_result = client.admin.command(\"ping\")\n", "    if ping_result.get(\"ok\") == 1.0:\n", "        # Connection successful\n", "        print(\"Connection to MongoDB successful\")\n", "        return client\n", "    print(\"Connection to MongoDB failed\")\n", "    return None\n", "\n", "\n", "MONGO_URI = os.environ[\"MONGO_URI\"]\n", "if not MONGO_URI:\n", "    print(\"MONGO_URI not set in environment variables\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "omFVZEKdAxY3", "outputId": "6438a5bf-e4bf-411a-ea14-8697e333c50a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["mongo_client = get_mongo_client(MONGO_URI)\n", "\n", "DB_NAME = \"acme_corpration\"\n", "COLLECTION_NAME = \"employees\"\n", "\n", "# Create or get the database\n", "db = mongo_client[DB_NAME]\n", "\n", "# Create or get the collections\n", "collection = db[COLLECTION_NAME]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ynrUYMm9A5O6", "outputId": "c6f6dc3b-60ca-4ca0-d616-3f7b1564aae1"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 200, 'electionId': ObjectId('7fffffff0000000000000039'), 'opTime': {'ts': Timestamp(1732788089, 200), 't': 57}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1732788089, 200), 'signature': {'hash': b'\\x8f\\xc1\\xcb\\x89WD\\xdeG+\\x13u\\xc5\\xa0\\xeeP\\xba\\xd1\\x00\\xc9\\xd3', 'keyId': 7390008424139849730}}, 'operationTime': Timestamp(1732788089, 200)}, acknowledged=True)"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["collection.delete_many({})"]}, {"cell_type": "markdown", "metadata": {"id": "fuE1e5NdIk0J"}, "source": ["## Data Ingestion"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GtmiE7-sA7Pn", "outputId": "2abc67f3-9d93-4b8f-fc77-67f47d1d1da0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data ingestion into MongoDB completed\n"]}], "source": ["documents = employee_df.to_dict(\"records\")\n", "collection.insert_many(documents)\n", "\n", "print(\"Data ingestion into MongoDB completed\")"]}, {"cell_type": "markdown", "metadata": {"id": "8OsyJ2VKIoal"}, "source": ["## MongoDB Graph Lookup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vcj7wPAhBUuQ"}, "outputs": [], "source": ["# Updated GraphLookup Query: Find Employees with Shared Skills\n", "graph_lookup_query = [\n", "    {\n", "        \"$match\": {\"employee_id\": 1}  # Start with Employee 1\n", "    },\n", "    {\n", "        \"$graphLookup\": {\n", "            \"from\": \"employees\",  # Collection name\n", "            \"startWith\": \"$team\",  # Starting with the employee's skills array\n", "            \"connectFromField\": \"team\",  # Match on array elements in the starting employee\n", "            \"connectToField\": \"team\",  # Match on array elements in other employees\n", "            \"as\": \"related_employees\",  # Output field for related employees\n", "            \"maxDepth\": 1,  # Limit the depth of recursion\n", "            \"depthField\": \"level\",  # Optional: Include the depth level in results\n", "        }\n", "    },\n", "]\n", "\n", "# Project the emebedding field\n", "project_stage = {\"$project\": {\"embedding\": 0, \"related_employees.embedding\": 0}}\n", "\n", "graph_lookup_query.append(project_stage)\n", "\n", "\n", "# Execute the query\n", "result = list(collection.aggregate(graph_lookup_query))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qHUULuoJBaro", "outputId": "d73b6db0-3bc5-4316-d813-faaaaf8ee0ea"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'Experience': 'DevOps Engineer with 9 years of experience specializing in '\n", "                'SQL, Agile, React, CI/CD.',\n", "  'Summary': '<PERSON> excels at delivering results in frontend development '\n", "             'while leveraging skills in SQL, Agile, React, CI/CD.',\n", "  '_id': ObjectId('67483f7c75040e5ce95c618a'),\n", "  'certifications': ['Certified Frontend Developer'],\n", "  'datapoint_summary': '<PERSON> is a DevOps Engineer in London with 9 years '\n", "                       'of experience. He specializes in SQL, Agile, React, '\n", "                       'and CI/CD, and is a Certified Frontend Developer. He '\n", "                       'excels in frontend development and is skilled in '\n", "                       'various technologies.',\n", "  'employee_id': 1,\n", "  'employment_status': 'Contract',\n", "  'experience_years': 9,\n", "  'location': 'London',\n", "  'name': '<PERSON>',\n", "  'position': 'DevOps Engineer',\n", "  'related_employees': [{'Experience': 'Backend Developer with 3 years of '\n", "                                       'experience specializing in Scrum, '\n", "                                       'Re<PERSON>, <PERSON>.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c620c'),\n", "                         'certifications': ['PMP Certified', 'AWS Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Backend '\n", "                                              'Dev<PERSON><PERSON> in Berlin with 3 '\n", "                                              'years of experience. He '\n", "                                              'specializes in <PERSON><PERSON>, React, '\n", "                                              'and Python, delivering results '\n", "                                              'in frontend development and '\n", "                                              'holds certifications in PMP and '\n", "                                              'AWS.',\n", "                         'employee_id': 131,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 3,\n", "                         'level': 0,\n", "                         'location': 'Berlin',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '135k-158k',\n", "                         'skills': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 11 years of '\n", "                                       'experience specializing in CSS, '\n", "                                       'Machine Learning, Docker.',\n", "                         'Summary': '<PERSON> excels at delivering results in '\n", "                                    'frontend development while leveraging '\n", "                                    'skills in CSS, Machine Learning, Docker.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61d4'),\n", "                         'certifications': ['PMP Certified',\n", "                                            'Certified Frontend Developer'],\n", "                         'datapoint_summary': 'Jack Hall is a Frontend '\n", "                                              'Dev<PERSON><PERSON> in Berlin with 11 '\n", "                                              'years of experience, '\n", "                                              'specializing in CSS, Machine '\n", "                                              'Learning, and Docker. He is PMP '\n", "                                              'Certified and a Certified '\n", "                                              '<PERSON><PERSON>, excelling '\n", "                                              'in delivering results in '\n", "                                              'frontend development.',\n", "                         'employee_id': 75,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 11,\n", "                         'level': 0,\n", "                         'location': 'Berlin',\n", "                         'name': 'Jack Hall',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '137k-178k',\n", "                         'skills': ['CSS', 'Machine Learning', 'Docker'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'DevOps Engineer with 8 years of '\n", "                                       'experience specializing in Data '\n", "                                       'Analysis, Docker, Scrum, JavaScript, '\n", "                                       'Java.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Data Analysis, <PERSON><PERSON>, <PERSON><PERSON>, '\n", "                                    'JavaScript, Java.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6243'),\n", "                         'certifications': ['PMP Certified',\n", "                                            'Certified Data Scientist',\n", "                                            'Google Cloud Certified'],\n", "                         'datapoint_summary': 'Charlie Hall is a DevOps '\n", "                                              'Engineer with 8 years of '\n", "                                              'experience in frontend '\n", "                                              'development, specializing in '\n", "                                              'Data Analysis, <PERSON><PERSON>, <PERSON><PERSON>, '\n", "                                              'JavaScript, and Java. He is PMP '\n", "                                              'certified, a Certified Data '\n", "                                              'Scientist, and Google Cloud '\n", "                                              'certified.',\n", "                         'employee_id': 186,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 8,\n", "                         'level': 0,\n", "                         'location': 'Tokyo',\n", "                         'name': 'Charlie Hall',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '116k-215k',\n", "                         'skills': ['Data Analysis',\n", "                                    'Docker',\n", "                                    'Scrum',\n", "                                    'JavaScript',\n", "                                    'Java'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 15 years of '\n", "                                       'experience specializing in CSS, '\n", "                                       '<PERSON><PERSON>, <PERSON>.',\n", "                         'Summary': '<PERSON> Hall excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c624e'),\n", "                         'certifications': ['Certified Data Scientist',\n", "                                            'Google Cloud Certified',\n", "                                            'Oracle Certified'],\n", "                         'datapoint_summary': 'Summary:\\n'\n", "                                              '- Name: <PERSON>\\n'\n", "                                              '- Occupation: Backend '\n", "                                              'Developer\\n'\n", "                                              '- Location: New York\\n'\n", "                                              '- Experience: 15 years\\n'\n", "                                              '- Employment Type: Contract\\n'\n", "                                              '- Salary: $72,000-$157,000\\n'\n", "                                              '- Skills: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>\\n'\n", "                                              '- Certifications: Certified '\n", "                                              'Data Scientist, Google Cloud '\n", "                                              'Certified, Oracle Certified\\n'\n", "                                              '- Specializes in frontend '\n", "                                              'development, excels at CSS, '\n", "                                              '<PERSON><PERSON>, and <PERSON>.',\n", "                         'employee_id': 197,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 15,\n", "                         'level': 0,\n", "                         'location': 'New York',\n", "                         'name': 'Alice Hall',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '72k-157k',\n", "                         'skills': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 6 years of '\n", "                                       'experience specializing in Machine '\n", "                                       'Learning, HTML, CSS, Agile, CI/CD.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Machine Learning, HTML, CSS, '\n", "                                    'Agile, CI/CD.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c623e'),\n", "                         'certifications': ['Certified Data Scientist',\n", "                                            'Google Cloud Certified'],\n", "                         'datapoint_summary': '<PERSON> is a frontend '\n", "                                              'developer in Berlin with 6 '\n", "                                              'years of experience. She '\n", "                                              'specializes in Machine '\n", "                                              'Learning, HTML, CSS, Agile, and '\n", "                                              'CI/CD. Grace is knowledgeable '\n", "                                              'in Certified Data Science and '\n", "                                              'Google Cloud. She has a salary '\n", "                                              'range of 87k-214k.',\n", "                         'employee_id': 181,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 6,\n", "                         'level': 0,\n", "                         'location': 'Berlin',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '87k-214k',\n", "                         'skills': ['Machine Learning',\n", "                                    'HTML',\n", "                                    'CSS',\n", "                                    'Agile',\n", "                                    'CI/CD'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Data Scientist with 12 years of '\n", "                                       'experience specializing in HTML, '\n", "                                       'JavaScript, Machine Learning, SQL, '\n", "                                       'Python.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in HTML, JavaScript, Machine '\n", "                                    'Learning, SQL, Python.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6222'),\n", "                         'certifications': ['Oracle Certified',\n", "                                            'PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Data Scientist '\n", "                                              'based in New York with 12 years '\n", "                                              'of experience. He specializes '\n", "                                              'in frontend development, '\n", "                                              'including HTML, JavaScript, '\n", "                                              'Machine Learning, SQL, and '\n", "                                              '<PERSON>. <PERSON> is skilled in both '\n", "                                              'software and data science '\n", "                                              'technologies and holds '\n", "                                              'certifications in Oracle and '\n", "                                              'PMP.',\n", "                         'employee_id': 153,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 12,\n", "                         'level': 0,\n", "                         'location': 'New York',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Data Scientist',\n", "                         'salary_range': '127k-218k',\n", "                         'skills': ['HTML',\n", "                                    'JavaScript',\n", "                                    'Machine Learning',\n", "                                    'SQL',\n", "                                    'Python'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Dev<PERSON>ps Engineer with 11 years of '\n", "                                       'experience specializing in CI/CD, '\n", "                                       'JavaScript, Kubernetes, HTML, API '\n", "                                       'Development.',\n", "                         'Summary': '<PERSON> excels at delivering '\n", "                                    'results in frontend development while '\n", "                                    'leveraging skills in CI/CD, JavaScript, '\n", "                                    'Kubernetes, HTML, API Development.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6201'),\n", "                         'certifications': ['Certified Frontend Developer'],\n", "                         'datapoint_summary': '<PERSON> is a part-time '\n", "                                              'Dev<PERSON>ps Engineer in London with '\n", "                                              '11 years of experience. He '\n", "                                              'specializes in CI/CD, '\n", "                                              'JavaScript, Kubernetes, HTML, '\n", "                                              'and API Development with salary '\n", "                                              'expectations of 89k-230k. He is '\n", "                                              'a Certified Frontend Developer '\n", "                                              'who excels in delivering '\n", "                                              'results in frontend '\n", "                                              'development.',\n", "                         'employee_id': 120,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 11,\n", "                         'level': 0,\n", "                         'location': 'London',\n", "                         'name': '<PERSON>',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '89k-230k',\n", "                         'skills': ['CI/CD',\n", "                                    'JavaScript',\n", "                                    'Kubernetes',\n", "                                    'HTML',\n", "                                    'API Development'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 7 years of '\n", "                                       'experience specializing in CSS, CI/CD, '\n", "                                       'Java, HTML, Kubernetes.',\n", "                         'Summary': '<PERSON> excels at delivering '\n", "                                    'results in frontend development while '\n", "                                    'leveraging skills in CSS, CI/CD, Java, '\n", "                                    'HTML, Kubernetes.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6206'),\n", "                         'certifications': ['PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a backend '\n", "                                              'developer based in Mumbai with '\n", "                                              '7 years of experience. He '\n", "                                              'specializes in CSS, CI/CD, '\n", "                                              'Java, HTML, and Kubernetes. '\n", "                                              'Additionally, he is proficient '\n", "                                              'in frontend development. '\n", "                                              '<PERSON> is PMP certified.',\n", "                         'employee_id': 125,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 7,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '94k-150k',\n", "                         'skills': ['CSS',\n", "                                    'CI/CD',\n", "                                    'Java',\n", "                                    'HTML',\n", "                                    'Kubernetes'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Project Manager with 14 years of '\n", "                                       'experience specializing in Data '\n", "                                       'Analysis, React, Machine Learning, '\n", "                                       'CSS.',\n", "                         'Summary': '<PERSON> excels at delivering results in '\n", "                                    'frontend development while leveraging '\n", "                                    'skills in Data Analysis, React, Machine '\n", "                                    'Learning, CSS.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61b2'),\n", "                         'certifications': ['AWS Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Project Manager '\n", "                                              'in New York with 14 years of '\n", "                                              'experience in frontend '\n", "                                              'development, specializing in '\n", "                                              'Data Analysis, React, Machine '\n", "                                              'Learning, and CSS. She is '\n", "                                              'skilled in AWS and has a salary '\n", "                                              'range of 55k-202k.',\n", "                         'employee_id': 41,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 14,\n", "                         'level': 0,\n", "                         'location': 'New York',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Project Manager',\n", "                         'salary_range': '55k-202k',\n", "                         'skills': ['Data Analysis',\n", "                                    'React',\n", "                                    'Machine Learning',\n", "                                    'CSS'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 13 years of '\n", "                                       'experience specializing in HTML, '\n", "                                       'JavaScript.',\n", "                         'Summary': '<PERSON> excels at delivering results in '\n", "                                    'frontend development while leveraging '\n", "                                    'skills in HTML, JavaScript.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6224'),\n", "                         'certifications': ['AWS Certified',\n", "                                            'Certified Data Scientist'],\n", "                         'datapoint_summary': 'Jack Hall is a frontend '\n", "                                              'developer in Sydney with 13 '\n", "                                              'years of experience. He '\n", "                                              'specializes in HTML and '\n", "                                              'JavaScript. Jack is certified '\n", "                                              'in AWS and data science. He is '\n", "                                              'skilled at delivering results '\n", "                                              'in frontend development.',\n", "                         'employee_id': 155,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 13,\n", "                         'level': 0,\n", "                         'location': 'Sydney',\n", "                         'name': 'Jack Hall',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '71k-212k',\n", "                         'skills': ['HTML', 'JavaScript'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'DevOps Engineer with 9 years of '\n", "                                       'experience specializing in SQL, Agile, '\n", "                                       'React, CI/CD.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in SQL, Agile, React, CI/CD.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c618a'),\n", "                         'certifications': ['Certified Frontend Developer'],\n", "                         'datapoint_summary': '<PERSON> is a DevOps Engineer '\n", "                                              'in London with 9 years of '\n", "                                              'experience. He specializes in '\n", "                                              'SQL, Agile, React, and CI/CD, '\n", "                                              'and is a Certified Frontend '\n", "                                              '<PERSON><PERSON><PERSON>. He excels in '\n", "                                              'frontend development and is '\n", "                                              'skilled in various '\n", "                                              'technologies.',\n", "                         'employee_id': 1,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 9,\n", "                         'level': 0,\n", "                         'location': 'London',\n", "                         'name': '<PERSON>',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '103k-208k',\n", "                         'skills': ['SQL'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 12 years of '\n", "                                       'experience specializing in Python, API '\n", "                                       'Development.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Python, API Development.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61d1'),\n", "                         'certifications': ['Certified Frontend Developer',\n", "                                            'Google Cloud Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Frontend '\n", "                                              'Developer based in Dubai with '\n", "                                              '12 years of experience. He '\n", "                                              'specializes in Python and API '\n", "                                              'development, holding '\n", "                                              'certifications in frontend '\n", "                                              'development and Google Cloud. '\n", "                                              '<PERSON> is skilled at delivering '\n", "                                              'results in frontend development '\n", "                                              'using his expertise in Python '\n", "                                              'and API development.',\n", "                         'employee_id': 72,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 12,\n", "                         'level': 0,\n", "                         'location': 'Dubai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '86k-158k',\n", "                         'skills': ['Python', 'API Development'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 7 years of '\n", "                                       'experience specializing in JavaScript, '\n", "                                       '<PERSON><PERSON><PERSON><PERSON>, Python.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in JavaScript, Kubernetes, Python.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6233'),\n", "                         'certifications': ['Oracle Certified',\n", "                                            'Google Cloud Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Backend '\n", "                                              'Developer in London with 7 '\n", "                                              'years of experience. He '\n", "                                              'specializes in JavaScript, '\n", "                                              '<PERSON><PERSON><PERSON><PERSON>, and Python for '\n", "                                              'frontend development. <PERSON> is '\n", "                                              'also certified in Oracle and '\n", "                                              'Google Cloud.',\n", "                         'employee_id': 170,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 7,\n", "                         'level': 0,\n", "                         'location': 'London',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '112k-227k',\n", "                         'skills': ['JavaScript', 'Kubernetes', 'Python'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'DevOps Engineer with 2 years of '\n", "                                       'experience specializing in React, Data '\n", "                                       'Analysis, CI/CD.',\n", "                         'Summary': '<PERSON> excels at delivering '\n", "                                    'results in frontend development while '\n", "                                    'leveraging skills in React, Data '\n", "                                    'Analysis, CI/CD.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61bc'),\n", "                         'certifications': ['AWS Certified'],\n", "                         'datapoint_summary': '<PERSON> is a DevOps '\n", "                                              'Engineer in Dubai with 2 years '\n", "                                              'of experience. He specializes '\n", "                                              'in React, Data Analysis, and '\n", "                                              'CI/CD, excelling in frontend '\n", "                                              'development. He is also AWS '\n", "                                              'certified.',\n", "                         'employee_id': 51,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 2,\n", "                         'level': 0,\n", "                         'location': 'Dubai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '140k-181k',\n", "                         'skills': ['React', 'Data Analysis', 'CI/CD'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 2 years of '\n", "                                       'experience specializing in SQL, Agile, '\n", "                                       'Data Analysis.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in SQL, Agile, Data Analysis.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c623f'),\n", "                         'certifications': ['Google Cloud Certified',\n", "                                            'Certified Frontend Developer',\n", "                                            'AWS Certified'],\n", "                         'datapoint_summary': '<PERSON> is a frontend '\n", "                                              'developer in Mumbai with 2 '\n", "                                              'years of experience. He '\n", "                                              'specializes in SQL, Agile, and '\n", "                                              'Data Analysis. He is certified '\n", "                                              'in frontend development and '\n", "                                              'cloud technologies. Hank is '\n", "                                              'known for delivering results in '\n", "                                              'frontend development.',\n", "                         'employee_id': 182,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 2,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '52k-179k',\n", "                         'skills': ['SQL', 'Agile', 'Data Analysis'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Data Scientist with 10 years of '\n", "                                       'experience specializing in HTML, Java.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in HTML, Java.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6227'),\n", "                         'certifications': ['Certified Data Scientist'],\n", "                         'datapoint_summary': '<PERSON> is a data scientist '\n", "                                              'in Mumbai with 10 years of '\n", "                                              'experience specializing in '\n", "                                              'frontend development with '\n", "                                              'expertise in HTML and Java.',\n", "                         'employee_id': 158,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 10,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Data Scientist',\n", "                         'salary_range': '77k-171k',\n", "                         'skills': ['HTML', 'Java'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 8 years of '\n", "                                       'experience specializing in React, Data '\n", "                                       '<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in React, Data Analysis, Agile, '\n", "                                    '<PERSON><PERSON>, <PERSON><PERSON>.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61d2'),\n", "                         'certifications': ['Oracle Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Backend '\n", "                                              'Developer based in New York '\n", "                                              'with 8 years of experience. He '\n", "                                              'specializes in frontend '\n", "                                              'development, with expertise in '\n", "                                              'React, Data Analysis, Agile, '\n", "                                              '<PERSON><PERSON>, and <PERSON><PERSON>. He is known '\n", "                                              'for delivering results and is '\n", "                                              'Oracle Certified.',\n", "                         'employee_id': 73,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 8,\n", "                         'level': 0,\n", "                         'location': 'New York',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '137k-225k',\n", "                         'skills': ['React',\n", "                                    'Data Analysis',\n", "                                    'Agile',\n", "                                    'Docker',\n", "                                    'Scrum'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 15 years of '\n", "                                       'experience specializing in Data '\n", "                                       'Analysis, HTML, CSS, JavaScript, SQL.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Data Analysis, HTML, CSS, '\n", "                                    'JavaScript, SQL.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61e5'),\n", "                         'certifications': ['Oracle Certified',\n", "                                            'AWS Certified',\n", "                                            'PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Backend '\n", "                                              'Developer in Toronto with 15 '\n", "                                              'years of experience and a '\n", "                                              'salary range of 85k-238k. She '\n", "                                              'specializes in frontend '\n", "                                              'development and has expertise '\n", "                                              'in Data Analysis, HTML, CSS, '\n", "                                              'JavaScript, and SQL. She is '\n", "                                              'also certified in Oracle, AWS, '\n", "                                              'and PM<PERSON>.',\n", "                         'employee_id': 92,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 15,\n", "                         'level': 0,\n", "                         'location': 'Toronto',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '85k-238k',\n", "                         'skills': ['Data Analysis',\n", "                                    'HTML',\n", "                                    'CSS',\n", "                                    'JavaScript',\n", "                                    'SQL'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 2 years of '\n", "                                       'experience specializing in Scrum, '\n", "                                       'Java, Python, CSS, Docker.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Scrum, Java, Python, CSS, '\n", "                                    'Docker.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61bb'),\n", "                         'certifications': ['AWS Certified',\n", "                                            'Google Cloud Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Frontend '\n", "                                              'Developer in Toronto with 2 '\n", "                                              'years of experience. He '\n", "                                              'specializes in Scrum, Java, '\n", "                                              'Python, CSS, and Docker, and '\n", "                                              'has certifications in AWS and '\n", "                                              'Google Cloud. Jack is '\n", "                                              'proficient in delivering '\n", "                                              'results in frontend development '\n", "                                              'utilizing his skills in various '\n", "                                              'technologies.',\n", "                         'employee_id': 50,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 2,\n", "                         'level': 0,\n", "                         'location': 'Toronto',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '145k-156k',\n", "                         'skills': ['<PERSON><PERSON>', '<PERSON>', 'Python', 'CSS', '<PERSON><PERSON>'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Project Manager with 8 years of '\n", "                                       'experience specializing in Data '\n", "                                       'Analysis, Python, JavaScript, SQL.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Data Analysis, Python, '\n", "                                    'JavaScript, SQL.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61c5'),\n", "                         'certifications': ['Google Cloud Certified'],\n", "                         'datapoint_summary': '<PERSON> is an experienced '\n", "                                              'Project Manager in Dubai with a '\n", "                                              'specialization in Data '\n", "                                              'Analysis, Python, JavaScript, '\n", "                                              'and SQL. She has 8 years of '\n", "                                              'experience and excels in '\n", "                                              'frontend development. '\n", "                                              'Additionally, she is <PERSON> '\n", "                                              'Cloud Certified.',\n", "                         'employee_id': 60,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 8,\n", "                         'level': 0,\n", "                         'location': 'Dubai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Project Manager',\n", "                         'salary_range': '94k-231k',\n", "                         'skills': ['Data Analysis',\n", "                                    'Python',\n", "                                    'JavaScript',\n", "                                    'SQL'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Project Manager with 7 years of '\n", "                                       'experience specializing in SQL, CI/CD, '\n", "                                       'Data Analysis.',\n", "                         'Summary': 'Grace Hall excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in SQL, CI/CD, Data Analysis.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c622f'),\n", "                         'certifications': ['Google Cloud Certified',\n", "                                            'Certified Data Scientist'],\n", "                         'datapoint_summary': 'Grace Hall is a Project Manager '\n", "                                              'in Mumbai with 7 years of '\n", "                                              'experience specializing in SQL, '\n", "                                              'CI/CD, and Data Analysis. She '\n", "                                              'excels in frontend development '\n", "                                              'and is skilled in SQL, CI/CD, '\n", "                                              'and Data Analysis. She is also '\n", "                                              'Google Cloud Certified and a '\n", "                                              'Certified Data Scientist.',\n", "                         'employee_id': 166,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 7,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': 'Grace Hall',\n", "                         'position': 'Project Manager',\n", "                         'salary_range': '97k-190k',\n", "                         'skills': ['SQL', 'CI/CD', 'Data Analysis'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'DevOps Engineer with 3 years of '\n", "                                       'experience specializing in Agile, '\n", "                                       'CI/CD, Java, API Development.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Agile, CI/CD, Java, API '\n", "                                    'Development.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6246'),\n", "                         'certifications': ['PMP Certified',\n", "                                            'Certified Data Scientist',\n", "                                            'AWS Certified'],\n", "                         'datapoint_summary': '<PERSON> is a DevOps '\n", "                                              'Engineer based in New York with '\n", "                                              '3 years of experience. He '\n", "                                              'specializes in Agile, CI/CD, '\n", "                                              'Java, and API Development with '\n", "                                              'a PMP, Certified Data '\n", "                                              'Scientist, and AWS '\n", "                                              'certifications. <PERSON> excels in '\n", "                                              'delivering results in frontend '\n", "                                              'development.',\n", "                         'employee_id': 189,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 3,\n", "                         'level': 0,\n", "                         'location': 'New York',\n", "                         'name': '<PERSON>',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '96k-202k',\n", "                         'skills': ['Agile',\n", "                                    'CI/CD',\n", "                                    'Java',\n", "                                    'API Development'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'DevOps Engineer with 12 years of '\n", "                                       'experience specializing in HTML, '\n", "                                       'React.',\n", "                         'Summary': '<PERSON> excels at delivering '\n", "                                    'results in frontend development while '\n", "                                    'leveraging skills in HTML, React.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61cc'),\n", "                         'certifications': ['Certified Frontend Developer',\n", "                                            'Certified Data Scientist'],\n", "                         'datapoint_summary': '<PERSON> is a DevOps '\n", "                                              'Engineer in Toronto with 12 '\n", "                                              'years of experience. He '\n", "                                              'specializes in HTML and React '\n", "                                              'for frontend development, '\n", "                                              'holding certifications in both '\n", "                                              'areas. <PERSON> is skilled in '\n", "                                              'delivering results in frontend '\n", "                                              'development.',\n", "                         'employee_id': 67,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 12,\n", "                         'level': 0,\n", "                         'location': 'Toronto',\n", "                         'name': '<PERSON>',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '104k-204k',\n", "                         'skills': ['HTML', 'React'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 15 years of '\n", "                                       'experience specializing in Kubernetes, '\n", "                                       'CI/CD.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Kubernetes, CI/CD.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c621f'),\n", "                         'certifications': ['Certified Data Scientist'],\n", "                         'datapoint_summary': '<PERSON> is a part-time '\n", "                                              'Backend Developer in Tokyo with '\n", "                                              '15 years of experience, earning '\n", "                                              'between 64k-165k. He '\n", "                                              'specializes in Kubernetes and '\n", "                                              'CI/CD, and is also skilled in '\n", "                                              'frontend development. <PERSON> is a '\n", "                                              'Certified Data Scientist who '\n", "                                              'excels at delivering results.',\n", "                         'employee_id': 150,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 15,\n", "                         'level': 0,\n", "                         'location': 'Tokyo',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '64k-165k',\n", "                         'skills': ['Kubernetes', 'CI/CD'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 12 years of '\n", "                                       'experience specializing in React, '\n", "                                       'JavaScript, Docker, CI/CD, API '\n", "                                       'Development.',\n", "                         'Summary': '<PERSON> excels at delivering results in '\n", "                                    'frontend development while leveraging '\n", "                                    'skills in React, JavaScript, Docker, '\n", "                                    'CI/CD, API Development.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c618f'),\n", "                         'certifications': ['Oracle Certified'],\n", "                         'datapoint_summary': 'Hank <PERSON> is a frontend '\n", "                                              'developer in San Francisco with '\n", "                                              '12 years of experience. He '\n", "                                              'specializes in React, '\n", "                                              'JavaScript, Docker, CI/CD, and '\n", "                                              'API development, excelling at '\n", "                                              'delivering results in frontend '\n", "                                              'development. <PERSON> is also '\n", "                                              'Oracle Certified.',\n", "                         'employee_id': 6,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 12,\n", "                         'level': 0,\n", "                         'location': 'San Francisco',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '109k-205k',\n", "                         'skills': ['React',\n", "                                    'JavaScript',\n", "                                    'Docker',\n", "                                    'CI/CD',\n", "                                    'API Development'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'DevOps Engineer with 6 years of '\n", "                                       'experience specializing in Machine '\n", "                                       'Learning, Data Analysis, API '\n", "                                       'Development, Python, CSS.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Machine Learning, Data '\n", "                                    'Analysis, API Development, Python, CSS.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c623a'),\n", "                         'certifications': ['Google Cloud Certified',\n", "                                            'Certified Frontend Developer'],\n", "                         'datapoint_summary': '<PERSON> is a part-time '\n", "                                              'Dev<PERSON>ps Engineer in Mumbai with '\n", "                                              '6 years of experience and a '\n", "                                              'salary ranging from 136k-219k. '\n", "                                              'She specializes in frontend '\n", "                                              'development, Machine Learning, '\n", "                                              'Data Analysis, API Development, '\n", "                                              'Python, and CSS. Alice is '\n", "                                              'proficient in Google Cloud and '\n", "                                              'a Certified Frontend Developer.',\n", "                         'employee_id': 177,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 6,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '136k-219k',\n", "                         'skills': ['Machine Learning',\n", "                                    'Data Analysis',\n", "                                    'API Development',\n", "                                    'Python',\n", "                                    'CSS'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Data Scientist with 8 years of '\n", "                                       'experience specializing in Python, API '\n", "                                       'Development.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Python, API Development.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61d9'),\n", "                         'certifications': ['Certified Frontend Developer',\n", "                                            'PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Data '\n", "                                              'Scientist in Berlin with 8 '\n", "                                              'years of experience. She '\n", "                                              'specializes in Python and API '\n", "                                              'Development and is skilled in '\n", "                                              'Frontend Development. She is '\n", "                                              'certified as a Frontend '\n", "                                              'Developer and has a PMP '\n", "                                              'certification.',\n", "                         'employee_id': 80,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 8,\n", "                         'level': 0,\n", "                         'location': 'Berlin',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Data Scientist',\n", "                         'salary_range': '123k-217k',\n", "                         'skills': ['Python', 'API Development'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Project Manager with 4 years of '\n", "                                       'experience specializing in Java, '\n", "                                       'Agile, <PERSON>rum.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Java, Agile, Scrum.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61c6'),\n", "                         'certifications': ['Certified Data Scientist',\n", "                                            'PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Project Manager '\n", "                                              'in New York with 4 years of '\n", "                                              'experience in frontend '\n", "                                              'development. He specializes in '\n", "                                              'Java, Agile, and Scrum, and '\n", "                                              'holds certifications in data '\n", "                                              'science and project management. '\n", "                                              '<PERSON> excels in delivering '\n", "                                              'results in his projects.',\n", "                         'employee_id': 61,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 4,\n", "                         'level': 0,\n", "                         'location': 'New York',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Project Manager',\n", "                         'salary_range': '113k-245k',\n", "                         'skills': ['Java', 'Agile', 'Scrum'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Data Scientist with 1 years of '\n", "                                       'experience specializing in Data '\n", "                                       'Analysis, Kubernetes, Scrum.',\n", "                         'Summary': '<PERSON> excels at delivering results in '\n", "                                    'frontend development while leveraging '\n", "                                    'skills in Data Analysis, Kubernetes, '\n", "                                    '<PERSON><PERSON>.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61ed'),\n", "                         'certifications': ['PMP Certified',\n", "                                            'Certified Data Scientist'],\n", "                         'datapoint_summary': '<PERSON> is a Data Scientist in '\n", "                                              'Tokyo with 1 year of '\n", "                                              'experience. He specializes in '\n", "                                              'Data Analysis, Kubernetes, and '\n", "                                              '<PERSON><PERSON>, excelling in frontend '\n", "                                              'development. <PERSON> is also PMP '\n", "                                              'Certified and a Certified Data '\n", "                                              'Scientist.',\n", "                         'employee_id': 100,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 1,\n", "                         'level': 0,\n", "                         'location': 'Tokyo',\n", "                         'name': 'Bob Hall',\n", "                         'position': 'Data Scientist',\n", "                         'salary_range': '86k-212k',\n", "                         'skills': ['Data Analysis', 'Kubernetes', 'Scrum'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Dev<PERSON>ps Engineer with 11 years of '\n", "                                       'experience specializing in Data '\n", "                                       'Analysis, Scrum, Java.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Data Analysis, Scrum, Java.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6234'),\n", "                         'certifications': ['Oracle Certified'],\n", "                         'datapoint_summary': '<PERSON> is a DevOps '\n", "                                              'Engineer in Berlin with 11 '\n", "                                              'years of experience, '\n", "                                              'specializing in Data Analysis, '\n", "                                              'Scrum, and Java. He is skilled '\n", "                                              'in frontend development and '\n", "                                              'holds an Oracle certification.',\n", "                         'employee_id': 171,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 11,\n", "                         'level': 0,\n", "                         'location': 'Berlin',\n", "                         'name': '<PERSON>',\n", "                         'position': 'DevOps Engineer',\n", "                         'salary_range': '63k-221k',\n", "                         'skills': ['Data Analysis', 'Scrum', 'Java'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 3 years of '\n", "                                       'experience specializing in React, '\n", "                                       'Java, Machine Learning.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in React, Java, Machine Learning.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6214'),\n", "                         'certifications': ['Google Cloud Certified',\n", "                                            'Certified Data Scientist',\n", "                                            'AWS Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Frontend '\n", "                                              'Developer based in Tokyo with 3 '\n", "                                              'years of experience. She '\n", "                                              'specializes in React, Java, and '\n", "                                              'Machine Learning, and holds '\n", "                                              'certifications in Google Cloud, '\n", "                                              'Data Science, and AWS. Grace '\n", "                                              'excels in delivering frontend '\n", "                                              'development results utilizing '\n", "                                              'her skills in React, Java, and '\n", "                                              'Machine Learning.',\n", "                         'employee_id': 139,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 3,\n", "                         'level': 0,\n", "                         'location': 'Tokyo',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '85k-184k',\n", "                         'skills': ['React', 'Java', 'Machine Learning'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Data Scientist with 9 years of '\n", "                                       'experience specializing in Agile, '\n", "                                       'Kubernetes, React, JavaScript, Scrum.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Agile, <PERSON><PERSON><PERSON><PERSON>, React, '\n", "                                    'JavaScript, Scrum.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61e2'),\n", "                         'certifications': ['Oracle Certified',\n", "                                            'PMP Certified',\n", "                                            'Google Cloud Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Data Scientist '\n", "                                              'in Berlin with 9 years of '\n", "                                              'experience specializing in '\n", "                                              '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, '\n", "                                              'JavaScript, and Scrum. She is '\n", "                                              'skilled in frontend development '\n", "                                              'and holds certifications in '\n", "                                              'Oracle, PMP, and Google Cloud.',\n", "                         'employee_id': 89,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 9,\n", "                         'level': 0,\n", "                         'location': 'Berlin',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Data Scientist',\n", "                         'salary_range': '92k-234k',\n", "                         'skills': ['Agile',\n", "                                    'Kubernetes',\n", "                                    'React',\n", "                                    'JavaScript',\n", "                                    'Scrum'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Frontend Developer with 6 years of '\n", "                                       'experience specializing in Agile, CSS.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Agile, CSS.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6216'),\n", "                         'certifications': ['Google Cloud Certified',\n", "                                            'Certified Data Scientist',\n", "                                            'PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Frontend '\n", "                                              '<PERSON><PERSON><PERSON> in Mumbai with 6 '\n", "                                              'years of experience. She '\n", "                                              'specializes in Agile and CSS, '\n", "                                              'demonstrating proficiency with '\n", "                                              'Google Cloud certification, '\n", "                                              'Data Science certification, and '\n", "                                              'PMP certification. She excels '\n", "                                              'in delivering results in '\n", "                                              'frontend development.',\n", "                         'employee_id': 141,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 6,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Frontend Developer',\n", "                         'salary_range': '117k-195k',\n", "                         'skills': ['Agile', 'CSS'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Data Scientist with 12 years of '\n", "                                       'experience specializing in API '\n", "                                       'Development, Scrum, Kubernetes.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in API Development, Scrum, '\n", "                                    'Kubernet<PERSON>.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6247'),\n", "                         'certifications': ['Oracle Certified',\n", "                                            'Certified Frontend Developer'],\n", "                         'datapoint_summary': '<PERSON> is a part-time Data '\n", "                                              'Scientist in Paris with a '\n", "                                              'salary range of 123k-222k. He '\n", "                                              'specializes in frontend '\n", "                                              'development, with skills in API '\n", "                                              'Development, Scrum, and '\n", "                                              '<PERSON><PERSON><PERSON><PERSON>. He has 12 years of '\n", "                                              'experience and is certified in '\n", "                                              'frontend development and by '\n", "                                              'Oracle.',\n", "                         'employee_id': 190,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 12,\n", "                         'level': 0,\n", "                         'location': 'Paris',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Data Scientist',\n", "                         'salary_range': '123k-222k',\n", "                         'skills': ['API Development', 'Scrum', 'Kubernetes'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Data Scientist with 1 years of '\n", "                                       'experience specializing in API '\n", "                                       'Development, React, Machine Learning, '\n", "                                       '<PERSON>er, Java.',\n", "                         'Summary': '<PERSON> excels at delivering results in '\n", "                                    'frontend development while leveraging '\n", "                                    'skills in API Development, React, Machine '\n", "                                    'Learning, <PERSON><PERSON>, Java.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61ef'),\n", "                         'certifications': ['PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Data Scientist '\n", "                                              'with 1 year of experience based '\n", "                                              'in Mumbai. He specializes in '\n", "                                              'API Development, React, Machine '\n", "                                              'Learning, Docker, and Java, '\n", "                                              'with expertise in frontend '\n", "                                              'development. He is PMP '\n", "                                              'certified and excels in '\n", "                                              'delivering results in his '\n", "                                              'field.',\n", "                         'employee_id': 102,\n", "                         'employment_status': 'Full-Time',\n", "                         'experience_years': 1,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Data Scientist',\n", "                         'salary_range': '80k-162k',\n", "                         'skills': ['API Development',\n", "                                    'React',\n", "                                    'Machine Learning',\n", "                                    'Docker',\n", "                                    'Java'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 4 years of '\n", "                                       'experience specializing in SQL, Java.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in SQL, Java.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c6226'),\n", "                         'certifications': ['PMP Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Backend '\n", "                                              'Dev<PERSON>per in Mumbai with 4 '\n", "                                              'years of experience in SQL and '\n", "                                              'Java. He is part-time and earns '\n", "                                              'between 95k-222k. <PERSON> is also '\n", "                                              'PMP certified and excels in '\n", "                                              'frontend development.',\n", "                         'employee_id': 157,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 4,\n", "                         'level': 0,\n", "                         'location': 'Mumbai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '95k-222k',\n", "                         'skills': ['SQL', 'Java'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Backend Developer with 14 years of '\n", "                                       'experience specializing in Python, '\n", "                                       'Data Analysis, Java, API Development.',\n", "                         'Summary': '<PERSON> excels at delivering results '\n", "                                    'in frontend development while leveraging '\n", "                                    'skills in Python, Data Analysis, Java, '\n", "                                    'API Development.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c618c'),\n", "                         'certifications': ['Google Cloud Certified'],\n", "                         'datapoint_summary': '<PERSON> is a Backend '\n", "                                              'Dev<PERSON><PERSON> in Dubai with 14 '\n", "                                              'years of experience. She '\n", "                                              'specializes in Python, Data '\n", "                                              'Analysis, Java, and API '\n", "                                              'Development. <PERSON> also has '\n", "                                              'expertise in Frontend '\n", "                                              'Development and is Google Cloud '\n", "                                              'Certified. She excels in '\n", "                                              'delivering results in both '\n", "                                              'frontend and backend '\n", "                                              'development.',\n", "                         'employee_id': 3,\n", "                         'employment_status': 'Contract',\n", "                         'experience_years': 14,\n", "                         'level': 0,\n", "                         'location': 'Dubai',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Backend Developer',\n", "                         'salary_range': '140k-179k',\n", "                         'skills': ['Python',\n", "                                    'Data Analysis',\n", "                                    'Java',\n", "                                    'API Development'],\n", "                         'team': 'Frontend Development'},\n", "                        {'Experience': 'Project Manager with 3 years of '\n", "                                       'experience specializing in HTML, '\n", "                                       'Python.',\n", "                         'Summary': '<PERSON> excels at delivering '\n", "                                    'results in frontend development while '\n", "                                    'leveraging skills in HTML, Python.',\n", "                         '_id': ObjectId('67483f7c75040e5ce95c61d3'),\n", "                         'certifications': ['Google Cloud Certified',\n", "                                            'Certified Frontend Developer'],\n", "                         'datapoint_summary': '<PERSON> is a Project '\n", "                                              'Manager in New York with 3 '\n", "                                              'years of experience '\n", "                                              'specializing in HTML and '\n", "                                              'Python. She is skilled in '\n", "                                              'frontend development and holds '\n", "                                              'certifications in Google Cloud '\n", "                                              'and Frontend Development. '\n", "                                              '<PERSON> is known for delivering '\n", "                                              'results efficiently in her '\n", "                                              'projects.',\n", "                         'employee_id': 74,\n", "                         'employment_status': 'Part-Time',\n", "                         'experience_years': 3,\n", "                         'level': 0,\n", "                         'location': 'New York',\n", "                         'name': '<PERSON>',\n", "                         'position': 'Project Manager',\n", "                         'salary_range': '90k-203k',\n", "                         'skills': ['HTML', 'Python'],\n", "                         'team': 'Frontend Development'}],\n", "  'salary_range': '103k-208k',\n", "  'skills': ['SQL'],\n", "  'team': 'Frontend Development'}]\n"]}], "source": ["import pprint\n", "\n", "pprint.pprint(result)"]}, {"cell_type": "markdown", "metadata": {"id": "-yqYt0l7_Ha6"}, "source": ["## Naive/Baseline RAG"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "B7lFi4OrB1QA"}, "outputs": [], "source": ["import time\n", "\n", "from pymongo.operations import SearchIndexModel\n", "\n", "\n", "def setup_vector_search_index(collection, index_definition, index_name=\"vector_index\"):\n", "    \"\"\"\n", "    Setup a vector search index for a MongoDB collection and wait for 30 seconds.\n", "\n", "    Args:\n", "    collection: MongoDB collection object\n", "    index_definition: Dictionary containing the index definition\n", "    index_name: Name of the index (default: \"vector_index\")\n", "    \"\"\"\n", "    new_vector_search_index_model = SearchIndexModel(\n", "        definition=index_definition, name=index_name, type=\"vectorSearch\"\n", "    )\n", "\n", "    # Create the new index\n", "    try:\n", "        result = collection.create_search_index(model=new_vector_search_index_model)\n", "        print(f\"Creating index '{index_name}'...\")\n", "\n", "        # Sleep for 30 seconds\n", "        print(f\"Waiting for 30 seconds to allow index '{index_name}' to be created...\")\n", "        time.sleep(30)\n", "\n", "        print(f\"30-second wait completed for index '{index_name}'.\")\n", "        return result\n", "\n", "    except Exception as e:\n", "        print(f\"Error creating new vector search index '{index_name}': {e!s}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ucB268IvRs2b"}, "outputs": [], "source": ["vector_search_index_definition = {\n", "    \"fields\": [\n", "        {\n", "            \"type\": \"vector\",\n", "            \"path\": \"embedding\",\n", "            \"numDimensions\": OPENAI_EMBEDDING_MODEL_DIMENSION,\n", "            \"similarity\": \"cosine\",\n", "        }\n", "    ]\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 87}, "id": "dN7npkaiR5ec", "outputId": "e1d481c1-895a-442d-861d-2ae57d174c7a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating index 'vector_index'...\n", "Waiting for 30 seconds to allow index 'vector_index' to be created...\n", "30-second wait completed for index 'vector_index'.\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'vector_index'"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["setup_vector_search_index(\n", "    collection, vector_search_index_definition, index_name=\"vector_index\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rKqaidU7_G9y"}, "outputs": [], "source": ["def vector_search(user_query, collection, vector_search_index_name=\"vector_index\"):\n", "    \"\"\"\n", "    Perform a vector search in the MongoDB collection based on the user query.\n", "\n", "    Args:\n", "    user_query (str): The user's query string.\n", "    collection (MongoCollection): The MongoDB collection to search.\n", "    additional_stages (list): Additional aggregation stages to include in the pipeline.\n", "    vector_search_index_name (str): The name of the vector search index.\n", "\n", "    Returns:\n", "    list: A list of matching documents.\n", "    \"\"\"\n", "\n", "    # Generate embedding for the user query\n", "    query_embedding = get_embedding(user_query)\n", "\n", "    if query_embedding is None:\n", "        return \"Invalid query or embedding generation failed.\"\n", "\n", "    # Define the vector search stage\n", "    vector_search_stage = {\n", "        \"$vectorSearch\": {\n", "            \"index\": \"vector_index\",\n", "            \"queryVector\": query_embedding,\n", "            \"path\": \"embedding\",\n", "            \"numCandidates\": 150,  # Number of candidate matches to consider\n", "            \"limit\": 5,  # Return top 5 matches\n", "        }\n", "    }\n", "\n", "    project_stage = {\n", "        \"$project\": {\n", "            \"embedding\": 0,  # Remove embedding from top-level documents\n", "            \"skills\": 0,  # Remove skills from results\n", "            \"certifications\": 0,  # Remove certifications from results\n", "            \"Summary\": 0,  # Remove summary from results\n", "        }\n", "    }\n", "\n", "    # Define the aggregate pipeline with the vector search stage and additional stages\n", "    pipeline = [vector_search_stage, project_stage]\n", "\n", "    # Execute the search\n", "    results = collection.aggregate(pipeline)\n", "    return list(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FWpLrXncAYkG"}, "outputs": [], "source": ["def handle_user_query_naive_rag(query: str):\n", "    results = vector_search(query, collection)\n", "\n", "    # Pass query and results as input to openai\n", "    if results:\n", "        context = results\n", "\n", "        response = openai.chat.completions.create(\n", "            model=\"gpt-4o\",\n", "            messages=[\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": \"You are an expert in recommending teams based on employee data. Consider the provided employee data to answer the user's query. If the data is not sufficient to answer the query, simply state that you need more information.\",\n", "                },\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": f\"Here's the user's query: {query}\\n\\nHere's some potentially relevant employee data: {context}\",\n", "                },\n", "            ],\n", "        )\n", "\n", "        # Extract and return the OpenAI's response\n", "        return response.choices[0].message.content\n", "    return \"No relevant employees found for this query.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lit6f4EMAfVO"}, "outputs": [], "source": ["query = (\n", "    \"Get me employees that can form a team to build a website for HR recruitement firm\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "XJzgVaFkAgWi"}, "outputs": [], "source": ["naive_rag_results = handle_user_query_naive_rag(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZdfYyaNIAsd1", "outputId": "8a6aa35c-af02-411b-eb40-6c7a31cd8072"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Based on the provided employee data, I can suggest a team to build a website for an HR recruitment firm. A typical website development team might require roles such as a frontend developer, a backend developer, and a project manager.\n", "\n", "1. **Frontend Developers**:\n", "   - **<PERSON> Hall**: With 15 years of experience, <PERSON> specializes in React and CSS, which are crucial for frontend development.\n", "   - **<PERSON>**: With 11 years of experience, specializing in Data Analysis and Agile methods, can contribute to the frontend design and functionality, especially in implementing agile practices.\n", "   - **<PERSON>**: Although less experienced, he brings skills in SQL, Agile, and Data Analysis, which can be valuable for integrating frontend functionalities with data operations.\n", "\n", "2. **Project Manager**:\n", "   - **<PERSON>**: With 7 years of experience and skills in SQL, CI/CD, and Data Analysis, <PERSON> can manage the project effectively, ensuring seamless communication and integration in a cross-functional team.\n", "\n", "3. **Possible DevOps Contribution**:\n", "   - **<PERSON>**: With 12 years of experience in HTML and React, <PERSON> can offer support in both the frontend aspect and the continuous integration/deployment process, which is essential for maintaining and deploying a website.\n", "\n", "While the roles of a backend developer or a UI/UX designer are not specifically covered by the provided profiles, the team can possibly look for external consultants or additional team members specializing in those areas to ensure comprehensive coverage of all aspects required for building a website. If more detailed profiles for backend services or UI/UX design are available, they should be considered to complement the team adequately.\n"]}], "source": ["print(naive_rag_results)"]}, {"cell_type": "markdown", "metadata": {"id": "d8BPktACIWyd"}, "source": ["## GraphRAG"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-WDBmvK8R_0O"}, "outputs": [], "source": ["def customGraphRAG(text: str):\n", "    \"\"\"\n", "    Performs a custom GraphRAG operation by conducting a vector search\n", "    followed by graph traversal using graphLookup.\n", "\n", "    Args:\n", "        text (str): The query text.\n", "\n", "    Returns:\n", "        list: A list of documents containing the relevant results.\n", "    \"\"\"\n", "\n", "    # Step 1: Generate the embedding for the query text\n", "    query_embedding = get_embedding(text)\n", "\n", "    # Step 2: Define the vector search pipeline\n", "    vector_search_stage = {\n", "        \"$vectorSearch\": {\n", "            \"index\": \"vector_index\",\n", "            \"queryVector\": query_embedding,\n", "            \"path\": \"embedding\",\n", "            \"numCandidates\": 150,  # Number of candidate matches to consider\n", "            \"limit\": 1,  # Return top 5 matches\n", "        }\n", "    }\n", "\n", "    # Step 3: Define the graph traversal pipeline\n", "    graph_lookup_stage = {\n", "        \"$graphLookup\": {\n", "            \"from\": \"employees\",  # Collection to perform graph traversal on\n", "            \"startWith\": \"$skills\",  # Start the traversal using skills field\n", "            \"connectFromField\": \"skills\",  # Field in the current document to match\n", "            \"connectToField\": \"skills\",  # Field in the other documents to match\n", "            \"as\": \"related_employees\",  # Output field for connected documents\n", "            \"maxDepth\": 2,  # Depth of graph traversal\n", "            \"depthField\": \"level\",  # Include recursion level in results\n", "        }\n", "    }\n", "\n", "    # Step 4: Exclude embeddings from the output (optional cleanup)\n", "    project_stage = {\n", "        \"$project\": {\n", "            \"_id\": 1,\n", "            \"embedding\": 0,  # Remove embedding from top-level documents\n", "            \"related_employees.embedding\": 0,  # Remove embedding from nested results\n", "            \"related_employees.skills\": 0,  # Remove skills from nested results\n", "            \"related_employees.certifications\": 0,  # Remove certifications from nested results\n", "            \"related_employees.Summary\": 0,  # Remove summary from nested results\n", "        }\n", "    }\n", "\n", "    # Step 5: Combine the stages into a pipeline\n", "    pipeline = [\n", "        vector_search_stage,  # Perform vector search\n", "        graph_lookup_stage,  # Conduct graph traversal\n", "        project_stage,  # Clean up unnecessary fields\n", "    ]\n", "\n", "    # Step 6: Execute the aggregation pipeline\n", "    try:\n", "        result = list(collection.aggregate(pipeline))\n", "        return result\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e!s}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HGmfhE-oTigJ"}, "outputs": [], "source": ["results = customGraphRAG(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VGfmEAJoTmIC", "outputId": "7798ac69-c868-4695-b5c3-7dd8d583375c"}, "outputs": [], "source": ["pprint.pprint(results)"]}, {"cell_type": "markdown", "metadata": {"id": "Kdh1beOteR9h"}, "source": ["### Prompt Compression Technique\n", "Because GraphRAG can be expensive, token wise"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cOSIWr4recDQ"}, "outputs": [], "source": ["!pip install --quiet -U llmlingua"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 177, "referenced_widgets": ["ef76796a74524a3ab2ce3dbdfa2f6607", "75693e54f31d40db86ffc8edd33aca6a", "5b4f1afdf393448fb48c66c3a5933c4e", "1e28312482aa438cb0c1be48aff0b9c8", "f224ad85c34446d895a5755252fa610d", "8fdc8055ea2746768ac65ba3c9cd6597", "6bc26451d0574b1ba6284a381ba8dc5f", "aa10c7dc2aec425f8a9629e199d481ec", "88f0ac2426fd472bba67e008430cde7f", "81c468f6fcfa423a929735fb3c345d07", "a7caa1ff307242b394314da780888050", "a2975615bfc24bf1bfc11e097c3a00a4", "1244ba5cd262417e9862d5069d831f8f", "4a6fcdb5bc8745c182b63522834fdc8f", "453296eb227c4d4abfc8d55207064d15", "f7de6fa5eb384cc18a2b50bdd28a317a", "adbc801a29b9434abb3b806d449fbf26", "2b4c1a424b92417588a70ff8229ee713", "6b8ea812551747f0a35c7b6c0788d106", "b10f3b0a36494b84afbff796d27260ff", "f396b35d0acc4c6e939a6824fad8d89b", "3225ba9395a2452e9f7725f16edd89d7", "4112dabb3d954a18a811e8ab3f098044", "5c5dd5e3860947dea65245480451393e", "6233ba1e2a8b4504952a9b5a195a43a3", "0bb9d11818a4472f875705bdaed06d68", "a16e36453aa044428e4394472e1b4441", "1f117a8d54be4fdba773e77591860121", "93e93cd065154f5fa4d3af5c2edb8d02", "70f7184cf8ca481a9ee95e33abb460d9", "156da873a66145fcbdf7beebdca9debd", "43979fb65685498db2171f6f8abf2e75", "ed27b8bbe8c34e46899cc23d3adcd672", "d2c1bcea6bf34ccbbb44f8fe34cdd041", "f047b58926ce48ea89255cb9e9f6116a", "0f2688247ce041d4800bd356425040e9", "ebeeac91e5eb4aac9935231492a48778", "623428b4a26844dd88a1c40c95a6d473", "b653001bc9744c0590db8a15446a88fb", "3d17792b4ee940c08c030dc47cd6165c", "aabf33463a8246e9b70af6501088410c", "9107c04abbe6405bb1ce18480fe39973", "1b0f9af8283641188c745c87a0a8b65e", "3b364d8e875a452ca4272c418e274d1c", "629f2cb96af842189c0f195dff491643", "20180ec7781d468b93048b5275edbcbe", "a47dd5434f5f437c9137b89624cdf1e2", "18c3ff72df3349a6b9203e46ea19be0a", "7352ad0338384d78b072992a234193da", "9afc784fb504492a97d2aa5d03490be3", "ce37479f2f0b4c6aa801170bf25ac4f8", "ecac637bd0184e9abd0c676ab89faa65", "fe37f3ab3769471bafd6825b76176906", "33549d0ba1974a2db0b2e606ecf8c55b", "6515e09f208549bf828acf4c431a0657"]}, "id": "YqpblBSieRHO", "outputId": "e114403e-ac37-4f2c-b0aa-2d1aa68e5346"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef76796a74524a3ab2ce3dbdfa2f6607", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/752 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a2975615bfc24bf1bfc11e097c3a00a4", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/1.15k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4112dabb3d954a18a811e8ab3f098044", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.1M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d2c1bcea6bf34ccbbb44f8fe34cdd041", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/280 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "629f2cb96af842189c0f195dff491643", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/2.24G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from llmlingua import PromptCompressor\n", "\n", "# https://github.com/microsoft/LLMLingua\n", "llm_lingua = PromptCompressor(\n", "    model_name=\"microsoft/llmlingua-2-xlm-roberta-large-meetingbank\",  # Smaller Model: microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank\n", "    use_llmlingua2=True,\n", "    device_map=\"cpu\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LMAn30YGWIRS"}, "outputs": [], "source": ["def handle_user_query(query: str, compress_prompt=False):\n", "    results = customGraphRAG(query)\n", "\n", "    # Pass query and results as input to openai\n", "    if results and results[0].get(\"related_employees\"):\n", "        context = results[0][\"related_employees\"]\n", "\n", "        prompt = f\"Here's the user's query: {query}\\n\\nHere's some potentially relevant employee data: {context}\"\n", "\n", "        if compress_prompt:\n", "            compression_result = llm_lingua.compress_prompt(\n", "                prompt, rate=0.20, force_tokens=[\"\\n\", \"?\"]\n", "            )\n", "            # Uncomment below to see the compression results\n", "            # pprint.pprint(compression_result)\n", "            final_prompt = compression_result[\"compressed_prompt\"]\n", "            print(f\"Compressed prompt: {final_prompt}\")\n", "            print()\n", "        else:\n", "            final_prompt = prompt\n", "\n", "        response = openai.chat.completions.create(\n", "            model=\"gpt-4o\",\n", "            messages=[\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": \"You are an expert in recommending teams based on employee data. Consider the provided employee data to answer the user's query.\",\n", "                },\n", "                {\"role\": \"user\", \"content\": final_prompt},\n", "            ],\n", "        )\n", "\n", "        # Extract and return the OpenAI's response\n", "        return response.choices[0].message.content\n", "    return \"No relevant employees found for this query.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8vsB-fzaWXwV", "outputId": "ab8b7b3b-8744-4ab8-bdfe-be6fafeb6fd6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["To form a team to build a website for an HR recruitment firm, you would ideally look for a mix of frontend developers, backend developers, a project manager, and potentially a data scientist or DevOps engineer for infrastructure and data needs. Here are some recommended candidates from the provided data:\n", "\n", "1. **<PERSON>** - Project Manager (Dubai)\n", "   - Experience: 7 years in project management\n", "   - Skills: Specializing in CSS, JavaScript\n", "   - Team: Project Management\n", "\n", "2. **<PERSON>** - <PERSON><PERSON> (Toronto)\n", "   - Experience: 11 years\n", "   - Skills: Agile, CSS, JavaScript, Data Analysis\n", "   - Team: Project Management\n", "\n", "3. **<PERSON>** - <PERSON><PERSON> (Sydney)\n", "   - Experience: 5 years\n", "   - Skills: React, SQL, Kubernetes\n", "   - Team: Project Management\n", "\n", "4. **<PERSON>** - <PERSON><PERSON> (Berlin)\n", "   - Experience: 6 years\n", "   - Skills: Machine Learning, HTML, CSS, Agile, CI/CD\n", "   - Team: Frontend Development\n", "\n", "5. **<PERSON>** - Project Manager (London)\n", "   - Experience: 13 years\n", "   - Skills: React, API Development, SQL\n", "   - Team: Project Management\n", "\n", "6. **<PERSON>** - <PERSON><PERSON> (Berlin)\n", "   - Experience: 11 years\n", "   - Skills: CSS, Agile, JavaScript, Data Analysis\n", "   - Team: AI & Analytics\n", "\n", "These candidates bring a diverse skill set that includes technical expertise in both frontend and backend development, along with strong project management skills. This combination should be well-suited to build an effective and efficient website for an HR recruitment firm.\n", "\n", "If you need more specific criteria or have different requirements, please provide additional details.\n"]}], "source": ["results = handle_user_query(query)\n", "\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OQV-5zUNWb6O", "outputId": "a807d1ad-fbb6-40af-8634-62f43ea2f86b"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (19789 > 512). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Compressed prompt: user query employees website HR\n", "\n", " employee data Brown Engineer York_years 3-Time-206k 3 Data Analysis Python Docker Brown Data Analysis Python Docker CSS Oracle Google Cloud Certified Python Docker CSS annual salary 150k to 206k Brown Developer 11-Time-173k Management Developer 11 Agile CSS SQL JavaScriptskilled Project Management Certified Frontend Developer PMP Certified results Wilson Scientist 9 Development Agile Kubernetes React JavaScript Scrum Wilson Scientist Agile Kubernetes React JavaScript Scrum frontend development certifications Oracle PMP Google Cloud Wilson Manager 7-Time-177k Management 7 CSS JavaScript resultsHall Developer 5-Time-179k Management Developer 5 years React SQL Kubernetes Part-Time Backend Developer 5 React SQL Kubernetes project Frontend Developer AWS Certified Johnson Developer 15-152k 15 CSS JavaScript Agile CSS JavaScript Agile PMP Certified Google Cloud CertifiedDavis Manager York 14-Time-202k Development 14 Data Analysis React Machine Learning CSS Davis 14 frontend Data Analysis React Machine Learning CSS AWS salary 55k-202k Miller Developer 13, Services Developer CSS Scrum 13 CSS Scrum backend certified Oracle PMPWilson Developer Analytics CSS Java Agile Python React CSS Java Agile Python React certifications Oracle Data Science Frontend Development Johnson Scientist 7-Time 7 Python Scrum Machine Learning CSS Data Scientist 7 Paris Python Scrum Machine Learning CSS HTML Oracle Certified Frontend DeveloperBrown Developer 7-Time Development CSS CI/CD Java HTML Kubernetes CSS CI/CD Java HTML Kubernetes frontend PMP certified Smith Developer 6 Analytics 6 React Scrum Data Analysis 6 React Scrum Data Analysis AI Analytics Data Scientist AWS Certified Frontend DeveloperClark Developer 15-Time Services Data Analysis SQL Python Kubernetes CSS Clark Data Analysis SQL Python Kubernetes CSS certifications Frontend Developer Data Scientist Miller Developer 3-Time-158k Development Developer 3 years Scrum React Python Scrum React Python certifications PMP AWSMiller Engineer Francisco 14 Management 14 Machine Learning CSS HTML Kubernetes Machine Learning CSS HTML Kubernetes project management Certified Data Scientist 187 Davis Manager 8-Time Management 8 years Agile React Project Agile React certifications AWS PMP project management resultsBrown Manager 15-Time Analytics Manager 15 React SQL Scrum Analytics React SQL Scrum certifications Oracle Google Cloud AI Analytics Miller Developer 3-Time-189k Developer 3 years CSS Scrum Miller Part-Time Backend Developer 3 CSS Scrum Certified Data Scientist AWSBrown Developer 6-Time-202k Data Analysis HTML React SQL Docker salary 101k-202k Data Analysis HTML React SQL Docker AWS Certified Johnson Scientist-Time-153k Services JavaScript Machine Learning CSS Agile JavaScript Machine Learning CSS Agile PMP Oracle Google Cloud certifiedClark Developer 11 Analytics Python Scrum Kubernetes CSS Docker Python Scrum Kubernetes Docker AI Analytics PMP certification salary 124k to 197k Davis Manager 10-Time-232k 10 Python Agile CSS Python Agile CSS infrastructure salary 122k-232k PMP certifiedClark Engineer 13,-Time 13 CSS Kubernetes CSS Kubernetes certifications Google Cloud front-end development AWS infrastructure Johnson Scientist 4 Services Scientist 4 React Java Docker Agile Scrum backend React Java Docker Agile Scrum Google Cloud Certified Data ScientistBrown Developer 14-Time-232k Analytics 14 SQL HTML React PMP Certified salary 141k-232k Miller Developer-Time-169k Data Analysis React HTML Docker Frontend Developer full-time 1 year Data Analysis React HTML Docker JavaScript certifications Frontend Development PMP AWS Data Analysis React HTML DockerDavis Engineer 11-Time-210k Docker Agile CSS Kubernetes Davis DevOps Engineer Docker Agile CSS Kubernetes Certified Data Scientist Taylor Scientist 9-Time-168k Scrum React Python Docker Scrum React Python Docker earns 79k-168k annually infrastructure AWS certificationHall Developer Francisco 12-205k Development React JavaScript Docker CI/CD API Hall San Francisco React JavaScript Docker CI/CD API Oracle Certified 94 Davis Developer 5 Services Developer 5 years CSS Scrum CSS Scrum certifications Google Cloud PMP frontend developmentDavis Manager Francisco 6-Time-153k Management 6 Data Analysis 6 101k-153k Data Analysis CSS certified PMP Oracle Wilson Engineer York 4-Time-195k React Kubernetes HTML API Machine Learning 4 React Kubernetes HTML Learning certified Google Cloud AWS salary $74k to $195k annuallySmith Engineer-Time Data Analysis CSS Scrum Part DevOps Engineer 13 101k-200k Infrastructure Data Analysis CSS Scrum Data Scientist Analysis CSS Scrum 197 Hall Developer York 15-157k Development Developer 15 years CSS Docker PythonAlice Hall Backend Developer New York 15 years Contract Salary $72,000-$157,000 CSS Docker Python Data Scientist Google Cloud Oracle frontend CSS Docker Python Taylor Engineer-Time Scrum CSS API PMP Data Scientist Clark Developer York-Time-187k 3 years Java API JavaScript React AgileClark Frontend Developer New York 3 years specializes Java API JavaScript React Agile certifications AWS Data Science excels AI Analytics Miller Engineer 3-174k DevOps 3 React project management Data Scientist salary 114k-174k Brown Developer 3-Time-179k HTML API Development Scrum/CD React salary 86k-179kspecializes HTML API Scrum CI/CD React AI Analytics certified Google Cloud AWS Oracle Moore Scientist-Time Development API React Machine Learning Docker Scientist API React Machine Learning Docker Java frontend PMP certified results Miller Scientist-Time-202k Machine Learning Scientist SQL Machine Learning React PMP CertifiedHall Developer 15-Time Services Developer 15 years React CSS Frontend Dubai Part-Time Experience 15 years Skills React CSS Certifications AWS Google Cloud Certified PMP Certified React backend Moore Developer 15-Time-210k Analytics 15 15 CSS Java AI Analytics Oracle Certified139 Davis Developer 3-Time Development 3 years React Java Machine Learning Davis Tokyo 3 React Java Machine Learning certifications Google Cloud Data Science AWS 141 Wilson Developer 6-195k Development 6 years Agile CSS 6 years Agile CSS Google Cloud Data Science PMP frontendDavis Developer 11-Time-232k Analytics CSS Agile JavaScript Data Analysis CSS Agile JavaScript Data Analysis AI Analytics Frontend Development Project Management Johnson Developer 12-Time-189k Java Machine Learning Scrum/CD React Java Machine Learning Scrum React Frontend AWS Oracle Certified salary 89k-189kSmith Scientist York 5-Time-225k Analytics Agile API Docker React SQL Smith 5 earnings $126k to $225k AI Analytics Agile API Docker React SQL PMP Certified Moore Developer 14-Time-177k Analytics Scrum AI Analytics 86k-177k certifications Data Science PMP OracleTaylor Developer 6 Development Machine Learning HTML CSS Agile/CD Machine Learning HTML CSS Agile CI/CD Certified Data Science Google Cloud salary 87k-214k Taylor Manager 2-Time-185k 2 React Java CSS 2 111k-185k React Java certified Oracle infrastructure projectsBrown Developer 12-Time Analytics Developer 12 React Java Python Kubernetes API Brown React Java Python Kubernetes API AI Analytics Certified Data Scientist Hall Developer 11-178k 11 CSS Machine Learning Docker CSS Machine Learning Docker PMP Certified Frontend Developer frontendJohnson Scientist 5-Time Management React Java Scrum CSS Kubernetes 65k-200k React Java Scrum CSS Kubernetes project management AWS Certified Frontend Developer Johnson Developer Francisco Management Developer 9 years React HTML JavaScript React HTML JavaScript project management frontend development Oracle certificationDavis Developer 12-Time-173k Services 12 Kubernetes API HTML React Kubernetes API HTML React certified Frontend Development Data Science Oracle Moore Developer York 8-225k Development React Data Analysis Agile Docker Scrum frontend React Data Analysis Agile Docker Scrum Oracle CertifiedMiller Developer 15-Time Development Data Analysis HTML CSS JavaScript SQL Miller salary 85k-238k frontend Data Analysis HTML CSS JavaScript SQL certified Oracle AWS PMP 51 Wilson Engineer 2 Development Engineer 2 React Data Analysis CI React Data Analysis/CD AWS certifiedSmith Scientist 13, 13 Python CSS SQL Python CSS SQL infrastructure salary 62k-162k PMP Certified Frontend Development Data Science 88 Taylor Developer 2-Time-168k Management Developer Scrum CSS SQL Scrum CSS SQL AWS certification project management results salary 60k-168kTaylor Developer 5-Time-206k Services 5 HTML CI/CD API CSS Taylor part Backend Sydney 5 HTML CI/CD API CSS certified Oracle Data Science AWS Davis Engineer 12-Time Kubernetes CSS Machine Learning Python Kubernetes CSS Machine Learning Python SQL AWS Oracle PMP infrastructureBrown Scientist York 11-Time-166k Services React Data Analysis Machine Learning Agile Kubernetes York React Data Analysis Machine Learning Agile Kubernetes certified Data Scientist Google Cloud Certified Moore Developer Francisco-Time Services 7 Python React Python React Certified Frontend DeveloperSmith Developer 15-Time-250k Analytics Developer 15 React JavaScript API Smith Backend Developer React JavaScript API AI Analytics certifications AWS Oracle Google Cloud 178 Davis Manager-Time-159k Analytics Manager 3 years Kubernetes HTML Python React Data Analysis Kubernetes HTML Python React PMP Certified Data Scientist AI Analytics126 Brown Developer Francisco 8 Services Developer 8 CSS JavaScript API CSS JavaScript API certification Data Science Miller Developer Francisco 7-Time-172k Management Developer 7 CI/CD Machine Learning Docker React Python Machine Learning Docker React Python project management Oracle Certified credential skills programming languagesSmith Engineer 6-Time Development Machine Learning Data Analysis API Python Smith 6 salary 136k-219k frontend Machine Learning Data Analysis API Python CSS Google Cloud Certified Frontend Developer Taylor Manager-Time-205k 13 React API SQL React Google Cloud Certified Data Scientist salary 57k-205k project management67 Wilson Engineer 12-204k Development Engineer 12 HTML React Wilson HTML React certifications Wilson Developer 5-169k Developer 5 years Scrum SQL React Kubernetes Data Analysis Backend Scrum SQL React Kubernetes Data Analysis infrastructure Certified Frontend DeveloperJohnson Engineer 15-Time Services SQL Java React JavaScript Johnson SQL Java React JavaScript backend Frontend Developer Data Scientist AWS Certified 43 Moore Developer York 15-Time-188k Management Scrum React Machine Learning HTML Scrum React project management salary $126k-$188k Google Cloud CertifiedWilson Developer 2-Time-156k Development Scrum Java Python CSS Docker Scrum Java Python CSS Docker certifications AWS Google Cloud frontend development 156 Moore Scientist 14-157k Services Scientist 14 API Development CSS Kubernetes Scientist 14 API Kubernetes PMP certified resultsMiller Manager 6-158k 6 Machine Learning React CSS Python Machine Learning React CSS Python certifications Oracle AWS Certified Data Scientist infrastructure projects 184 Brown Engineer 6-166k Management Engineer 6 HTML React SQL DevOps HTML React SQL project managementBrown Engineer 11-Time-201k Agile SQL Kubernetes React Brown DevOps Engineer 110k-201k Agile SQL Kubernetes React certified Google Cloud PMP Oracle infrastructure Smith Developer-Time Management Machine Learning CSS SQL Docker 64k-200k Machine Learning SQL Docker Kubernetes AWS certification project management Machine DockerJohnson Scientist 9 Analytics Kubernetes API Development CSS Data Scientist Kubernetes API CSS AI Analytics AWS Certified salary 82k-209k Wilson Scientist 14-187k Management Scientist 14 CSS Agile CSS Agile project management certifications Data Science AWS Frontend DevelopmentWilson Developer 6-154k Management Python CSS Machine Learning React Kubernetes Python CSS Machine Learning React Kubernetes Data Scientist PMP Oracle Davis Manager 6-Time-175k React Machine Learning Java Kubernetes Data Analysis React Machine Learning Java Kubernetes Analysis Frontend Developer Oracle CertifiedJohnson Scientist 8 Services Scrum CSS Agile SQL Scrum CSS Agile SQL AWS Certified Moore Developer-176k React API Python Machine Learning Kubernetes Backend Developer 2-year contract salary 55k-176k React API Python Machine Learning Kubernetes Google Cloud Certified163 Davis Developer 8-151k Management Developer SQL Java CSS Docker SQL Java CSS Docker Frontend Developer Oracle Data Scientist project management\n", "\n", "Based on your query for employees who are skilled with website-related HR tasks or development, here are some potential teams or individuals you could consider:\n", "\n", "1. **Frontend Development Focused Team:**\n", "   - **<PERSON>eloper (15 years):** Specializes in React and CSS, is Google Cloud and PMP certified.\n", "   - **<PERSON> (15 years):** Experienced in React and CSS, with certifications in AWS and Oracle.\n", "   - **<PERSON> (9 years in San Francisco):** Skilled in React, HTML, and JavaScript with a focus on frontend development.\n", "\n", "2. **Data Analysis and Machine Learning Team:**\n", "   - **<PERSON> (12 years):** Skilled in Machine Learning and Python with Kubernetes and Java, focusing on infrastructure.\n", "   - **Miller Scientist (Time-202k):** Specializes in SQL and Machine Learning, is PMP certified.\n", "   - **<PERSON> (11 years):** Skilled in Python, Kubernetes, and AI Analytics with a PMP certification.\n", "\n", "3. **Agile and Project Management Team:**\n", "   - **<PERSON> Engineer (12 years):** Experienced with React and HTML, applicable for Agile development projects.\n", "   - **Taylor Manager (2 years):** Has management experience with React and Java, Oracle certified with infrastructure projects.\n", "   - **Davis Manager (8 years):** Agile and React focused, PMP certified, with an emphasis on project management.\n", "\n", "4. **Full-Stack Development Team:**\n", "   - **<PERSON> (15 years):** Proficient in HTML, CSS, JavaScript, SQL, and data analysis.\n", "   - **<PERSON> (12 years):** Experienced with React, Java, Python, and Kubernetes with AI Analytics capabilities.\n", "   - **<PERSON> (6 years):** Skilled in Python, CSS, and Kubernetes, focused on management and backend development.\n", "\n", "These teams are compiled based on their expertise in development, project management, and their relevant certifications for web and HR tech applications. The choice of team would depend on the specific aspect of web and HR work you want to focus on, whether it is frontend interfaces, backend data handling, agile project execution, or a comprehensive full-stack development approach.\n"]}], "source": ["results = handle_user_query(query, compress_prompt=True)\n", "\n", "print(results)"]}], "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0bb9d11818a4472f875705bdaed06d68": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_43979fb65685498db2171f6f8abf2e75", "placeholder": "​", "style": "IPY_MODEL_ed27b8bbe8c34e46899cc23d3adcd672", "value": " 17.1M/17.1M [00:01&lt;00:00, 10.4MB/s]"}}, "0f2688247ce041d4800bd356425040e9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aabf33463a8246e9b70af6501088410c", "max": 280, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9107c04abbe6405bb1ce18480fe39973", "value": 280}}, "1244ba5cd262417e9862d5069d831f8f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_adbc801a29b9434abb3b806d449fbf26", "placeholder": "​", "style": "IPY_MODEL_2b4c1a424b92417588a70ff8229ee713", "value": "tokenizer_config.json: 100%"}}, "156da873a66145fcbdf7beebdca9debd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "18c3ff72df3349a6b9203e46ea19be0a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33549d0ba1974a2db0b2e606ecf8c55b", "placeholder": "​", "style": "IPY_MODEL_6515e09f208549bf828acf4c431a0657", "value": " 2.24G/2.24G [03:35&lt;00:00, 10.4MB/s]"}}, "1b0f9af8283641188c745c87a0a8b65e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e28312482aa438cb0c1be48aff0b9c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_81c468f6fcfa423a929735fb3c345d07", "placeholder": "​", "style": "IPY_MODEL_a7caa1ff307242b394314da780888050", "value": " 752/752 [00:00&lt;00:00, 48.5kB/s]"}}, "1f117a8d54be4fdba773e77591860121": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "20180ec7781d468b93048b5275edbcbe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9afc784fb504492a97d2aa5d03490be3", "placeholder": "​", "style": "IPY_MODEL_ce37479f2f0b4c6aa801170bf25ac4f8", "value": "model.safetensors: 100%"}}, "2b4c1a424b92417588a70ff8229ee713": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3225ba9395a2452e9f7725f16edd89d7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "33549d0ba1974a2db0b2e606ecf8c55b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3b364d8e875a452ca4272c418e274d1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3d17792b4ee940c08c030dc47cd6165c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4112dabb3d954a18a811e8ab3f098044": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5c5dd5e3860947dea65245480451393e", "IPY_MODEL_6233ba1e2a8b4504952a9b5a195a43a3", "IPY_MODEL_0bb9d11818a4472f875705bdaed06d68"], "layout": "IPY_MODEL_a16e36453aa044428e4394472e1b4441"}}, "43979fb65685498db2171f6f8abf2e75": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "453296eb227c4d4abfc8d55207064d15": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f396b35d0acc4c6e939a6824fad8d89b", "placeholder": "​", "style": "IPY_MODEL_3225ba9395a2452e9f7725f16edd89d7", "value": " 1.15k/1.15k [00:00&lt;00:00, 70.1kB/s]"}}, "4a6fcdb5bc8745c182b63522834fdc8f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b8ea812551747f0a35c7b6c0788d106", "max": 1147, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b10f3b0a36494b84afbff796d27260ff", "value": 1147}}, "5b4f1afdf393448fb48c66c3a5933c4e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa10c7dc2aec425f8a9629e199d481ec", "max": 752, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_88f0ac2426fd472bba67e008430cde7f", "value": 752}}, "5c5dd5e3860947dea65245480451393e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1f117a8d54be4fdba773e77591860121", "placeholder": "​", "style": "IPY_MODEL_93e93cd065154f5fa4d3af5c2edb8d02", "value": "tokenizer.json: 100%"}}, "6233ba1e2a8b4504952a9b5a195a43a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_70f7184cf8ca481a9ee95e33abb460d9", "max": 17082756, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_156da873a66145fcbdf7beebdca9debd", "value": 17082756}}, "623428b4a26844dd88a1c40c95a6d473": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "629f2cb96af842189c0f195dff491643": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_20180ec7781d468b93048b5275edbcbe", "IPY_MODEL_a47dd5434f5f437c9137b89624cdf1e2", "IPY_MODEL_18c3ff72df3349a6b9203e46ea19be0a"], "layout": "IPY_MODEL_7352ad0338384d78b072992a234193da"}}, "6515e09f208549bf828acf4c431a0657": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6b8ea812551747f0a35c7b6c0788d106": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6bc26451d0574b1ba6284a381ba8dc5f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "70f7184cf8ca481a9ee95e33abb460d9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7352ad0338384d78b072992a234193da": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "75693e54f31d40db86ffc8edd33aca6a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8fdc8055ea2746768ac65ba3c9cd6597", "placeholder": "​", "style": "IPY_MODEL_6bc26451d0574b1ba6284a381ba8dc5f", "value": "config.json: 100%"}}, "81c468f6fcfa423a929735fb3c345d07": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "88f0ac2426fd472bba67e008430cde7f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8fdc8055ea2746768ac65ba3c9cd6597": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9107c04abbe6405bb1ce18480fe39973": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "93e93cd065154f5fa4d3af5c2edb8d02": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9afc784fb504492a97d2aa5d03490be3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a16e36453aa044428e4394472e1b4441": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a2975615bfc24bf1bfc11e097c3a00a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1244ba5cd262417e9862d5069d831f8f", "IPY_MODEL_4a6fcdb5bc8745c182b63522834fdc8f", "IPY_MODEL_453296eb227c4d4abfc8d55207064d15"], "layout": "IPY_MODEL_f7de6fa5eb384cc18a2b50bdd28a317a"}}, "a47dd5434f5f437c9137b89624cdf1e2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ecac637bd0184e9abd0c676ab89faa65", "max": 2235829648, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fe37f3ab3769471bafd6825b76176906", "value": 2235829648}}, "a7caa1ff307242b394314da780888050": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "aa10c7dc2aec425f8a9629e199d481ec": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aabf33463a8246e9b70af6501088410c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "adbc801a29b9434abb3b806d449fbf26": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b10f3b0a36494b84afbff796d27260ff": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b653001bc9744c0590db8a15446a88fb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce37479f2f0b4c6aa801170bf25ac4f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d2c1bcea6bf34ccbbb44f8fe34cdd041": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f047b58926ce48ea89255cb9e9f6116a", "IPY_MODEL_0f2688247ce041d4800bd356425040e9", "IPY_MODEL_ebeeac91e5eb4aac9935231492a48778"], "layout": "IPY_MODEL_623428b4a26844dd88a1c40c95a6d473"}}, "ebeeac91e5eb4aac9935231492a48778": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1b0f9af8283641188c745c87a0a8b65e", "placeholder": "​", "style": "IPY_MODEL_3b364d8e875a452ca4272c418e274d1c", "value": " 280/280 [00:00&lt;00:00, 8.41kB/s]"}}, "ecac637bd0184e9abd0c676ab89faa65": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ed27b8bbe8c34e46899cc23d3adcd672": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ef76796a74524a3ab2ce3dbdfa2f6607": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_75693e54f31d40db86ffc8edd33aca6a", "IPY_MODEL_5b4f1afdf393448fb48c66c3a5933c4e", "IPY_MODEL_1e28312482aa438cb0c1be48aff0b9c8"], "layout": "IPY_MODEL_f224ad85c34446d895a5755252fa610d"}}, "f047b58926ce48ea89255cb9e9f6116a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b653001bc9744c0590db8a15446a88fb", "placeholder": "​", "style": "IPY_MODEL_3d17792b4ee940c08c030dc47cd6165c", "value": "special_tokens_map.json: 100%"}}, "f224ad85c34446d895a5755252fa610d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f396b35d0acc4c6e939a6824fad8d89b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f7de6fa5eb384cc18a2b50bdd28a317a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe37f3ab3769471bafd6825b76176906": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}