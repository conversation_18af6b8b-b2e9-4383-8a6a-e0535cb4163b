{"cells": [{"cell_type": "markdown", "metadata": {"id": "3hWMQQOdJMbx"}, "source": ["# RAG Pipeline With <PERSON> 2, MongoDB and Hugging Face [Open Models]\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_with_gemma2_mongodb_open_models.ipynb)"]}, {"cell_type": "markdown", "metadata": {"id": "_XtqPyEuJYjz"}, "source": ["## Set Up Libraries"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NoFjgZHHWlXp", "outputId": "629de58f-70fe-471b-fa1e-f8700fc8c0f9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m547.8/547.8 kB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.0/13.0 MB\u001b[0m \u001b[31m41.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m53.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m227.1/227.1 kB\u001b[0m \u001b[31m28.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m40.8/40.8 MB\u001b[0m \u001b[31m40.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.3/116.3 kB\u001b[0m \u001b[31m17.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m64.9/64.9 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.1/194.1 kB\u001b[0m \u001b[31m25.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m134.8/134.8 kB\u001b[0m \u001b[31m20.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m36.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.3/21.3 MB\u001b[0m \u001b[31m70.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "cudf-cu12 24.4.1 requires pandas<2.2.2dev0,>=2.0, but you have pandas 2.2.2 which is incompatible.\n", "cudf-cu12 24.4.1 requires pyarrow<15.0.0a0,>=14.0.1, but you have pyarrow 16.1.0 which is incompatible.\n", "google-colab 1.0.0 requires pandas==2.0.3, but you have pandas 2.2.2 which is incompatible.\n", "google-colab 1.0.0 requires requests==2.31.0, but you have requests 2.32.3 which is incompatible.\n", "ibis-framework 8.0.0 requires pyarrow<16,>=2, but you have pyarrow 16.1.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m9.3/9.3 MB\u001b[0m \u001b[31m23.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m309.4/309.4 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install --upgrade --quiet datasets pandas pymongo sentence_transformers\n", "!pip install --upgrade --quiet transformers\n", "# Install below if using GPU\n", "!pip install --upgrade --quiet accelerate"]}, {"cell_type": "markdown", "metadata": {"id": "7myisb4nJgZa"}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 779, "referenced_widgets": ["e33f6b3a1247488890282df389b6ed61", "23e0098f59854da2a408d9f229fe482b", "b902ddfd9f52410cb70bd73c477683b4", "07c11c417b2041dcbff3c62bf9fd410b", "0ea6c486a6e3472396f51d25828f183b", "679d124f567e47f186f8b7d38037cf16", "b93d153824b243b68c48017f45db3139", "acdd038e8c7140e2af6a9084bbd42410", "91342c5ebe334efda4c002e3f7e5d2a3", "e5624cfc65d84f1cb193080dc1919758", "38cf7c5ca6e1476cb01e655c58d856a8"]}, "id": "LLUInmm_XVnZ", "outputId": "0b06d525-4ebd-490f-d92b-95e61cef9df9"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e33f6b3a1247488890282df389b6ed61", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading readme:   0%|          | 0.00/6.17k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1500,\n  \"fields\": [\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1429,\n        \"samples\": [\n          \"A New York City architect becomes a one-man vigilante squad after his wife is murdered by street punks in which he randomly goes out and kills would-be muggers on the mean streets after dark.\",\n          \"As the daring thief A<PERSON>\\u00e8ne <PERSON> (<PERSON><PERSON>) ransacks the homes of wealthy Parisians, the police, with a secret weapon in their arsenal, attempt to ferret him out.\",\n          \"The spoilt son of a millionaire finds the love of his life, but she will only accept him if he proves himself as a kung-fu master. He enters and wins the \\\"Kung-Fu Scholar\\\" tournament, ...\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.09038552453906,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 139,\n        \"samples\": [\n          152.0,\n          127.0,\n          96.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1368,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BNWE5MzAwMjQtNzI1YS00YjZhLTkxNDItM2JjNjM3ZjI5NzBjXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTgwNjIyNTczMF5BMl5BanBnXkFtZTcwODI5MDkyMQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BZDJjOTE0N2EtMmRlZS00NzU0LWE0ZWQtM2Q3MWMxNjcwZjBhXkEyXkFqcGdeQXVyNDk3NzU2MTQ@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1435,\n        \"samples\": [\n          \"Turbo: A Power Rangers Movie\",\n          \"Neon Genesis Evangelion: Death & Rebirth\",\n          \"Johnny Mnemonic\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134,\n          124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\",\n          \"TV-G\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.861995960390892,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot_embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-61fef7df-3c47-4c76-800e-a18db6afb66e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>plot</th>\n", "      <th>runtime</th>\n", "      <th>genres</th>\n", "      <th>fullplot</th>\n", "      <th>directors</th>\n", "      <th>writers</th>\n", "      <th>countries</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>cast</th>\n", "      <th>title</th>\n", "      <th>num_mflix_comments</th>\n", "      <th>rated</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>type</th>\n", "      <th>metacritic</th>\n", "      <th>plot_embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>199.0</td>\n", "      <td>[Action]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[0.0007293965299999999, -0.026834568000000003,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>22.0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>0</td>\n", "      <td>TV-G</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.022837115, -0.022941574000000003, 0.014937...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>101.0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>[USA]</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[0.00023330492999999998, -0.028511643000000003...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>88.0</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>The Black Pirate</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.005927917, -0.033394486, 0.0015323418, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>58.0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>For Heaven's Sake</td>\n", "      <td>0</td>\n", "      <td>PASSED</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.0059373598, -0.026604708, -0.0070914757000...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-61fef7df-3c47-4c76-800e-a18db6afb66e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-61fef7df-3c47-4c76-800e-a18db6afb66e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-61fef7df-3c47-4c76-800e-a18db6afb66e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-c680c079-9b9b-49db-94f6-ac59eef669d3\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-c680c079-9b9b-49db-94f6-ac59eef669d3')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-c680c079-9b9b-49db-94f6-ac59eef669d3 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["                                                plot  runtime  \\\n", "0  Young <PERSON> is left a lot of money when her ...    199.0   \n", "1  A penniless young man tries to save an heiress...     22.0   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...    101.0   \n", "3  Seeking revenge, an athletic young man joins t...     88.0   \n", "4  An irresponsible young millionaire changes his...     58.0   \n", "\n", "                       genres  \\\n", "0                    [Action]   \n", "1     [Comedy, Short, Action]   \n", "2  [Action, Adventure, Drama]   \n", "3         [Adventure, Action]   \n", "4   [Action, Comedy, Romance]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                             writers countries  \\\n", "0  [<PERSON> (screenplay), <PERSON>...     [USA]   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]     [USA]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...     [USA]   \n", "3  [<PERSON> (story), <PERSON> (a...     [USA]   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.     [USA]   \n", "\n", "                                              poster  languages  \\\n", "0  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2                                               None  [English]   \n", "3  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                                cast                  title  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...  The Perils of Pauline   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     From Hand to Mouth   \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>             <PERSON>   \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>..       The Black Pirate   \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.      For Heaven's Sake   \n", "\n", "   num_mflix_comments   rated                                         imdb  \\\n", "0                   0    None    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1                   0    TV-G   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2                   0    None   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3                   1    None  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4                   0  PASSED   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                              awards   type  metacritic  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "\n", "                                      plot_embedding  \n", "0  [0.0007293965299999999, -0.026834568000000003,...  \n", "1  [-0.022837115, -0.022941574000000003, 0.014937...  \n", "2  [0.00023330492999999998, -0.028511643000000003...  \n", "3  [-0.005927917, -0.033394486, 0.0015323418, -0....  \n", "4  [-0.0059373598, -0.026604708, -0.0070914757000...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load Dataset\n", "import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# Make sure you have an Hugging Face token(HF_TOKEN) in your development environemnt before running the code below\n", "# How to get a token: https://huggingface.co/docs/hub/en/security-tokens\n", "\n", "# https://huggingface.co/datasets/MongoDB/embedded_movies\n", "dataset = load_dataset(\"MongoDB/embedded_movies\", split=\"train\", streaming=True)\n", "dataset = dataset.take(4000)\n", "\n", "# Convert the dataset to a pandas dataframe\n", "dataset_df = pd.DataFrame(dataset)\n", "\n", "dataset_df.head(5)"]}, {"cell_type": "markdown", "metadata": {"id": "EvHrNR5fJmz1"}, "source": ["## Data Cleaning"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "0saXweilXblD", "outputId": "970042d9-7ade-4955-c5d1-00ba747d4c74"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Number of missing values in each column after removal:\n", "plot                    0\n", "runtime                14\n", "genres                  0\n", "fullplot                0\n", "directors              12\n", "writers                13\n", "countries               0\n", "poster                 78\n", "languages               1\n", "cast                    1\n", "title                   0\n", "num_mflix_comments      0\n", "rated                 279\n", "imdb                    0\n", "awards                  0\n", "type                    0\n", "metacritic            893\n", "plot_embedding          1\n", "dtype: int64\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1452,\n  \"fields\": [\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> saves Tokyo from a flying saucer that transforms into the beast <PERSON><PERSON>.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.5693352357647,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 137,\n        \"samples\": [\n          60.0,\n          151.0,\n          110.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1332,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BMTQ2NTMxODEyNV5BMl5BanBnXkFtZTcwMDgxMjA0MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTY5OTg1ODk0MV5BMl5BanBnXkFtZTcwMTEwMjU1MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTMyMjgyMDIyM15BMl5BanBnXkFtZTcwNjg3MjAyMg@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1391,\n        \"samples\": [\n          \"Superhero Movie\",\n          \"Hooper\",\n          \"Sivaji\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134,\n          124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\",\n          \"TV-G\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.855402595666057,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-90795e89-efe3-40fd-b2ca-c59de1beb32f\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>plot</th>\n", "      <th>runtime</th>\n", "      <th>genres</th>\n", "      <th>fullplot</th>\n", "      <th>directors</th>\n", "      <th>writers</th>\n", "      <th>countries</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>cast</th>\n", "      <th>title</th>\n", "      <th>num_mflix_comments</th>\n", "      <th>rated</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>type</th>\n", "      <th>metacritic</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>199.0</td>\n", "      <td>[Action]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>22.0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>0</td>\n", "      <td>TV-G</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>101.0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>[USA]</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>88.0</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>The Black Pirate</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>58.0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>For Heaven's Sake</td>\n", "      <td>0</td>\n", "      <td>PASSED</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-90795e89-efe3-40fd-b2ca-c59de1beb32f')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-90795e89-efe3-40fd-b2ca-c59de1beb32f button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-90795e89-efe3-40fd-b2ca-c59de1beb32f');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-1347a2a7-ad14-46cb-8e08-f1aa86e8031a\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-1347a2a7-ad14-46cb-8e08-f1aa86e8031a')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-1347a2a7-ad14-46cb-8e08-f1aa86e8031a button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["                                                plot  runtime  \\\n", "0  Young <PERSON> is left a lot of money when her ...    199.0   \n", "1  A penniless young man tries to save an heiress...     22.0   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...    101.0   \n", "3  Seeking revenge, an athletic young man joins t...     88.0   \n", "4  An irresponsible young millionaire changes his...     58.0   \n", "\n", "                       genres  \\\n", "0                    [Action]   \n", "1     [Comedy, Short, Action]   \n", "2  [Action, Adventure, Drama]   \n", "3         [Adventure, Action]   \n", "4   [Action, Comedy, Romance]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                             writers countries  \\\n", "0  [<PERSON> (screenplay), <PERSON>...     [USA]   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]     [USA]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...     [USA]   \n", "3  [<PERSON> (story), <PERSON> (a...     [USA]   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.     [USA]   \n", "\n", "                                              poster  languages  \\\n", "0  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2                                               None  [English]   \n", "3  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                                cast                  title  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...  The Perils of Pauline   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     From Hand to Mouth   \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>             <PERSON>   \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>..       The Black Pirate   \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.      For Heaven's Sake   \n", "\n", "   num_mflix_comments   rated                                         imdb  \\\n", "0                   0    None    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1                   0    TV-G   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2                   0    None   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3                   1    None  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4                   0  PASSED   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                              awards   type  metacritic  \n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN  \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN  \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN  \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN  \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data Preparation\n", "\n", "# Remove data point where plot coloumn is missing\n", "dataset_df = dataset_df.dropna(subset=[\"fullplot\"])\n", "print(\"\\nNumber of missing values in each column after removal:\")\n", "print(dataset_df.isnull().sum())\n", "\n", "# Remove the plot_embedding from each data point in the dataset as we are going to create new embeddings with an open source embedding model from Hugging Face\n", "dataset_df = dataset_df.drop(columns=[\"plot_embedding\"])\n", "dataset_df.head(5)"]}, {"cell_type": "markdown", "metadata": {"id": "iY00KJM_Jn2_"}, "source": ["## Embedding Generation"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 354, "referenced_widgets": ["13922824f7eb4a9fa9daaca028290ff1", "e21740a1fe1e47f593409a23563265fa", "21d3f7f6d60c4733960e1fac14837551", "9b1463160d89457699e8cb20f52cdd53", "85fad341ac03441284d0f79afc4c0edc", "426035a02eb54a6d8ebd8ea210bc284d", "f9be4a1aa4384f8dafbd7270ee688f58", "9253f97927084862846fdc4edd66e26f", "33bd7abc88314415ab8acad6c4bd2a59", "36ebd34bf019428db97f2939def00345", "cee177a2947348cc996c517e699daf8b", "8b7fd56cef624959b4236a2f3d00d18f", "2a04c4ab8dd04d289c7356139e111604", "b64d751a8c604063afba528bbf3460ad", "21130c13fd894d9f81f663f9477c8202", "5b9cc5943305436c86c322131908ad54", "38096d247b484dfd9eca1bfe0cb0869b", "2d69f9660e4f459493007dd39eac4cad", "ef38b69a2acc47d5ba47e66cb58764a8", "a90a64a9873b4112a2f5ffcfde06411e", "e35eceac5a0f4a15b0217c6e6e890d1b", "751ef5769dcb49d4aec0e9ba714d3a98", "6b086ada3646438082c1bad298e1a7f5", "a0657b2393064a64ba3c9d6e3e9ad098", "e47b416da6e540b28e7323452631fbd2", "e4006278e9a440ed8e3828eaae4bdcd7", "97c348b47ed0405198fcbf1724d0aafc", "09c92d9bba7e4827a8462125ecd2fbb5", "ad05522d58f64d5c9ea2f3d267b3a4d6", "da00bae6673b4d709ef1b7317e1450b8", "fee5fffb9ebf471d9ecb912c3fd26c91", "1eabd224899a4e079667c1e385f672cc", "5eb5a8b647364a6a83733423538a6f17", "fefbfbce7e0f46baad2c4413414868fe", "b4bb9facf9644a1aa03d5886215e69fb", "6f45b925a0dc487389724a1d36768cc2", "8b02b770dd654d609d0483e2d06006a9", "7d1255955d7f4d5d822d167aec268095", "28129422cd4042f8ba837ed812c714dd", "1bec5087aacc48b9b786ae5fd5445085", "a30de3a43306419b9ab44a08d342b869", "56f59cc557444a5cbd267ed596936198", "4dfe2f30154c41d89a223e73473d17bb", "acee94b8731b481293d2bce48bbb343f", "b5f9fad01d7f4e2ca51d2116004478e4", "2f4f48920cce4de78df7c9f782f44240", "a11a6a665e0241f58a836accc3f3351d", "c762578ed8324a9bb933193b3d76690a", "df6e9ea5d47341caa0e78505560b7b4d", "dac60c6af6f4412c91d40246095a607f", "9531382c5465446e8a2802017f13b02b", "e877369337da47fc9750cb360c41b640", "45dcb668409f45e281a6ac9432b035d7", "157ec39934a44e9881e67e3a960a31ba", "66a7186430064ea3b2edf40866d7b3a3", "51f41a92d0d24c93a85bd0cdd12baf96", "7144f5c190064859b404376a57309be7", "28fdd8aaf14f41fab7609a98767ce96c", "fa813647eeb94bfeaf3e2508f8f61cdc", "2dc48f9fae434dceb3bc21db3a2c6ed2", "a205dd66663049f0baabc4c499327515", "90df64cedf344ce4aac9fa06ab7ec1ab", "6cef4be37536439ab53d0d9b06edc23c", "81d3260445d24e0292b51f7f361031a0", "531dfe254eff4d9485b7fab2dfeacf6e", "01bf1fca1c0f4189b3fa1e9b87c41ba0", "a7cc12ff38e2471d98292232ec2fef40", "bdab962e4eeb44daa31f6782958e7501", "29de5219c1a5407b8f49d20693bceb5e", "e226fdf7b27649abb898ead54220826f", "860798785a9b4728924aff2f303d628f", "53247cc4267c46ba924b81914a2837e9", "5c295ef2733a49ce80805fcc6895e2fa", "4646c04e22944a77ae246a8022babe56", "26e0b18819a647b2a64970544f80edaa", "92ebc28e906346caa277db29b4519a2b", "bdc8dbca7607474a91309a5be1c30938", "1d7dd8b4fc824566a92567e78e45feb6", "e4fbdc5e183f46308ccc88b62e6c3b43", "966ae244eb7049008193fa4ca0f8702c", "afdb3bb004d341268c927b0c58bf9128", "8ce370ae0e844c4f89f0f0ed51d1d614", "2d5272e42bcf4d9d92f1f820fa736237", "06f675a549d7475bb1bb53216aab8013", "29c7b957d65842fba09879dfbf07e0d5", "7faac74bd69c4bb9951ee85a5c35a219", "80ef8e629a9f4e3fa0bb480011d69c83", "d14a2456a14348c5b6c1070494000339", "d11c848a53404576a89d7a7ecee559df", "8f83b31014524ec1abaa879697b46d7b", "3c41505982b6465aab66844d1c1c5bb7", "d3a6ddd12b314e0bb7dce664b193bf09", "113070d604954fe2851f68d0a65a517f", "8ba541e61ea846f8b641294de98897a7", "bf0decd455834c5793275c0ad1006fd6", "c29219a1b29c402aad7c11e804823b1d", "aa5997e1dbba4955a1ccbbbde45cb93d", "e8693a705cdf46eaa4cb5143159108be", "76a32f0cea704609a33023343b65dc32", "3ace6127a48a47b6993fb5f59e5edcf2", "fa60f0b2621f49efa9aa67909e0b10cb", "dbfba6717f604929b0ab1a396fe8f014", "7c06a7cf9bbc406d9bbe2ba6f1de6e8c", "def05e65e2f44423aa05cedb4f83431b", "c133bac838ca47fa99a9de8f5c48ae41", "907f122633ba4d4fa29a2a30c1109c2e", "e97aec423d75440690458f2131563ccd", "da7515953b974f7399c259bf5fcb24ab", "cd1c98560aa14452ac0f4429e6ec4d8a", "7b5b53cda2da45b0859fcba6aa8e57cb"]}, "id": "PVGz8jp1XdBQ", "outputId": "43343bfd-4fda-4cba-c687-b04cbeaa50ef"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "13922824f7eb4a9fa9daaca028290ff1", "version_major": 2, "version_minor": 0}, "text/plain": ["modules.json:   0%|          | 0.00/385 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8b7fd56cef624959b4236a2f3d00d18f", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/67.9k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b086ada3646438082c1bad298e1a7f5", "version_major": 2, "version_minor": 0}, "text/plain": ["sentence_bert_config.json:   0%|          | 0.00/57.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fefbfbce7e0f46baad2c4413414868fe", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/619 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b5f9fad01d7f4e2ca51d2116004478e4", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/670M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "51f41a92d0d24c93a85bd0cdd12baf96", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/342 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a7cc12ff38e2471d98292232ec2fef40", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1d7dd8b4fc824566a92567e78e45feb6", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/712k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d11c848a53404576a89d7a7ecee559df", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/125 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3ace6127a48a47b6993fb5f59e5edcf2", "version_major": 2, "version_minor": 0}, "text/plain": ["1_Pooling/config.json:   0%|          | 0.00/191 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Generating embeddings: 100%|██████████| 1452/1452 [00:36<00:00, 39.52it/s]\n"]}], "source": ["from sentence_transformers import SentenceTransformer\n", "from tqdm import tqdm\n", "\n", "# https://huggingface.co/thenlper/gte-large\n", "embedding_model = SentenceTransformer(\"thenlper/gte-large\")\n", "\n", "\n", "def get_embedding(text: str) -> list[float]:\n", "    if not text.strip():\n", "        print(\"Attempted to get embedding for empty text.\")\n", "        return []\n", "\n", "    embedding = embedding_model.encode(text)\n", "\n", "    return embedding.tolist()\n", "\n", "\n", "# Apply the embedding function with a progress bar\n", "tqdm.pandas(desc=\"Generating embeddings\")\n", "dataset_df[\"embedding\"] = dataset_df[\"fullplot\"].progress_apply(get_embedding)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 747}, "id": "-M4oKALcYuGo", "outputId": "d37a26ea-ebc6-41b0-9056-ce1dd9e02f0f"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1452,\n  \"fields\": [\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> saves Tokyo from a flying saucer that transforms into the beast <PERSON><PERSON>.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.5693352357647,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 137,\n        \"samples\": [\n          60.0,\n          151.0,\n          110.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1332,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BMTQ2NTMxODEyNV5BMl5BanBnXkFtZTcwMDgxMjA0MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTY5OTg1ODk0MV5BMl5BanBnXkFtZTcwMTEwMjU1MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTMyMjgyMDIyM15BMl5BanBnXkFtZTcwNjg3MjAyMg@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1391,\n        \"samples\": [\n          \"Superhero Movie\",\n          \"Hooper\",\n          \"Sivaji\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134,\n          124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\",\n          \"TV-G\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.855402595666057,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-f9f46056-f6b0-46e1-bb5e-d3c6ce6f243b\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>plot</th>\n", "      <th>runtime</th>\n", "      <th>genres</th>\n", "      <th>fullplot</th>\n", "      <th>directors</th>\n", "      <th>writers</th>\n", "      <th>countries</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>cast</th>\n", "      <th>title</th>\n", "      <th>num_mflix_comments</th>\n", "      <th>rated</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>type</th>\n", "      <th>metacritic</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>199.0</td>\n", "      <td>[Action]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.009285833686590195, -0.005062105134129524,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>22.0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>0</td>\n", "      <td>TV-G</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.002439383417367935, 0.02309592068195343, -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>101.0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>[USA]</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[0.012204288505017757, -0.011455747298896313, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>88.0</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>The Black Pirate</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[0.004541359841823578, -0.0006100474274717271,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>58.0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>For Heaven's Sake</td>\n", "      <td>0</td>\n", "      <td>PASSED</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.002225597621873021, 0.011567803099751472, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f9f46056-f6b0-46e1-bb5e-d3c6ce6f243b')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f9f46056-f6b0-46e1-bb5e-d3c6ce6f243b button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f9f46056-f6b0-46e1-bb5e-d3c6ce6f243b');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-d64d441a-5b76-45df-a5c6-1004fb0b77da\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-d64d441a-5b76-45df-a5c6-1004fb0b77da')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-d64d441a-5b76-45df-a5c6-1004fb0b77da button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["                                                plot  runtime  \\\n", "0  Young <PERSON> is left a lot of money when her ...    199.0   \n", "1  A penniless young man tries to save an heiress...     22.0   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...    101.0   \n", "3  Seeking revenge, an athletic young man joins t...     88.0   \n", "4  An irresponsible young millionaire changes his...     58.0   \n", "\n", "                       genres  \\\n", "0                    [Action]   \n", "1     [Comedy, Short, Action]   \n", "2  [Action, Adventure, Drama]   \n", "3         [Adventure, Action]   \n", "4   [Action, Comedy, Romance]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                             writers countries  \\\n", "0  [<PERSON> (screenplay), <PERSON>...     [USA]   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]     [USA]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...     [USA]   \n", "3  [<PERSON> (story), <PERSON> (a...     [USA]   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.     [USA]   \n", "\n", "                                              poster  languages  \\\n", "0  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2                                               None  [English]   \n", "3  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                                cast                  title  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...  The Perils of Pauline   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     From Hand to Mouth   \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>             <PERSON>   \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>..       The Black Pirate   \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.      For Heaven's Sake   \n", "\n", "   num_mflix_comments   rated                                         imdb  \\\n", "0                   0    None    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1                   0    TV-G   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2                   0    None   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3                   1    None  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4                   0  PASSED   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                              awards   type  metacritic  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "\n", "                                           embedding  \n", "0  [-0.009285833686590195, -0.005062105134129524,...  \n", "1  [-0.002439383417367935, 0.02309592068195343, -...  \n", "2  [0.012204288505017757, -0.011455747298896313, ...  \n", "3  [0.004541359841823578, -0.0006100474274717271,...  \n", "4  [-0.002225597621873021, 0.011567803099751472, ...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "M-y3pyy6K42N"}, "source": ["## MongoDB Vector Database and Connection Setup"]}, {"cell_type": "markdown", "metadata": {"id": "t71kFjCsJ3ad"}, "source": ["\n", "MongoDB acts as both an operational and a vector database for the RAG system.\n", "MongoDB Atlas specifically provides a database solution that efficiently stores, queries and retrieves vector embeddings.\n", "\n", "Creating a database and collection within MongoDB is made simple with MongoDB Atlas.\n", "\n", "1. First, register for a [MongoDB Atlas account](https://www.mongodb.com/cloud/atlas/register). For existing users, sign into MongoDB Atlas.\n", "2. [Follow the instructions](https://www.mongodb.com/docs/atlas/tutorial/deploy-free-tier-cluster/). Select Atlas UI as the procedure to deploy your first cluster.\n", "3. Create the database: `movie_rec_sys`.\n", "4. Within the database ` movie_collection`, create the collection ‘listings_reviews’.\n", "5. Create a [vector search index](https://www.mongodb.com/docs/atlas/atlas-vector-search/create-index/#procedure/) named vector_index for the ‘listings_reviews’ collection. This index enables the RAG application to retrieve records as additional context to supplement user queries via vector search. Below is the JSON definition of the data collection vector search index.\n", "\n", "Your vector search index created on MongoDB Atlas should look like below:\n", "\n", "```\n", "{\n", "  \"fields\": [\n", "    {\n", "      \"numDimensions\": 1024,\n", "      \"path\": \"embedding\",\n", "      \"similarity\": \"cosine\",\n", "      \"type\": \"vector\"\n", "    }\n", "  ]\n", "}\n", "\n", "```\n", "\n", "Follow MongoDB’s [steps to get the connection](https://www.mongodb.com/docs/manual/reference/connection-string/) string from the Atlas UI. After setting up the database and obtaining the Atlas cluster connection URI, securely store the URI within your development environment."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "LAwrsIXPKfPt"}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"MONGO_URI\"] = \"\""]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BEG4BtKWXefx", "outputId": "99dcc1fc-38d5-47d7-c1c2-3613b0b9a2e3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["import pymongo\n", "\n", "\n", "def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish and validate connection to the MongoDB.\"\"\"\n", "\n", "    client = pymongo.MongoClient(mongo_uri, appname=\"devrel.showcase.gemma2.python\")\n", "\n", "    # Validate the connection\n", "    ping_result = client.admin.command(\"ping\")\n", "    if ping_result.get(\"ok\") == 1.0:\n", "        # Connection successful\n", "        print(\"Connection to MongoDB successful\")\n", "        return client\n", "    print(\"Connection to MongoDB failed\")\n", "    return None\n", "\n", "\n", "mongo_uri = os.environ[\"MONGO_URI\"]\n", "\n", "if not mongo_uri:\n", "    print(\"MONGO_URI not set in environment variables\")\n", "\n", "mongo_client = get_mongo_client(mongo_uri)\n", "\n", "DB_NAME = \"movie_rec_sys\"\n", "COLLECTION_NAME = \"movie_collection\"\n", "\n", "db = mongo_client.get_database(DB_NAME)\n", "collection = db.get_collection(COLLECTION_NAME)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5wV9TkDrXhZA", "outputId": "7e6a4b4e-283d-414e-c0eb-3f36b852afc3"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 0, 'electionId': ObjectId('7fffffff000000000000002a'), 'opTime': {'ts': Timestamp(1719600641, 43), 't': 42}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1719600641, 43), 'signature': {'hash': b'\\tS\\xb0Ja\\xd5\\x90\\xf6\\xed`\\xef>\\x94\\xab\\x05\\xd2[\\xdbM\\xc1', 'keyId': 7320226449804230662}}, 'operationTime': Timestamp(1719600641, 43)}, acknowledged=True)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Delete any existing records in the collection\n", "collection.delete_many({})"]}, {"cell_type": "markdown", "metadata": {"id": "dvvlzve5K9zf"}, "source": ["## Data Ingestion"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "F4obMlpzXkpL", "outputId": "5ef779a8-27f0-48f7-d91b-a29eba065a7b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data ingestion into MongoDB completed\n"]}], "source": ["documents = dataset_df.to_dict(\"records\")\n", "collection.insert_many(documents)\n", "\n", "print(\"Data ingestion into MongoDB completed\")"]}, {"cell_type": "markdown", "metadata": {"id": "8b_UwtFoLB-2"}, "source": ["## Vector Search Operation"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "3GOkbnqtXns_"}, "outputs": [], "source": ["def vector_search(user_query, collection):\n", "    \"\"\"\n", "    Perform a vector search in the MongoDB collection based on the user query.\n", "\n", "    Args:\n", "    user_query (str): The user's query string.\n", "    collection (MongoCollection): The MongoDB collection to search.\n", "\n", "    Returns:\n", "    list: A list of matching documents.\n", "    \"\"\"\n", "\n", "    # Generate embedding for the user query\n", "    query_embedding = get_embedding(user_query)\n", "\n", "    if query_embedding is None:\n", "        return \"Invalid query or embedding generation failed.\"\n", "\n", "    # Define the vector search pipeline\n", "    vector_search_stage = {\n", "        \"$vectorSearch\": {\n", "            \"index\": \"vector_index\",\n", "            \"queryVector\": query_embedding,\n", "            \"path\": \"embedding\",\n", "            \"numCandidates\": 150,  # Number of candidate matches to consider\n", "            \"limit\": 4,  # Return top 4 matches\n", "        }\n", "    }\n", "\n", "    unset_stage = {\n", "        \"$unset\": \"embedding\"  # Exclude the 'embedding' field from the results\n", "    }\n", "\n", "    project_stage = {\n", "        \"$project\": {\n", "            \"_id\": 0,  # Exclude the _id field\n", "            \"fullplot\": 1,  # Include the plot field\n", "            \"title\": 1,  # Include the title field\n", "            \"genres\": 1,  # Include the genres field\n", "            \"score\": {\n", "                \"$meta\": \"vectorSearchScore\"  # Include the search score\n", "            },\n", "        }\n", "    }\n", "\n", "    pipeline = [vector_search_stage, unset_stage, project_stage]\n", "\n", "    # Execute the search\n", "    results = collection.aggregate(pipeline)\n", "    return list(results)"]}, {"cell_type": "markdown", "metadata": {"id": "NF-A-0zELFX9"}, "source": ["## Handle User Results"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "7fGN2kK2Xsd1"}, "outputs": [], "source": ["def get_search_result(query, collection):\n", "    get_knowledge = vector_search(query, collection)\n", "\n", "    search_result = \"\"\n", "    for result in get_knowledge:\n", "        search_result += f\"Title: {result.get('title', 'N/A')}, Plot: {result.get('fullplot', 'N/A')}\\n\"\n", "\n", "    return search_result"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3ETL8LyEXtfc", "outputId": "3345ac90-e55e-42b3-e0d8-97d957c4f0e1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: What is the best romantic movie to watch and why?\n", "Continue to answer the query by using the Search Results:\n", "Title: Shut Up and Kiss Me!, Plot: <PERSON> and <PERSON> are 27-year old best friends in Miami, born on the same day and each searching for the perfect woman. <PERSON> is a rookie stockbroker living with his psychic <PERSON>. <PERSON> is a slick surfer dude yet to find commitment. Each meets the women of their dreams on the same day. <PERSON> knocks heads in an elevator with the gorgeous <PERSON>, passing out before getting her number. <PERSON> falls for the insatiable <PERSON><PERSON>, but <PERSON><PERSON>'s uncle is mob boss <PERSON>, charged with her protection. This high-energy romantic comedy asks to what extent will you go for true love?\n", "Title: Pearl Harbor, Plot: Pearl Harbor is a classic tale of romance set during a war that complicates everything. It all starts when childhood friends <PERSON> and <PERSON> become Army Air Corps pilots and meet <PERSON>, a Navy nurse. <PERSON> falls head over heels and next thing you know <PERSON> and <PERSON> are hooking up. Then <PERSON> volunteers to go fight in Britain and <PERSON> and <PERSON> get transferred to Pearl Harbor. While <PERSON> is off fighting everything gets completely whack and next thing you know everybody is in the middle of an air raid we now know as \"Pearl Harbor.\"\n", "Title: Titanic, Plot: The plot focuses on the romances of two couples upon the doomed ship's maiden voyage. <PERSON> (<PERSON>) is a wealthy woman mourning the loss of her aunt, who reignites a romance with former flame <PERSON><PERSON> (<PERSON>). Meanwhile, a charming ne'er-do-well named <PERSON> (<PERSON>) steals a ticket for the ship, and falls for a sweet innocent Irish girl on board. But their romance is threatened by the villainous <PERSON> (<PERSON>), who has discovered about the ticket and makes <PERSON> his unwilling accomplice, as well as having sinister plans for the girl.\n", "Title: China Girl, Plot: A modern day Romeo & Juliet story is told in New York when an Italian boy and a Chinese girl become lovers, causing a tragic conflict between ethnic gangs.\n", ".\n"]}], "source": ["# Conduct query with retrival of sources\n", "query = \"What is the best romantic movie to watch and why?\"\n", "source_information = get_search_result(query, collection)\n", "combined_information = f\"Query: {query}\\nContinue to answer the query by using the Search Results:\\n{source_information}.\"\n", "\n", "print(combined_information)"]}, {"cell_type": "markdown", "metadata": {"id": "TfsakemuLO-I"}, "source": ["## <PERSON><PERSON> 2"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 433, "referenced_widgets": ["f067e230df4b4fb4b43f37283308f24a", "2a20261c36974cdc97cd9a3ca3816e66", "9905e733da824c578c19c6d4836c2e61", "********************************", "5086d1964ca44d0090a0abb3d683a79d", "bd2a613f0ff84b28828adcb58b1da1f8", "1869b8f95f1e4622b3ed6bf70aaa1304", "30146a3333b74a1cb66f786fd6cff06f", "bfc38d4f56624c9da0cb8d9232335093", "219c2ea75fd240b3aeabf1be44f5d56f", "a67d5ab6d0d84a3e998e51a2344cace6", "3d96b91a79564055bd0ff4c5e0b0ad5d", "efcdfcf3b81c4874aaa60286015135e4", "cb17f94e8ff040a7a0587d62473ebc03", "0ee39adad78243efa0f250dcba61a6be", "bf8791b1705f43f7858781adeff64caa", "a942581c47c0496e803991509fa7981b", "91557dbe9fff427291a3a26ce4737692", "e37aeaaaf726451ebc5d55cac98516f7", "60695e511f8b40329b4246379dfec1e2", "3c35eac0744d45d1aab1932a24250a90", "430aa93b5f5e411b8e53169cd69a6e70", "9a5cf354ad72414d85a1c57e8677f996", "8adc0c8236934a518a908cf590780a93", "1dd3c304b5e9477aadc4bcc4146db4f4", "dda81e891f67426ea1c39f8426f0ff68", "55c093e8e910454da6dc095cd48bd813", "79ff2778559e43229e65651361ef902f", "dd4cf4cd0a67415d80c2f63d795fb7c0", "8ba3a3b074a84d5da9497bca0cdb6677", "5d2e9a9b14474fd7bd152f508d1cffb9", "6ae244d25ffb40b786de5a117da133cc", "5a5802787f1b4186bb53a8ba2d83771d", "c41b60ce525f4fbf8bfc8bead2836de4", "648c25c772e44da9b1d85ce425e27992", "5cc638cd2aa14fe9a5ea0edb6517a843", "ba3f6ff46c20401d9603358004b9ef79", "ab19997000c54b71a2e7ad0244f5e13f", "5e0f2f7d686c49c6af072c032a5dab71", "9be59a5eaf614bdfa82548cb59a1db65", "9f4565e88645462da2ce1d451a21ee17", "68f9b679a3ed43768b966717335cde1d", "d88b34f32a344bdf96a5ebeaef20914c", "c310a8b40f0e491080b1d61d64330f33", "69fb4131a1814acdaccf9073c256b3f7", "dc529b0ee05d4b3fb813cdb4d3ce81d2", "05d46e07ad1e4c56967d800ebb8ac7cd", "873a00bb2298421b8f10b16daa3be672", "8cdbe9cb268f4fdb8995ef3dc38c7250", "9005945bd6b64449a360c21c5fbfe224", "4f02774281d2445dbee86bebed37e5b2", "bb4720ebbf8f4bb8b92789720670acc0", "7727008f0e8c421e87a9480c213f2f8d", "d918bfced89443588e4091ae38b99ce4", "05942849c6844d26894eaa775297e497", "0f7628e0e14444efaff6202a3a4e1486", "3ca224f30c334684942fc48c78fc2a94", "33ab2f9c01814e1996c64173435ce03a", "a335d92674384cc5aba97b69092c3e6b", "45110c45914f4960b95b7f8e8a9d9dc8", "95bfb091ed3944b494b8debbe1882cca", "c44d340af41148c896243a4bb2765274", "ca9e369b65614bedb95e9b41c80a2a1d", "eebee189615a46afbfe0c53c735e700f", "bee98e2b239d4f86b86700064d48081d", "5cead09958164bd8b0d44a4e2a98bcbb", "562998e2b0cd4491afde251f2da7e875", "1a682016789f4db8ba1e5e575906692e", "a90307112b6f40bc94016c9e0ba78c79", "e6e26fc7717748de912443c7d1ca0e88", "a9f7a498212b45ed8d2557ce73433ff1", "c76b91c0f401472090e1fbfa86000c46", "d624527d60374c2bb8c61a029356b6d4", "354d4357b4bc47e1b9f9ac9be4965683", "f4b61e9d1262499bab3d04bd1654d201", "493e2c272eff462e9c2296f6c089fc8e", "c2a1b3c85ca04349b4cbf1e9fdd7475c", "8f94eb08070a42999f31c3a88804c045", "772ba82e0d02445b92d6e0acadc67d93", "e4c93a10a6b945c5ae9893397b7615f0", "ab0b1de522d244b49f0c98d515e55b5a", "1a61ce804ac34067a93326d6e79dd620", "7cb599f3187a4840b1beebd28fbc17e6", "90d6a76f16fe409e8a14e37b97a6ed40", "60b965546cde482a85071307850e94a3", "b3f6dba1106d4441939b8a1f9e479520", "3b74e0184c764b51a98c91ba529bedea", "77e1697d73bb43b9b2bd01f5eb13a180", "fb5b27b07d474f1b87dcbe7ceb3062b7", "1406c7c872be40bb986f610fa9df08a0", "a7ad64b18ac74515b7e87526394c8462", "54cb9b09b1ff4fd2b03941ef9764d736", "952a641b6f0a4847a64794227a6bae18", "7570b015931a45f0a40edff2a0dc13a9", "53c73b977ab64d2081501b9e4890eb82", "35a3b3ca65374da29286da068a747865", "dcf0d91486dc4d7fa7381f215c54912f", "4c2406671e8f4d469f9dc1835d7b58ba", "59818e71fb744ac287a7cc07514020f0", "520a95b1116249a7a85eebbadb284a9e", "559410616ff04c9c844df66cefdd4d32", "e5e429fb10e14e31b8c66e3e77867eac", "ae44d67c42d74277896c1a2753cb4e33", "ee76445245704862a1518228dad12636", "df5b6013ce634d5c99cdeb9afa8bca10", "e486284572194e518a8c290b416302a0", "28f2c9a25e664f43838e30ade2a105f5", "7142f90e97f94ad3bdc1ea6c4d9c5f5c", "99a4b4b432ac401a9f9191d0d612ec66", "e49321c9cd4c4433a4427a671aa405e7", "a070ab55acc040099e43bee8e3778ed7", "7c9d4bbb9dd34caa8f6cced1ff1a5224", "0863f2f8cbbc4ee29f0cdb59109f4a3a", "f6b50a861e8e4f47b742e2a19d73e11f", "d4abb18518c848df89993074cd04f754", "40f33b6d7313412682bc9b8eb73bd29e", "139c306b3ee24a1a80506977c1e095ef", "b945b19ff6e74a04b9fe8d94d7d6af69", "7710d76cc94b4034b12ebab0e5ce8dbe", "108ad072a2744c2ca3061c8584bce0d0", "a2ce7b63b1884629839bb92bfef023b9", "9dbb765c18cc4f29b39d555cee5c4de5", "c38687fe58cf47448ed97e88cffcec62", "fb6ff431c60e4e39bd397df94761747a", "4c51568e730a45919e155f2cd6e50c1f", "3b97f9770e5b442987e1caa32ea6f36d", "6150112a6e754cafbf487b8e03bdd1d5", "57b719fe1674453daaeefac94a7fb122", "3b7687fa9307414db22cb81c7aa7ae3e", "96e5d431d8804f11b8d96c67865bc4a6", "5151b8902a0243cdbe34a7ecf0cfb769", "d5ad1e31904240058dda36d075ccf220", "346e7011d8b34b808e2ee8d721cad99f", "9e89e7b1e27440c6afdea44421682cf7", "bee1999ea2204e53bd34b7221c863657", "7680574714914ea8ac6d48dddd9753e3", "0b359134060f4643bdd03fd627b2f290", "dbaf912a361e42bd8f4c47510be3c2b3", "25ff191eed6245f79d7c2c53fcf3bc59", "20d7189c4668480c8ca5fd84e538ecc4", "d23e31dfd8b04332b146d8b7c4a180f0", "cad1d26876af40c2a628ffe0ef59c804", "f59c6b89fe3443c4bba8457320692212"]}, "id": "nMZSSYH1XwbV", "outputId": "b4553418-065a-4e08-d85f-624ab9637540"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f067e230df4b4fb4b43f37283308f24a", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/40.6k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3d96b91a79564055bd0ff4c5e0b0ad5d", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/4.24M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9a5cf354ad72414d85a1c57e8677f996", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.5M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c41b60ce525f4fbf8bfc8bead2836de4", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/636 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "69fb4131a1814acdaccf9073c256b3f7", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/857 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0f7628e0e14444efaff6202a3a4e1486", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json:   0%|          | 0.00/39.1k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "562998e2b0cd4491afde251f2da7e875", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8f94eb08070a42999f31c3a88804c045", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00004.safetensors:   0%|          | 0.00/4.90G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fb5b27b07d474f1b87dcbe7ceb3062b7", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00004.safetensors:   0%|          | 0.00/4.95G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "520a95b1116249a7a85eebbadb284a9e", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00003-of-00004.safetensors:   0%|          | 0.00/4.96G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a070ab55acc040099e43bee8e3778ed7", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00004-of-00004.safetensors:   0%|          | 0.00/3.67G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9dbb765c18cc4f29b39d555cee5c4de5", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "346e7011d8b34b808e2ee8d721cad99f", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/173 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2-9b-it\")\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    \"google/gemma-2-9b-it\", device_map=\"auto\", torch_dtype=torch.bfloat16\n", ")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pUt4aVm5XzUv", "outputId": "54d4cba7-f9d6-4c5b-d84f-ee775d558437"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bos>Query: What is the best romantic movie to watch and why?\n", "Continue to answer the query by using the Search Results:\n", "Title: Shut Up and Kiss Me!, Plot: <PERSON> and <PERSON> are 27-year old best friends in Miami, born on the same day and each searching for the perfect woman. <PERSON> is a rookie stockbroker living with his psychic <PERSON>. <PERSON> is a slick surfer dude yet to find commitment. Each meets the women of their dreams on the same day. <PERSON> knocks heads in an elevator with the gorgeous <PERSON>, passing out before getting her number. <PERSON> falls for the insatiable <PERSON><PERSON>, but <PERSON><PERSON>'s uncle is mob boss <PERSON>, charged with her protection. This high-energy romantic comedy asks to what extent will you go for true love?\n", "Title: Pearl Harbor, Plot: Pearl Harbor is a classic tale of romance set during a war that complicates everything. It all starts when childhood friends <PERSON> and <PERSON> become Army Air Corps pilots and meet <PERSON>, a Navy nurse. <PERSON> falls head over heels and next thing you know <PERSON> and <PERSON> are hooking up. Then <PERSON> volunteers to go fight in Britain and <PERSON> and <PERSON> get transferred to Pearl Harbor. While <PERSON> is off fighting everything gets completely whack and next thing you know everybody is in the middle of an air raid we now know as \"Pearl Harbor.\"\n", "Title: Titanic, Plot: The plot focuses on the romances of two couples upon the doomed ship's maiden voyage. <PERSON> (<PERSON>) is a wealthy woman mourning the loss of her aunt, who reignites a romance with former flame <PERSON><PERSON> (<PERSON>). Meanwhile, a charming ne'er-do-well named <PERSON> (<PERSON>) steals a ticket for the ship, and falls for a sweet innocent Irish girl on board. But their romance is threatened by the villainous <PERSON> (<PERSON>), who has discovered about the ticket and makes <PERSON> his unwilling accomplice, as well as having sinister plans for the girl.\n", "Title: China Girl, Plot: A modern day Romeo & Juliet story is told in New York when an Italian boy and a Chinese girl become lovers, causing a tragic conflict between ethnic gangs.\n", ".\n", "\n", "Based on the provided movie plots, which one would you recommend as the best romantic movie and why?\n", "\n", "I would recommend **Shut Up and Kiss Me!** as the best romantic movie from this list. Here's\n"]}], "source": ["input_ids = tokenizer(combined_information, return_tensors=\"pt\").to(\"cuda\")\n", "outputs = model.generate(**input_ids, max_length=500)\n", "print(tokenizer.decode(outputs[0]))"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "A100", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"01bf1fca1c0f4189b3fa1e9b87c41ba0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "05942849c6844d26894eaa775297e497": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "05d46e07ad1e4c56967d800ebb8ac7cd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bb4720ebbf8f4bb8b92789720670acc0", "max": 857, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7727008f0e8c421e87a9480c213f2f8d", "value": 857}}, "06f675a549d7475bb1bb53216aab8013": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "07c11c417b2041dcbff3c62bf9fd410b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e5624cfc65d84f1cb193080dc1919758", "placeholder": "​", "style": "IPY_MODEL_38cf7c5ca6e1476cb01e655c58d856a8", "value": " 6.17k/6.17k [00:00&lt;00:00, 406kB/s]"}}, "0863f2f8cbbc4ee29f0cdb59109f4a3a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b945b19ff6e74a04b9fe8d94d7d6af69", "max": 3670322200, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7710d76cc94b4034b12ebab0e5ce8dbe", "value": 3670322200}}, "09c92d9bba7e4827a8462125ecd2fbb5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0b359134060f4643bdd03fd627b2f290": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0ea6c486a6e3472396f51d25828f183b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0ee39adad78243efa0f250dcba61a6be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3c35eac0744d45d1aab1932a24250a90", "placeholder": "​", "style": "IPY_MODEL_430aa93b5f5e411b8e53169cd69a6e70", "value": " 4.24M/4.24M [00:00&lt;00:00, 24.7MB/s]"}}, "0f7628e0e14444efaff6202a3a4e1486": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3ca224f30c334684942fc48c78fc2a94", "IPY_MODEL_33ab2f9c01814e1996c64173435ce03a", "IPY_MODEL_a335d92674384cc5aba97b69092c3e6b"], "layout": "IPY_MODEL_45110c45914f4960b95b7f8e8a9d9dc8"}}, "108ad072a2744c2ca3061c8584bce0d0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "113070d604954fe2851f68d0a65a517f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "13922824f7eb4a9fa9daaca028290ff1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e21740a1fe1e47f593409a23563265fa", "IPY_MODEL_21d3f7f6d60c4733960e1fac14837551", "IPY_MODEL_9b1463160d89457699e8cb20f52cdd53"], "layout": "IPY_MODEL_85fad341ac03441284d0f79afc4c0edc"}}, "139c306b3ee24a1a80506977c1e095ef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1406c7c872be40bb986f610fa9df08a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7570b015931a45f0a40edff2a0dc13a9", "placeholder": "​", "style": "IPY_MODEL_53c73b977ab64d2081501b9e4890eb82", "value": "model-00002-of-00004.safetensors: 100%"}}, "157ec39934a44e9881e67e3a960a31ba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1869b8f95f1e4622b3ed6bf70aaa1304": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1a61ce804ac34067a93326d6e79dd620": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a682016789f4db8ba1e5e575906692e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c76b91c0f401472090e1fbfa86000c46", "placeholder": "​", "style": "IPY_MODEL_d624527d60374c2bb8c61a029356b6d4", "value": "Downloading shards: 100%"}}, "1bec5087aacc48b9b786ae5fd5445085": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1d7dd8b4fc824566a92567e78e45feb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e4fbdc5e183f46308ccc88b62e6c3b43", "IPY_MODEL_966ae244eb7049008193fa4ca0f8702c", "IPY_MODEL_afdb3bb004d341268c927b0c58bf9128"], "layout": "IPY_MODEL_8ce370ae0e844c4f89f0f0ed51d1d614"}}, "1dd3c304b5e9477aadc4bcc4146db4f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ba3a3b074a84d5da9497bca0cdb6677", "max": 17518525, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5d2e9a9b14474fd7bd152f508d1cffb9", "value": 17518525}}, "1eabd224899a4e079667c1e385f672cc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "20d7189c4668480c8ca5fd84e538ecc4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "21130c13fd894d9f81f663f9477c8202": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e35eceac5a0f4a15b0217c6e6e890d1b", "placeholder": "​", "style": "IPY_MODEL_751ef5769dcb49d4aec0e9ba714d3a98", "value": " 67.9k/67.9k [00:00&lt;00:00, 4.67MB/s]"}}, "219c2ea75fd240b3aeabf1be44f5d56f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "21d3f7f6d60c4733960e1fac14837551": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9253f97927084862846fdc4edd66e26f", "max": 385, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_33bd7abc88314415ab8acad6c4bd2a59", "value": 385}}, "23e0098f59854da2a408d9f229fe482b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_679d124f567e47f186f8b7d38037cf16", "placeholder": "​", "style": "IPY_MODEL_b93d153824b243b68c48017f45db3139", "value": "Downloading readme: 100%"}}, "25ff191eed6245f79d7c2c53fcf3bc59": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "26e0b18819a647b2a64970544f80edaa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "28129422cd4042f8ba837ed812c714dd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "28f2c9a25e664f43838e30ade2a105f5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "28fdd8aaf14f41fab7609a98767ce96c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6cef4be37536439ab53d0d9b06edc23c", "max": 342, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_81d3260445d24e0292b51f7f361031a0", "value": 342}}, "29c7b957d65842fba09879dfbf07e0d5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "29de5219c1a5407b8f49d20693bceb5e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4646c04e22944a77ae246a8022babe56", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_26e0b18819a647b2a64970544f80edaa", "value": 231508}}, "2a04c4ab8dd04d289c7356139e111604": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_38096d247b484dfd9eca1bfe0cb0869b", "placeholder": "​", "style": "IPY_MODEL_2d69f9660e4f459493007dd39eac4cad", "value": "README.md: 100%"}}, "2a20261c36974cdc97cd9a3ca3816e66": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bd2a613f0ff84b28828adcb58b1da1f8", "placeholder": "​", "style": "IPY_MODEL_1869b8f95f1e4622b3ed6bf70aaa1304", "value": "tokenizer_config.json: 100%"}}, "2d5272e42bcf4d9d92f1f820fa736237": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2d69f9660e4f459493007dd39eac4cad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2dc48f9fae434dceb3bc21db3a2c6ed2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2f4f48920cce4de78df7c9f782f44240": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dac60c6af6f4412c91d40246095a607f", "placeholder": "​", "style": "IPY_MODEL_9531382c5465446e8a2802017f13b02b", "value": "model.safetensors: 100%"}}, "30146a3333b74a1cb66f786fd6cff06f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33ab2f9c01814e1996c64173435ce03a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ca9e369b65614bedb95e9b41c80a2a1d", "max": 39072, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_eebee189615a46afbfe0c53c735e700f", "value": 39072}}, "33bd7abc88314415ab8acad6c4bd2a59": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "346e7011d8b34b808e2ee8d721cad99f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9e89e7b1e27440c6afdea44421682cf7", "IPY_MODEL_bee1999ea2204e53bd34b7221c863657", "IPY_MODEL_7680574714914ea8ac6d48dddd9753e3"], "layout": "IPY_MODEL_0b359134060f4643bdd03fd627b2f290"}}, "354d4357b4bc47e1b9f9ac9be4965683": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35a3b3ca65374da29286da068a747865": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "36ebd34bf019428db97f2939def00345": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "38096d247b484dfd9eca1bfe0cb0869b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "38cf7c5ca6e1476cb01e655c58d856a8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "********************************": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_219c2ea75fd240b3aeabf1be44f5d56f", "placeholder": "​", "style": "IPY_MODEL_a67d5ab6d0d84a3e998e51a2344cace6", "value": " 40.6k/40.6k [00:00&lt;00:00, 2.97MB/s]"}}, "3ace6127a48a47b6993fb5f59e5edcf2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fa60f0b2621f49efa9aa67909e0b10cb", "IPY_MODEL_dbfba6717f604929b0ab1a396fe8f014", "IPY_MODEL_7c06a7cf9bbc406d9bbe2ba6f1de6e8c"], "layout": "IPY_MODEL_def05e65e2f44423aa05cedb4f83431b"}}, "3b74e0184c764b51a98c91ba529bedea": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3b7687fa9307414db22cb81c7aa7ae3e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3b97f9770e5b442987e1caa32ea6f36d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3c35eac0744d45d1aab1932a24250a90": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3c41505982b6465aab66844d1c1c5bb7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c29219a1b29c402aad7c11e804823b1d", "max": 125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_aa5997e1dbba4955a1ccbbbde45cb93d", "value": 125}}, "3ca224f30c334684942fc48c78fc2a94": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_95bfb091ed3944b494b8debbe1882cca", "placeholder": "​", "style": "IPY_MODEL_c44d340af41148c896243a4bb2765274", "value": "model.safetensors.index.json: 100%"}}, "3d96b91a79564055bd0ff4c5e0b0ad5d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_efcdfcf3b81c4874aaa60286015135e4", "IPY_MODEL_cb17f94e8ff040a7a0587d62473ebc03", "IPY_MODEL_0ee39adad78243efa0f250dcba61a6be"], "layout": "IPY_MODEL_bf8791b1705f43f7858781adeff64caa"}}, "40f33b6d7313412682bc9b8eb73bd29e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "426035a02eb54a6d8ebd8ea210bc284d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "430aa93b5f5e411b8e53169cd69a6e70": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "45110c45914f4960b95b7f8e8a9d9dc8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "45dcb668409f45e281a6ac9432b035d7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4646c04e22944a77ae246a8022babe56": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "493e2c272eff462e9c2296f6c089fc8e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c2406671e8f4d469f9dc1835d7b58ba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c51568e730a45919e155f2cd6e50c1f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5151b8902a0243cdbe34a7ecf0cfb769", "placeholder": "​", "style": "IPY_MODEL_d5ad1e31904240058dda36d075ccf220", "value": " 4/4 [00:06&lt;00:00,  1.69s/it]"}}, "4dfe2f30154c41d89a223e73473d17bb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f02774281d2445dbee86bebed37e5b2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5086d1964ca44d0090a0abb3d683a79d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5151b8902a0243cdbe34a7ecf0cfb769": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "51f41a92d0d24c93a85bd0cdd12baf96": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7144f5c190064859b404376a57309be7", "IPY_MODEL_28fdd8aaf14f41fab7609a98767ce96c", "IPY_MODEL_fa813647eeb94bfeaf3e2508f8f61cdc"], "layout": "IPY_MODEL_2dc48f9fae434dceb3bc21db3a2c6ed2"}}, "520a95b1116249a7a85eebbadb284a9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_559410616ff04c9c844df66cefdd4d32", "IPY_MODEL_e5e429fb10e14e31b8c66e3e77867eac", "IPY_MODEL_ae44d67c42d74277896c1a2753cb4e33"], "layout": "IPY_MODEL_ee76445245704862a1518228dad12636"}}, "531dfe254eff4d9485b7fab2dfeacf6e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "53247cc4267c46ba924b81914a2837e9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "53c73b977ab64d2081501b9e4890eb82": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "54cb9b09b1ff4fd2b03941ef9764d736": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4c2406671e8f4d469f9dc1835d7b58ba", "placeholder": "​", "style": "IPY_MODEL_59818e71fb744ac287a7cc07514020f0", "value": " 4.95G/4.95G [00:22&lt;00:00, 224MB/s]"}}, "559410616ff04c9c844df66cefdd4d32": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_df5b6013ce634d5c99cdeb9afa8bca10", "placeholder": "​", "style": "IPY_MODEL_e486284572194e518a8c290b416302a0", "value": "model-00003-of-00004.safetensors: 100%"}}, "55c093e8e910454da6dc095cd48bd813": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "562998e2b0cd4491afde251f2da7e875": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1a682016789f4db8ba1e5e575906692e", "IPY_MODEL_a90307112b6f40bc94016c9e0ba78c79", "IPY_MODEL_e6e26fc7717748de912443c7d1ca0e88"], "layout": "IPY_MODEL_a9f7a498212b45ed8d2557ce73433ff1"}}, "56f59cc557444a5cbd267ed596936198": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "57b719fe1674453daaeefac94a7fb122": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "59818e71fb744ac287a7cc07514020f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5a5802787f1b4186bb53a8ba2d83771d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5b9cc5943305436c86c322131908ad54": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c295ef2733a49ce80805fcc6895e2fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5cc638cd2aa14fe9a5ea0edb6517a843": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9f4565e88645462da2ce1d451a21ee17", "max": 636, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_68f9b679a3ed43768b966717335cde1d", "value": 636}}, "5cead09958164bd8b0d44a4e2a98bcbb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5d2e9a9b14474fd7bd152f508d1cffb9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5e0f2f7d686c49c6af072c032a5dab71": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5eb5a8b647364a6a83733423538a6f17": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "60695e511f8b40329b4246379dfec1e2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "60b965546cde482a85071307850e94a3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6150112a6e754cafbf487b8e03bdd1d5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "648c25c772e44da9b1d85ce425e27992": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5e0f2f7d686c49c6af072c032a5dab71", "placeholder": "​", "style": "IPY_MODEL_9be59a5eaf614bdfa82548cb59a1db65", "value": "special_tokens_map.json: 100%"}}, "66a7186430064ea3b2edf40866d7b3a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "679d124f567e47f186f8b7d38037cf16": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68f9b679a3ed43768b966717335cde1d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "69fb4131a1814acdaccf9073c256b3f7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_dc529b0ee05d4b3fb813cdb4d3ce81d2", "IPY_MODEL_05d46e07ad1e4c56967d800ebb8ac7cd", "IPY_MODEL_873a00bb2298421b8f10b16daa3be672"], "layout": "IPY_MODEL_8cdbe9cb268f4fdb8995ef3dc38c7250"}}, "6ae244d25ffb40b786de5a117da133cc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6b086ada3646438082c1bad298e1a7f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a0657b2393064a64ba3c9d6e3e9ad098", "IPY_MODEL_e47b416da6e540b28e7323452631fbd2", "IPY_MODEL_e4006278e9a440ed8e3828eaae4bdcd7"], "layout": "IPY_MODEL_97c348b47ed0405198fcbf1724d0aafc"}}, "6cef4be37536439ab53d0d9b06edc23c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f45b925a0dc487389724a1d36768cc2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a30de3a43306419b9ab44a08d342b869", "max": 619, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_56f59cc557444a5cbd267ed596936198", "value": 619}}, "7142f90e97f94ad3bdc1ea6c4d9c5f5c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7144f5c190064859b404376a57309be7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a205dd66663049f0baabc4c499327515", "placeholder": "​", "style": "IPY_MODEL_90df64cedf344ce4aac9fa06ab7ec1ab", "value": "tokenizer_config.json: 100%"}}, "751ef5769dcb49d4aec0e9ba714d3a98": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7570b015931a45f0a40edff2a0dc13a9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7680574714914ea8ac6d48dddd9753e3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cad1d26876af40c2a628ffe0ef59c804", "placeholder": "​", "style": "IPY_MODEL_f59c6b89fe3443c4bba8457320692212", "value": " 173/173 [00:00&lt;00:00, 14.0kB/s]"}}, "76a32f0cea704609a33023343b65dc32": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7710d76cc94b4034b12ebab0e5ce8dbe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7727008f0e8c421e87a9480c213f2f8d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "772ba82e0d02445b92d6e0acadc67d93": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7cb599f3187a4840b1beebd28fbc17e6", "placeholder": "​", "style": "IPY_MODEL_90d6a76f16fe409e8a14e37b97a6ed40", "value": "model-00001-of-00004.safetensors: 100%"}}, "77e1697d73bb43b9b2bd01f5eb13a180": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "79ff2778559e43229e65651361ef902f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b5b53cda2da45b0859fcba6aa8e57cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7c06a7cf9bbc406d9bbe2ba6f1de6e8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cd1c98560aa14452ac0f4429e6ec4d8a", "placeholder": "​", "style": "IPY_MODEL_7b5b53cda2da45b0859fcba6aa8e57cb", "value": " 191/191 [00:00&lt;00:00, 12.8kB/s]"}}, "7c9d4bbb9dd34caa8f6cced1ff1a5224": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_40f33b6d7313412682bc9b8eb73bd29e", "placeholder": "​", "style": "IPY_MODEL_139c306b3ee24a1a80506977c1e095ef", "value": "model-00004-of-00004.safetensors: 100%"}}, "7cb599f3187a4840b1beebd28fbc17e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7d1255955d7f4d5d822d167aec268095": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7faac74bd69c4bb9951ee85a5c35a219": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "80ef8e629a9f4e3fa0bb480011d69c83": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "81d3260445d24e0292b51f7f361031a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "85fad341ac03441284d0f79afc4c0edc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "860798785a9b4728924aff2f303d628f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "873a00bb2298421b8f10b16daa3be672": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d918bfced89443588e4091ae38b99ce4", "placeholder": "​", "style": "IPY_MODEL_05942849c6844d26894eaa775297e497", "value": " 857/857 [00:00&lt;00:00, 67.1kB/s]"}}, "8adc0c8236934a518a908cf590780a93": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_79ff2778559e43229e65651361ef902f", "placeholder": "​", "style": "IPY_MODEL_dd4cf4cd0a67415d80c2f63d795fb7c0", "value": "tokenizer.json: 100%"}}, "8b02b770dd654d609d0483e2d06006a9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4dfe2f30154c41d89a223e73473d17bb", "placeholder": "​", "style": "IPY_MODEL_acee94b8731b481293d2bce48bbb343f", "value": " 619/619 [00:00&lt;00:00, 47.7kB/s]"}}, "8b7fd56cef624959b4236a2f3d00d18f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2a04c4ab8dd04d289c7356139e111604", "IPY_MODEL_b64d751a8c604063afba528bbf3460ad", "IPY_MODEL_21130c13fd894d9f81f663f9477c8202"], "layout": "IPY_MODEL_5b9cc5943305436c86c322131908ad54"}}, "8ba3a3b074a84d5da9497bca0cdb6677": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ba541e61ea846f8b641294de98897a7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8cdbe9cb268f4fdb8995ef3dc38c7250": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ce370ae0e844c4f89f0f0ed51d1d614": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f83b31014524ec1abaa879697b46d7b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ba541e61ea846f8b641294de98897a7", "placeholder": "​", "style": "IPY_MODEL_bf0decd455834c5793275c0ad1006fd6", "value": "special_tokens_map.json: 100%"}}, "8f94eb08070a42999f31c3a88804c045": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_772ba82e0d02445b92d6e0acadc67d93", "IPY_MODEL_e4c93a10a6b945c5ae9893397b7615f0", "IPY_MODEL_ab0b1de522d244b49f0c98d515e55b5a"], "layout": "IPY_MODEL_1a61ce804ac34067a93326d6e79dd620"}}, "9005945bd6b64449a360c21c5fbfe224": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "907f122633ba4d4fa29a2a30c1109c2e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "90d6a76f16fe409e8a14e37b97a6ed40": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "90df64cedf344ce4aac9fa06ab7ec1ab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "91342c5ebe334efda4c002e3f7e5d2a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "91557dbe9fff427291a3a26ce4737692": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9253f97927084862846fdc4edd66e26f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "92ebc28e906346caa277db29b4519a2b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "952a641b6f0a4847a64794227a6bae18": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9531382c5465446e8a2802017f13b02b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "95bfb091ed3944b494b8debbe1882cca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "966ae244eb7049008193fa4ca0f8702c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_29c7b957d65842fba09879dfbf07e0d5", "max": 711661, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7faac74bd69c4bb9951ee85a5c35a219", "value": 711661}}, "96e5d431d8804f11b8d96c67865bc4a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "97c348b47ed0405198fcbf1724d0aafc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9905e733da824c578c19c6d4836c2e61": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_30146a3333b74a1cb66f786fd6cff06f", "max": 40608, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bfc38d4f56624c9da0cb8d9232335093", "value": 40608}}, "99a4b4b432ac401a9f9191d0d612ec66": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9a5cf354ad72414d85a1c57e8677f996": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8adc0c8236934a518a908cf590780a93", "IPY_MODEL_1dd3c304b5e9477aadc4bcc4146db4f4", "IPY_MODEL_dda81e891f67426ea1c39f8426f0ff68"], "layout": "IPY_MODEL_55c093e8e910454da6dc095cd48bd813"}}, "9b1463160d89457699e8cb20f52cdd53": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_36ebd34bf019428db97f2939def00345", "placeholder": "​", "style": "IPY_MODEL_cee177a2947348cc996c517e699daf8b", "value": " 385/385 [00:00&lt;00:00, 26.4kB/s]"}}, "9be59a5eaf614bdfa82548cb59a1db65": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9dbb765c18cc4f29b39d555cee5c4de5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c38687fe58cf47448ed97e88cffcec62", "IPY_MODEL_fb6ff431c60e4e39bd397df94761747a", "IPY_MODEL_4c51568e730a45919e155f2cd6e50c1f"], "layout": "IPY_MODEL_3b97f9770e5b442987e1caa32ea6f36d"}}, "9e89e7b1e27440c6afdea44421682cf7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dbaf912a361e42bd8f4c47510be3c2b3", "placeholder": "​", "style": "IPY_MODEL_25ff191eed6245f79d7c2c53fcf3bc59", "value": "generation_config.json: 100%"}}, "9f4565e88645462da2ce1d451a21ee17": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a0657b2393064a64ba3c9d6e3e9ad098": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_09c92d9bba7e4827a8462125ecd2fbb5", "placeholder": "​", "style": "IPY_MODEL_ad05522d58f64d5c9ea2f3d267b3a4d6", "value": "sentence_bert_config.json: 100%"}}, "a070ab55acc040099e43bee8e3778ed7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7c9d4bbb9dd34caa8f6cced1ff1a5224", "IPY_MODEL_0863f2f8cbbc4ee29f0cdb59109f4a3a", "IPY_MODEL_f6b50a861e8e4f47b742e2a19d73e11f"], "layout": "IPY_MODEL_d4abb18518c848df89993074cd04f754"}}, "a11a6a665e0241f58a836accc3f3351d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e877369337da47fc9750cb360c41b640", "max": 670332568, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_45dcb668409f45e281a6ac9432b035d7", "value": 670332568}}, "a205dd66663049f0baabc4c499327515": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a2ce7b63b1884629839bb92bfef023b9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a30de3a43306419b9ab44a08d342b869": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a335d92674384cc5aba97b69092c3e6b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bee98e2b239d4f86b86700064d48081d", "placeholder": "​", "style": "IPY_MODEL_5cead09958164bd8b0d44a4e2a98bcbb", "value": " 39.1k/39.1k [00:00&lt;00:00, 3.10MB/s]"}}, "a67d5ab6d0d84a3e998e51a2344cace6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a7ad64b18ac74515b7e87526394c8462": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_35a3b3ca65374da29286da068a747865", "max": 4947570872, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_dcf0d91486dc4d7fa7381f215c54912f", "value": 4947570872}}, "a7cc12ff38e2471d98292232ec2fef40": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bdab962e4eeb44daa31f6782958e7501", "IPY_MODEL_29de5219c1a5407b8f49d20693bceb5e", "IPY_MODEL_e226fdf7b27649abb898ead54220826f"], "layout": "IPY_MODEL_860798785a9b4728924aff2f303d628f"}}, "a90307112b6f40bc94016c9e0ba78c79": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_354d4357b4bc47e1b9f9ac9be4965683", "max": 4, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f4b61e9d1262499bab3d04bd1654d201", "value": 4}}, "a90a64a9873b4112a2f5ffcfde06411e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a942581c47c0496e803991509fa7981b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a9f7a498212b45ed8d2557ce73433ff1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aa5997e1dbba4955a1ccbbbde45cb93d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ab0b1de522d244b49f0c98d515e55b5a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3b74e0184c764b51a98c91ba529bedea", "placeholder": "​", "style": "IPY_MODEL_77e1697d73bb43b9b2bd01f5eb13a180", "value": " 4.90G/4.90G [00:22&lt;00:00, 233MB/s]"}}, "ab19997000c54b71a2e7ad0244f5e13f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "acdd038e8c7140e2af6a9084bbd42410": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "acee94b8731b481293d2bce48bbb343f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ad05522d58f64d5c9ea2f3d267b3a4d6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ae44d67c42d74277896c1a2753cb4e33": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_99a4b4b432ac401a9f9191d0d612ec66", "placeholder": "​", "style": "IPY_MODEL_e49321c9cd4c4433a4427a671aa405e7", "value": " 4.96G/4.96G [00:21&lt;00:00, 238MB/s]"}}, "afdb3bb004d341268c927b0c58bf9128": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_80ef8e629a9f4e3fa0bb480011d69c83", "placeholder": "​", "style": "IPY_MODEL_d14a2456a14348c5b6c1070494000339", "value": " 712k/712k [00:00&lt;00:00, 3.62MB/s]"}}, "b3f6dba1106d4441939b8a1f9e479520": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b4bb9facf9644a1aa03d5886215e69fb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_28129422cd4042f8ba837ed812c714dd", "placeholder": "​", "style": "IPY_MODEL_1bec5087aacc48b9b786ae5fd5445085", "value": "config.json: 100%"}}, "b5f9fad01d7f4e2ca51d2116004478e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2f4f48920cce4de78df7c9f782f44240", "IPY_MODEL_a11a6a665e0241f58a836accc3f3351d", "IPY_MODEL_c762578ed8324a9bb933193b3d76690a"], "layout": "IPY_MODEL_df6e9ea5d47341caa0e78505560b7b4d"}}, "b64d751a8c604063afba528bbf3460ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ef38b69a2acc47d5ba47e66cb58764a8", "max": 67863, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a90a64a9873b4112a2f5ffcfde06411e", "value": 67863}}, "b902ddfd9f52410cb70bd73c477683b4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_acdd038e8c7140e2af6a9084bbd42410", "max": 6172, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_91342c5ebe334efda4c002e3f7e5d2a3", "value": 6172}}, "b93d153824b243b68c48017f45db3139": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b945b19ff6e74a04b9fe8d94d7d6af69": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ba3f6ff46c20401d9603358004b9ef79": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d88b34f32a344bdf96a5ebeaef20914c", "placeholder": "​", "style": "IPY_MODEL_c310a8b40f0e491080b1d61d64330f33", "value": " 636/636 [00:00&lt;00:00, 49.7kB/s]"}}, "bb4720ebbf8f4bb8b92789720670acc0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bd2a613f0ff84b28828adcb58b1da1f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bdab962e4eeb44daa31f6782958e7501": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_53247cc4267c46ba924b81914a2837e9", "placeholder": "​", "style": "IPY_MODEL_5c295ef2733a49ce80805fcc6895e2fa", "value": "vocab.txt: 100%"}}, "bdc8dbca7607474a91309a5be1c30938": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bee1999ea2204e53bd34b7221c863657": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_20d7189c4668480c8ca5fd84e538ecc4", "max": 173, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d23e31dfd8b04332b146d8b7c4a180f0", "value": 173}}, "bee98e2b239d4f86b86700064d48081d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf0decd455834c5793275c0ad1006fd6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bf8791b1705f43f7858781adeff64caa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bfc38d4f56624c9da0cb8d9232335093": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c133bac838ca47fa99a9de8f5c48ae41": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c29219a1b29c402aad7c11e804823b1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c2a1b3c85ca04349b4cbf1e9fdd7475c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c310a8b40f0e491080b1d61d64330f33": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c38687fe58cf47448ed97e88cffcec62": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6150112a6e754cafbf487b8e03bdd1d5", "placeholder": "​", "style": "IPY_MODEL_57b719fe1674453daaeefac94a7fb122", "value": "Loading checkpoint shards: 100%"}}, "c41b60ce525f4fbf8bfc8bead2836de4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_648c25c772e44da9b1d85ce425e27992", "IPY_MODEL_5cc638cd2aa14fe9a5ea0edb6517a843", "IPY_MODEL_ba3f6ff46c20401d9603358004b9ef79"], "layout": "IPY_MODEL_ab19997000c54b71a2e7ad0244f5e13f"}}, "c44d340af41148c896243a4bb2765274": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c762578ed8324a9bb933193b3d76690a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_157ec39934a44e9881e67e3a960a31ba", "placeholder": "​", "style": "IPY_MODEL_66a7186430064ea3b2edf40866d7b3a3", "value": " 670M/670M [00:02&lt;00:00, 241MB/s]"}}, "c76b91c0f401472090e1fbfa86000c46": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ca9e369b65614bedb95e9b41c80a2a1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cad1d26876af40c2a628ffe0ef59c804": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cb17f94e8ff040a7a0587d62473ebc03": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e37aeaaaf726451ebc5d55cac98516f7", "max": 4241003, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_60695e511f8b40329b4246379dfec1e2", "value": 4241003}}, "cd1c98560aa14452ac0f4429e6ec4d8a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cee177a2947348cc996c517e699daf8b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d11c848a53404576a89d7a7ecee559df": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8f83b31014524ec1abaa879697b46d7b", "IPY_MODEL_3c41505982b6465aab66844d1c1c5bb7", "IPY_MODEL_d3a6ddd12b314e0bb7dce664b193bf09"], "layout": "IPY_MODEL_113070d604954fe2851f68d0a65a517f"}}, "d14a2456a14348c5b6c1070494000339": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d23e31dfd8b04332b146d8b7c4a180f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d3a6ddd12b314e0bb7dce664b193bf09": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e8693a705cdf46eaa4cb5143159108be", "placeholder": "​", "style": "IPY_MODEL_76a32f0cea704609a33023343b65dc32", "value": " 125/125 [00:00&lt;00:00, 9.48kB/s]"}}, "d4abb18518c848df89993074cd04f754": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d5ad1e31904240058dda36d075ccf220": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d624527d60374c2bb8c61a029356b6d4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d88b34f32a344bdf96a5ebeaef20914c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d918bfced89443588e4091ae38b99ce4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "da00bae6673b4d709ef1b7317e1450b8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "da7515953b974f7399c259bf5fcb24ab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "dac60c6af6f4412c91d40246095a607f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dbaf912a361e42bd8f4c47510be3c2b3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dbfba6717f604929b0ab1a396fe8f014": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e97aec423d75440690458f2131563ccd", "max": 191, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_da7515953b974f7399c259bf5fcb24ab", "value": 191}}, "dc529b0ee05d4b3fb813cdb4d3ce81d2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9005945bd6b64449a360c21c5fbfe224", "placeholder": "​", "style": "IPY_MODEL_4f02774281d2445dbee86bebed37e5b2", "value": "config.json: 100%"}}, "dcf0d91486dc4d7fa7381f215c54912f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "dd4cf4cd0a67415d80c2f63d795fb7c0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dda81e891f67426ea1c39f8426f0ff68": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ae244d25ffb40b786de5a117da133cc", "placeholder": "​", "style": "IPY_MODEL_5a5802787f1b4186bb53a8ba2d83771d", "value": " 17.5M/17.5M [00:00&lt;00:00, 98.6MB/s]"}}, "def05e65e2f44423aa05cedb4f83431b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df5b6013ce634d5c99cdeb9afa8bca10": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df6e9ea5d47341caa0e78505560b7b4d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e21740a1fe1e47f593409a23563265fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_426035a02eb54a6d8ebd8ea210bc284d", "placeholder": "​", "style": "IPY_MODEL_f9be4a1aa4384f8dafbd7270ee688f58", "value": "modules.json: 100%"}}, "e226fdf7b27649abb898ead54220826f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_92ebc28e906346caa277db29b4519a2b", "placeholder": "​", "style": "IPY_MODEL_bdc8dbca7607474a91309a5be1c30938", "value": " 232k/232k [00:00&lt;00:00, 3.22MB/s]"}}, "e33f6b3a1247488890282df389b6ed61": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_23e0098f59854da2a408d9f229fe482b", "IPY_MODEL_b902ddfd9f52410cb70bd73c477683b4", "IPY_MODEL_07c11c417b2041dcbff3c62bf9fd410b"], "layout": "IPY_MODEL_0ea6c486a6e3472396f51d25828f183b"}}, "e35eceac5a0f4a15b0217c6e6e890d1b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e37aeaaaf726451ebc5d55cac98516f7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e4006278e9a440ed8e3828eaae4bdcd7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1eabd224899a4e079667c1e385f672cc", "placeholder": "​", "style": "IPY_MODEL_5eb5a8b647364a6a83733423538a6f17", "value": " 57.0/57.0 [00:00&lt;00:00, 4.41kB/s]"}}, "e47b416da6e540b28e7323452631fbd2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_da00bae6673b4d709ef1b7317e1450b8", "max": 57, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fee5fffb9ebf471d9ecb912c3fd26c91", "value": 57}}, "e486284572194e518a8c290b416302a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e49321c9cd4c4433a4427a671aa405e7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e4c93a10a6b945c5ae9893397b7615f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_60b965546cde482a85071307850e94a3", "max": 4903351912, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b3f6dba1106d4441939b8a1f9e479520", "value": 4903351912}}, "e4fbdc5e183f46308ccc88b62e6c3b43": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2d5272e42bcf4d9d92f1f820fa736237", "placeholder": "​", "style": "IPY_MODEL_06f675a549d7475bb1bb53216aab8013", "value": "tokenizer.json: 100%"}}, "e5624cfc65d84f1cb193080dc1919758": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e5e429fb10e14e31b8c66e3e77867eac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_28f2c9a25e664f43838e30ade2a105f5", "max": 4962221464, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7142f90e97f94ad3bdc1ea6c4d9c5f5c", "value": 4962221464}}, "e6e26fc7717748de912443c7d1ca0e88": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_493e2c272eff462e9c2296f6c089fc8e", "placeholder": "​", "style": "IPY_MODEL_c2a1b3c85ca04349b4cbf1e9fdd7475c", "value": " 4/4 [01:22&lt;00:00, 19.86s/it]"}}, "e8693a705cdf46eaa4cb5143159108be": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e877369337da47fc9750cb360c41b640": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e97aec423d75440690458f2131563ccd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ee76445245704862a1518228dad12636": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eebee189615a46afbfe0c53c735e700f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ef38b69a2acc47d5ba47e66cb58764a8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "efcdfcf3b81c4874aaa60286015135e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a942581c47c0496e803991509fa7981b", "placeholder": "​", "style": "IPY_MODEL_91557dbe9fff427291a3a26ce4737692", "value": "tokenizer.model: 100%"}}, "f067e230df4b4fb4b43f37283308f24a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2a20261c36974cdc97cd9a3ca3816e66", "IPY_MODEL_9905e733da824c578c19c6d4836c2e61", "IPY_MODEL_********************************"], "layout": "IPY_MODEL_5086d1964ca44d0090a0abb3d683a79d"}}, "f4b61e9d1262499bab3d04bd1654d201": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f59c6b89fe3443c4bba8457320692212": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f6b50a861e8e4f47b742e2a19d73e11f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_108ad072a2744c2ca3061c8584bce0d0", "placeholder": "​", "style": "IPY_MODEL_a2ce7b63b1884629839bb92bfef023b9", "value": " 3.67G/3.67G [00:16&lt;00:00, 209MB/s]"}}, "f9be4a1aa4384f8dafbd7270ee688f58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fa60f0b2621f49efa9aa67909e0b10cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c133bac838ca47fa99a9de8f5c48ae41", "placeholder": "​", "style": "IPY_MODEL_907f122633ba4d4fa29a2a30c1109c2e", "value": "1_Pooling/config.json: 100%"}}, "fa813647eeb94bfeaf3e2508f8f61cdc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_531dfe254eff4d9485b7fab2dfeacf6e", "placeholder": "​", "style": "IPY_MODEL_01bf1fca1c0f4189b3fa1e9b87c41ba0", "value": " 342/342 [00:00&lt;00:00, 24.4kB/s]"}}, "fb5b27b07d474f1b87dcbe7ceb3062b7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1406c7c872be40bb986f610fa9df08a0", "IPY_MODEL_a7ad64b18ac74515b7e87526394c8462", "IPY_MODEL_54cb9b09b1ff4fd2b03941ef9764d736"], "layout": "IPY_MODEL_952a641b6f0a4847a64794227a6bae18"}}, "fb6ff431c60e4e39bd397df94761747a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3b7687fa9307414db22cb81c7aa7ae3e", "max": 4, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_96e5d431d8804f11b8d96c67865bc4a6", "value": 4}}, "fee5fffb9ebf471d9ecb912c3fd26c91": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fefbfbce7e0f46baad2c4413414868fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b4bb9facf9644a1aa03d5886215e69fb", "IPY_MODEL_6f45b925a0dc487389724a1d36768cc2", "IPY_MODEL_8b02b770dd654d609d0483e2d06006a9"], "layout": "IPY_MODEL_7d1255955d7f4d5d822d167aec268095"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}