{"cells": [{"attachments": {"image-2.png": {"image/png": "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"}, "image.png": {"image/png": "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*****************************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"}}, "cell_type": "markdown", "metadata": {"id": "ELUZeeM6BshL"}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_mongodb_llama3_huggingface_open_source.ipynb) \n", "\n", "# Implementing RAG pipelines with MongoDB and Llama3 and Open models From Hugging Face\n", "\n", "This notebook is designed to demonstrate how to integrate and utilize Hugging Face's open-source models, specifically Llama3, with MongoDB to implement Retrieval-Augmented Generation (RAG) pipelines for enhanced question answering capabilities.\n", "\n", "The process involves preparing a dataset of arXiv papers, transforming their data for effective retrieval, setting up a MongoDB database with vector search capabilities, and using llama3 model for generating answers based on the retrieved documents.\n", "\n", "Key Highlights:\n", "- Usage of Hugging Face open-source models and MongoDB for creating RAG pipelines.\n", "- Steps include dataset preparation, database setup, data ingestion, and query processing.\n", "- Detailed guidance on setting up MongoDB collections and vector search indexes.\n", "- Integration with the Llama3 model from Hugging Face for answering complex queries.\n", "\n", "Follow the following instruction to set up a MongoDB database and enable vector search:\n", "1. [Register a free Atlas account](https://account.mongodb.com/account/register?utm_campaign=devrel&utm_source=community&utm_medium=cta&utm_content=GitHub%20Cookbook&utm_term=richmond.alake)\n", " or sign in to your existing Atlas account.\n", "\n", "2. [Follow the instructions](https://www.mongodb.com/docs/atlas/tutorial/deploy-free-tier-cluster/)\n", " (select Atlas UI as the procedure) to deploy your first cluster, which distributes your data across multiple servers for improved performance and redundancy.\n", " \n", " ![image.png](attachment:image.png)\n", "\n", "3. For a free Cluser, be sure to select \"Shared\" option when creating your new cluster. See image below for details\n", "\n", "![image-2.png](attachment:image-2.png)\n", "\n", "4. Create the database: `knowledge_base`, and collection `research_papers`\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "BPgc75ATDIvA"}, "source": ["## Import Libraries\n", "\n", "Import libaries into development environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UUlChXcYO43e"}, "outputs": [], "source": ["!pip install datasets pandas pymongo sentence_transformers\n", "!pip install -U transformers\n", "# Install the library below if using GPU, if using CPU, please comment out below\n", "!pip install accelerate"]}, {"cell_type": "markdown", "metadata": {"id": "JZHLRiGvDSgB"}, "source": ["## Dataset Loading and Preparation\n", "\n", "Load the dataset from HuggingFace.\n", "\n", "Only using the first 100 datapoint for demo purposes."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "uRvntN3jO8j1"}, "outputs": [], "source": ["# Load Dataset\n", "import os\n", "\n", "import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# Make sure you have an Hugging Face token(HF_TOKEN) in your development environemnt before running the code below\n", "# How to get a token: https://huggingface.co/docs/hub/en/security-tokens\n", "# Dataset Location: https://huggingface.co/datasets/MongoDB/subset_arxiv_papers_with_embeddings\n", "os.environ[\"HF_TOKEN\"] = (\n", "    \"place_hugging_face_access_token here\"  # Do not use this in production environment, use a .env file instead\n", ")\n", "\n", "dataset = load_dataset(\"MongoDB/subset_arxiv_papers_with_embeddings\")\n", "\n", "# Convert the dataset to a pandas dataframe\n", "dataset_df = pd.DataFrame(dataset[\"train\"])\n", "\n", "dataset_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LUsc7qu6O_Nz"}, "outputs": [], "source": ["# Data Preparation\n", "\n", "# Only use the first 100 for demo and POC purposes\n", "dataset_df = dataset_df.head(100)\n", "\n", "# Remove the embedding from each data point in the dataset as we are going to create new embeddings with an open source embedding model from Hugging Face\n", "dataset_df = dataset_df.drop(columns=[\"embedding\"])\n", "dataset_df.head(5)"]}, {"cell_type": "markdown", "metadata": {"id": "sX3xd3ijDvNJ"}, "source": ["## Generate Embeddings"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 677}, "id": "PgGL45kgPB9r", "outputId": "67c74bdc-b731-44bb-adf7-077cf84f6dcc"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 100,\n  \"fields\": [\n    {\n      \"column\": \"id\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0029011491975882153,\n        \"min\": 704.0001,\n        \"max\": 704.01,\n        \"num_unique_values\": 100,\n        \"samples\": [\n          704.0084,\n          704.0054,\n          704.0071\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"submitter\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 93,\n        \"samples\": [\n          \"<PERSON><PERSON> <PERSON>\",\n          \"<PERSON>\",\n          \"Tom\\\\'a\\\\v{s} <PERSON><PERSON>\\\\v{r}\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"authors\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 96,\n        \"samples\": [\n          \"Partha Mukhopadhyay\",\n          \"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\",\n          \"<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 100,\n        \"samples\": [\n          \"Formation of density singularities in ideal hydrodynamics of freely\\n  cooling inelastic gases: a family of exact solutions\",\n          \"The Hardy-Lorentz Spaces $H^{p,q}(R^n)$\",\n          \"Pairwise comparisons of typological profiles (of languages)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"comments\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 83,\n        \"samples\": [\n          \"Resubmit with new results on the upper bound of the number of steady\\n  states. 20 pages, 2 figures, See\\n  http://www.math.rutgers.edu/~sontag/PUBDIR/index.html for online preprints\\n  and reprints of related work\",\n          \"37 pages, 15 figures; published version\",\n          \"4 pages, 2 figures; mistakes due to an erroneous electron-phonon\\n  coupling constant have been corrected; mode splitting is larger than\\n  originally expected\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"journal-ref\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 52,\n        \"samples\": [\n          \"J.Opt.Soc.Am.A 23(10): 2592-2601 (2006)\",\n          \"Int.J.Mod.Phys.A22:1953-1982,2007\",\n          \"Comm. Math. Helv. 84 (2009), No. 2, pp. 259--296.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"doi\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 50,\n        \"samples\": [\n          \"10.1103/PhysRevD.75.094018\",\n          \"10.1088/0264-9381/24/14/008\",\n          \"10.1103/PhysRevLett.99.126405\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"report-no\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 8,\n        \"samples\": [\n          \"IGPG-07/03-2\",\n          \"DAMTP-2007-30\",\n          \"ANL-HEP-PR-07-12\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"categories\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 66,\n        \"samples\": [\n          \"math.PR\",\n          \"math.GR math.DG\",\n          \"hep-ph\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"license\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"http://creativecommons.org/licenses/by-nc-nd/4.0/\",\n          \"http://arxiv.org/licenses/nonexclusive-distrib/1.0/\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"abstract\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 100,\n        \"samples\": [\n          \"  We employ granular hydrodynamics to investigate a paradigmatic problem of\\nclustering of particles in a freely cooling dilute granular gas. We consider\\nlarge-scale hydrodynamic motions where the viscosity and heat conduction can be\\nneglected, and one arrives at the equations of ideal gas dynamics with an\\nadditional term describing bulk energy losses due to inelastic collisions. We\\nemploy Lagrangian coordinates and derive a broad family of exact non-stationary\\nanalytical solutions that depend only on one spatial coordinate. These\\nsolutions exhibit a new type of singularity, where the gas density blows up in\\na finite time when starting from smooth initial conditions. The density blowups\\nsignal formation of close-packed clusters of particles. As the density blow-up\\ntime $t_c$ is approached, the maximum density exhibits a power law $\\\\sim\\n(t_c-t)^{-2}$. The velocity gradient blows up as $\\\\sim - (t_c-t)^{-1}$ while\\nthe velocity itself remains continuous and develops a cusp (rather than a shock\\ndiscontinuity) at the singularity. The gas temperature vanishes at the\\nsingularity, and the singularity follows the isobaric scenario: the gas\\npressure remains finite and approximately uniform in space and constant in time\\nclose to the singularity. An additional exact solution shows that the density\\nblowup, of the same type, may coexist with an \\\"ordinary\\\" shock, at which the\\nhydrodynamic fields are discontinuous but finite. We confirm stability of the\\nexact solutions with respect to small one-dimensional perturbations by solving\\nthe ideal hydrodynamic equations numerically. Furthermore, numerical solutions\\nshow that the local features of the density blowup hold universally,\\nindependently of details of the initial and boundary conditions.\\n\",\n          \"  In this paper we consider the Hardy-Lorentz spaces $H^{p,q}(R^n)$, with\\n$0<p\\\\le 1$, $0<q\\\\le \\\\infty$. We discuss the atomic decomposition of the\\nelements in these spaces, their interpolation properties, and the behavior of\\nsingular integrals and other operators acting on them.\\n\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"versions\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"update_date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2007-05-23 00:00:00\",\n        \"max\": \"2022-03-31 00:00:00\",\n        \"num_unique_values\": 47,\n        \"samples\": [\n          \"2008-09-05 00:00:00\",\n          \"2008-01-19 00:00:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"authors_parsed\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-2d3a27db-c5ee-46da-afce-99284beacd5d\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>submitter</th>\n", "      <th>authors</th>\n", "      <th>title</th>\n", "      <th>comments</th>\n", "      <th>journal-ref</th>\n", "      <th>doi</th>\n", "      <th>report-no</th>\n", "      <th>categories</th>\n", "      <th>license</th>\n", "      <th>abstract</th>\n", "      <th>versions</th>\n", "      <th>update_date</th>\n", "      <th>authors_parsed</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>704.0001</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, C.-...</td>\n", "      <td>Calculation of prompt diphoton production cros...</td>\n", "      <td>37 pages, 15 figures; published version</td>\n", "      <td>Phys.Rev.D76:013009,2007</td>\n", "      <td>10.1103/PhysRevD.76.013009</td>\n", "      <td>ANL-HEP-PR-07-12</td>\n", "      <td>hep-ph</td>\n", "      <td>None</td>\n", "      <td>A fully differential calculation in perturba...</td>\n", "      <td>[{'version': 'v1', 'created': 'Mon, 2 Apr 2007...</td>\n", "      <td>2008-11-26</td>\n", "      <td>[[<PERSON><PERSON><PERSON><PERSON><PERSON>, C., ], [<PERSON>, E. L., ], [<PERSON><PERSON><PERSON><PERSON>,...</td>\n", "      <td>[-0.0073745595291256905, -0.03725249320268631,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>704.0002</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> and <PERSON></td>\n", "      <td>Sparsity-certifying Graph Decompositions</td>\n", "      <td>To appear in Graphs and Combinatorics</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>math.CO cs.CG</td>\n", "      <td>http://arxiv.org/licenses/nonexclusive-distrib...</td>\n", "      <td>We describe a new algorithm, the $(k,\\ell)$-...</td>\n", "      <td>[{'version': 'v1', 'created': 'Sat, 31 Mar 200...</td>\n", "      <td>2008-12-13</td>\n", "      <td>[[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ], [<PERSON><PERSON>, <PERSON>, ]]</td>\n", "      <td>[0.005753430537879467, 0.007056022062897682, 0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>704.0003</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>The evolution of the Earth-Moon system based o...</td>\n", "      <td>23 pages, 3 figures</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>physics.gen-ph</td>\n", "      <td>None</td>\n", "      <td>The evolution of Earth-Moon system is descri...</td>\n", "      <td>[{'version': 'v1', 'created': 'Sun, 1 Apr 2007...</td>\n", "      <td>2008-01-13</td>\n", "      <td>[[<PERSON>, <PERSON>, ]]</td>\n", "      <td>[-0.0057186526246368885, 0.022108040750026703,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>704.0004</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>A determinant of Stirling cycle numbers counts...</td>\n", "      <td>11 pages</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>math.CO</td>\n", "      <td>None</td>\n", "      <td>We show that a determinant of Stirling cycle...</td>\n", "      <td>[{'version': 'v1', 'created': 'Sat, 31 Mar 200...</td>\n", "      <td>2007-05-23</td>\n", "      <td>[[<PERSON><PERSON>, <PERSON>, ]]</td>\n", "      <td>[-0.02010205015540123, -0.0021757606882601976,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>704.0005</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON> and <PERSON></td>\n", "      <td>From dyadic $\\Lambda_{\\alpha}$ to $\\Lambda_{\\a...</td>\n", "      <td>None</td>\n", "      <td>Illinois J. Math. 52 (2008) no.2, 681-689</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>math.CA math.FA</td>\n", "      <td>None</td>\n", "      <td>In this paper we show how to compute the $\\L...</td>\n", "      <td>[{'version': 'v1', 'created': 'Mon, 2 Apr 2007...</td>\n", "      <td>2013-10-15</td>\n", "      <td>[[<PERSON><PERSON>, <PERSON><PERSON>, ], [<PERSON><PERSON><PERSON>, <PERSON>, ]]</td>\n", "      <td>[-0.0027832775376737118, 0.014300416223704815,...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-2d3a27db-c5ee-46da-afce-99284beacd5d')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-2d3a27db-c5ee-46da-afce-99284beacd5d button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-2d3a27db-c5ee-46da-afce-99284beacd5d');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-3a97a838-2b0e-445a-af3a-e782e6330ba2\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-3a97a838-2b0e-445a-af3a-e782e6330ba2')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-3a97a838-2b0e-445a-af3a-e782e6330ba2 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["         id           submitter  \\\n", "0  704.0001      <PERSON>   \n", "1  704.0002        <PERSON>   \n", "2  704.0003         <PERSON><PERSON> Pan   \n", "3  704.0004        <PERSON>   \n", "4  704.0005  <PERSON>   \n", "\n", "                                             authors  \\\n", "0  <PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, C.-...   \n", "1                    <PERSON><PERSON><PERSON> and <PERSON>   \n", "2                                        Hongjun Pan   \n", "3                                       <PERSON>   \n", "4           <PERSON><PERSON> and <PERSON>   \n", "\n", "                                               title  \\\n", "0  Calculation of prompt diphoton production cros...   \n", "1           Sparsity-certifying Graph Decompositions   \n", "2  The evolution of the Earth-Moon system based o...   \n", "3  A determinant of Stirling cycle numbers counts...   \n", "4  From dyadic $\\Lambda_{\\alpha}$ to $\\Lambda_{\\a...   \n", "\n", "                                  comments  \\\n", "0  37 pages, 15 figures; published version   \n", "1    To appear in Graphs and Combinatorics   \n", "2                      23 pages, 3 figures   \n", "3                                 11 pages   \n", "4                                     None   \n", "\n", "                                 journal-ref                         doi  \\\n", "0                   Phys.Rev.D76:013009,2007  10.1103/PhysRevD.76.013009   \n", "1                                       None                        None   \n", "2                                       None                        None   \n", "3                                       None                        None   \n", "4  Illinois J. <PERSON>. 52 (2008) no.2, 681-689                        None   \n", "\n", "          report-no       categories  \\\n", "0  ANL-HEP-PR-07-12           hep-ph   \n", "1              None    math.CO cs.CG   \n", "2              None   physics.gen-ph   \n", "3              None          math.CO   \n", "4              None  math.CA math.FA   \n", "\n", "                                             license  \\\n", "0                                               None   \n", "1  http://arxiv.org/licenses/nonexclusive-distrib...   \n", "2                                               None   \n", "3                                               None   \n", "4                                               None   \n", "\n", "                                            abstract  \\\n", "0    A fully differential calculation in perturba...   \n", "1    We describe a new algorithm, the $(k,\\ell)$-...   \n", "2    The evolution of Earth-Moon system is descri...   \n", "3    We show that a determinant of Stirling cycle...   \n", "4    In this paper we show how to compute the $\\L...   \n", "\n", "                                            versions update_date  \\\n", "0  [{'version': 'v1', 'created': 'Mon, 2 Apr 2007...  2008-11-26   \n", "1  [{'version': 'v1', 'created': '<PERSON><PERSON>, 31 Mar 200...  2008-12-13   \n", "2  [{'version': 'v1', 'created': 'Sun, 1 Apr 2007...  2008-01-13   \n", "3  [{'version': 'v1', 'created': '<PERSON><PERSON>, 31 Mar 200...  2007-05-23   \n", "4  [{'version': 'v1', 'created': 'Mon, 2 Apr 2007...  2013-10-15   \n", "\n", "                                      authors_parsed  \\\n", "0  [[<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ], [<PERSON>, E. <PERSON>, ], [<PERSON><PERSON><PERSON><PERSON>,...   \n", "1           [[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ], [<PERSON><PERSON>, <PERSON>, ]]   \n", "2                                 [[<PERSON>, <PERSON><PERSON>, ]]   \n", "3                                [[<PERSON><PERSON>, <PERSON>, ]]   \n", "4  [[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ], [<PERSON><PERSON><PERSON>, <PERSON>, ]]   \n", "\n", "                                           embedding  \n", "0  [-0.0073745595291256905, -0.03725249320268631,...  \n", "1  [0.005753430537879467, 0.007056022062897682, 0...  \n", "2  [-0.0057186526246368885, 0.022108040750026703,...  \n", "3  [-0.02010205015540123, -0.0021757606882601976,...  \n", "4  [-0.0027832775376737118, 0.014300416223704815,...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from sentence_transformers import SentenceTransformer\n", "\n", "# https://huggingface.co/thenlper/gte-large\n", "embedding_model = SentenceTransformer(\"thenlper/gte-large\")\n", "\n", "\n", "def get_embedding(text: str) -> list[float]:\n", "    if not text.strip():\n", "        print(\"Attempted to get embedding for empty text.\")\n", "        return []\n", "\n", "    embedding = embedding_model.encode(text)\n", "\n", "    return embedding.tolist()\n", "\n", "\n", "dataset_df[\"embedding\"] = dataset_df.apply(\n", "    lambda x: get_embedding(x[\"title\"] + \" \" + x[\"authors\"] + \" \" + x[\"abstract\"]),\n", "    axis=1,\n", ")\n", "\n", "dataset_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "XAkrmDaF_9kN"}, "source": ["## Database and Collection Setup\n", "\n", "Complete the steps below if not already carried out previously.\n", "Creating a database and collection within MongoDB is made simple with MongoDB Atlas.\n", "\n", "1. [Register a free Atlas account](https://account.mongodb.com/account/register?utm_campaign=devrel&utm_source=community&utm_medium=cta&utm_content=GitHub%20Cookbook&utm_term=richmond.alake)\n", " or sign in to your existing Atlas account.\n", "\n", "2. [Follow the instructions](https://www.mongodb.com/docs/atlas/tutorial/deploy-free-tier-cluster/)\n", " (select Atlas UI as the procedure)  to deploy your first cluster.\n", "\n", "3. Create the database: `knowledge_base`.\n", "4. Within the database` knowledge_base`, create the following collections: `research_papers`\n", "5. Create a\n", "[vector search index](https://www.mongodb.com/docs/atlas/atlas-vector-search/create-index/#procedure)\n", " named `vector_index` for the `research_papers` collection. This index enables the RAG application to retrieve records as additional context to supplement user queries via vector search. Below is the JSON definition of the data collection vector search index.\n", "\n", "\n", " Below is a snipper of what the vector search index definition should looks like:\n", " ```\n", "    {\n", "      \"fields\": [\n", "        {\n", "          \"numDimensions\": 1024,\n", "          \"path\": \"embedding\",\n", "          \"similarity\": \"cosine\",\n", "          \"type\": \"vector\"\n", "        }\n", "      ]\n", "    }\n", "  ```"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "F-YbBIraPFgb", "outputId": "37a58536-ea57-49cf-b861-8560cb506a33"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["import pymongo\n", "\n", "\n", "def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish connection to the MongoDB.\"\"\"\n", "    try:\n", "        client = pymongo.MongoClient(\n", "            mongo_uri, appname=\"devrel.showcase.rag_llama3_huggingface\"\n", "        )\n", "        print(\"Connection to MongoDB successful\")\n", "        return client\n", "    except pymongo.errors.ConnectionFailure as e:\n", "        print(f\"Connection failed: {e}\")\n", "        return None\n", "\n", "\n", "# Ensure connection strings are placed securely in environment variables and not disclosed in production environments.\n", "mongo_uri = \"mongodb...pName=Cluster0\"  # Placeholder, replace with your connection string or actual environment variable fetching method.\n", "\n", "if not mongo_uri:\n", "    print(\"MONGO_URI not set in environment variables.\")\n", "\n", "mongo_client = get_mongo_client(mongo_uri)\n", "\n", "# Ingest data into MongoDB\n", "db = mongo_client[\"knowledge_base\"]\n", "collection = db[\"research_papers\"]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "I0DP8D6qPGyJ", "outputId": "769249a6-f30e-4b62-a677-021c3ac04e70"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 100, 'electionId': ObjectId('7fffffff000000000000001f'), 'opTime': {'ts': Timestamp(1713636955, 100), 't': 31}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1713636955, 100), 'signature': {'hash': b'J\\x17\\x95:(\\x9f\\xb4\\x96\\xcdv:\"\\xbc\\x0c)\\x98\\xd3\\tq\\x89', 'keyId': 7320226449804230662}}, 'operationTime': Timestamp(1713636955, 100)}, acknowledged=True)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Delete any existing records in the collection\n", "collection.delete_many({})"]}, {"cell_type": "markdown", "metadata": {"id": "g3jt3YfPD2GY"}, "source": ["## Data Ingestion"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5o6SOG7dPHzD", "outputId": "22fbeac4-2532-47de-94ca-70ec300da5c3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data ingestion into MongoDB completed\n"]}], "source": ["documents = dataset_df.to_dict(\"records\")\n", "collection.insert_many(documents)\n", "\n", "print(\"Data ingestion into MongoDB completed\")"]}, {"cell_type": "markdown", "metadata": {"id": "ZyBMu6NFD4sD"}, "source": ["## Vector Search"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "PRC31ofFPKiV"}, "outputs": [], "source": ["def vector_search(user_query, collection):\n", "    \"\"\"\n", "    Perform a vector search in the MongoDB collection based on the user query.\n", "\n", "    Args:\n", "    user_query (str): The user's query string.\n", "    collection (MongoCollection): The MongoDB collection to search.\n", "\n", "    Returns:\n", "    list: A list of matching documents.\n", "    \"\"\"\n", "\n", "    # Generate embedding for the user query\n", "    query_embedding = get_embedding(user_query)\n", "\n", "    if query_embedding is None:\n", "        return \"Invalid query or embedding generation failed.\"\n", "\n", "    # Define the vector search pipeline\n", "    vector_search_stage = {\n", "        \"$vectorSearch\": {\n", "            \"index\": \"vector_index\",\n", "            \"queryVector\": query_embedding,\n", "            \"path\": \"embedding\",\n", "            \"numCandidates\": 150,  # Number of candidate matches to consider\n", "            \"limit\": 4,  # Return top 4 matches\n", "        }\n", "    }\n", "\n", "    unset_stage = {\n", "        \"$unset\": \"embedding\"  # Exclude the 'embedding' field from the results\n", "    }\n", "\n", "    project_stage = {\n", "        \"$project\": {\n", "            \"_id\": 0,  # Exclude the _id field\n", "            \"fullplot\": 1,  # Include the plot field\n", "            \"title\": 1,  # Include the title field\n", "            \"genres\": 1,  # Include the genres field\n", "            \"score\": {\"$meta\": \"vectorSearchScore\"},  # Include the search score\n", "        }\n", "    }\n", "\n", "    pipeline = [vector_search_stage, unset_stage, project_stage]\n", "\n", "    # Execute the search\n", "    results = collection.aggregate(pipeline)\n", "    return list(results)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "9MLEt5JtPLt6"}, "outputs": [], "source": ["def get_search_result(query, collection):\n", "    get_knowledge = vector_search(query, collection)\n", "\n", "    search_result = \"\"\n", "    for result in get_knowledge:\n", "        search_result += f\"Title: {result.get('title', 'N/A')}, Plot: {result.get('abstract', 'N/A')}\\n\"\n", "\n", "    return search_result"]}, {"cell_type": "markdown", "metadata": {"id": "P3U3nZDOD-Dl"}, "source": ["## Handling User Queries"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jkswY16gPNLJ", "outputId": "e4f98b1d-d5b0-4497-b019-fe22e6336c5e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'system', 'content': 'You are a research assitant!'}, {'role': 'user', 'content': 'Query: Get me papers on Artificial Intelligence?\\nContinue to answer the query by using the Search Results:\\n.'}]\n"]}], "source": ["# Conduct query with retrival of sources\n", "query = \"Get me papers on Artificial Intelligence?\"\n", "source_information = get_search_result(query, collection)\n", "combined_information = f\"Query: {query}\\nContinue to answer the query by using the Search Results:\\n{source_information}.\"\n", "messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a research assitant!\"},\n", "    {\"role\": \"user\", \"content\": combined_information},\n", "]\n", "print(messages)"]}, {"cell_type": "markdown", "metadata": {"id": "PDfpkvNNEBXs"}, "source": ["## Loading and Using Llama3"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 66, "referenced_widgets": ["f469e6ca90984864b1c841049a39e154", "ece3866a2f3242d696a374710dcdaa53", "28c797fe529b432787f13f4853b4ee58", "7fec4cd68fc94cd4b3a79e001fb967ae", "bbc2c01aee5442999cc82d703029e7d1", "078cc39fd28443659b0ec2339b749296", "0818fd8f9e1e4abeaf8c169b022c20f8", "0be57a9aa4124e4fbe2ac57baf878ad9", "957f547f721b4b739fa89c5a4518a1be", "c6ac614925f346b286ec1f6e48468d86", "0804231321fb4c60973f91880116e6a9"]}, "id": "7hKfOmElPOJF", "outputId": "c8186561-d3b4-41c2-98e8-da0af9133e9c"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f469e6ca90984864b1c841049a39e154", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Meta-Llama-3-8B-Instruct\")\n", "# CPU Enabled uncomment below 👇🏽\n", "# model = AutoModelForCausalLM.from_pretrained(\"meta-llama/Meta-Llama-3-8B-Instruct\")\n", "# GPU Enabled use below 👇🏽\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    \"meta-llama/Meta-Llama-3-8B-Instruct\", torch_dtype=torch.bfloat16, device_map=\"auto\"\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Foy-fO58-69G", "outputId": "c24b8009-7490-4b05-fb51-5d4575d84001"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The attention mask and the pad token id were not set. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n", "Setting `pad_token_id` to `eos_token_id`:128001 for open-end generation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["I'd be happy to help you with that. Here are some research papers on Artificial Intelligence that I found using a search engine:\n", "\n", "**1. \"Deep Learning\" by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (2015)**\n", "\n", "This paper is a seminal work on deep learning, a subset of AI that involves training neural networks to perform tasks such as image recognition, speech recognition, and natural language processing.\n", "\n", "Source: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2015). Deep learning. Nature, 521(7553), 436-444. doi: 10.1038/nature14539\n", "\n", "**2. \"AlphaGo: Mastering the Game of Go with Deep Neural Networks and Tree Search\" by <PERSON><PERSON>, <PERSON>, and <PERSON> (2015)**\n", "\n", "This paper describes the development of AlphaGo, a computer program that uses AI to play the game of Go. AlphaGo was able to defeat a human world champion in a five-game match, marking a significant milestone in AI research.\n", "\n", "Source: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, J. (2015). AlphaGo: Mastering the\n"]}], "source": ["input_ids = tokenizer.apply_chat_template(\n", "    messages, add_generation_prompt=True, return_tensors=\"pt\"\n", ").to(model.device)\n", "\n", "terminators = [tokenizer.eos_token_id, tokenizer.convert_tokens_to_ids(\"<|eot_id|>\")]\n", "\n", "outputs = model.generate(\n", "    input_ids,\n", "    max_new_tokens=256,\n", "    eos_token_id=terminators,\n", "    do_sample=True,\n", "    temperature=0.6,\n", "    top_p=0.9,\n", ")\n", "response = outputs[0][input_ids.shape[-1] :]\n", "print(tokenizer.decode(response, skip_special_tokens=True))"]}], "metadata": {"colab": {"machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"078cc39fd28443659b0ec2339b749296": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0804231321fb4c60973f91880116e6a9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0818fd8f9e1e4abeaf8c169b022c20f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0be57a9aa4124e4fbe2ac57baf878ad9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "28c797fe529b432787f13f4853b4ee58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0be57a9aa4124e4fbe2ac57baf878ad9", "max": 4, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_957f547f721b4b739fa89c5a4518a1be", "value": 4}}, "7fec4cd68fc94cd4b3a79e001fb967ae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6ac614925f346b286ec1f6e48468d86", "placeholder": "​", "style": "IPY_MODEL_0804231321fb4c60973f91880116e6a9", "value": " 4/4 [01:05&lt;00:00, 14.00s/it]"}}, "957f547f721b4b739fa89c5a4518a1be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bbc2c01aee5442999cc82d703029e7d1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6ac614925f346b286ec1f6e48468d86": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ece3866a2f3242d696a374710dcdaa53": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_078cc39fd28443659b0ec2339b749296", "placeholder": "​", "style": "IPY_MODEL_0818fd8f9e1e4abeaf8c169b022c20f8", "value": "Loading checkpoint shards: 100%"}}, "f469e6ca90984864b1c841049a39e154": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ece3866a2f3242d696a374710dcdaa53", "IPY_MODEL_28c797fe529b432787f13f4853b4ee58", "IPY_MODEL_7fec4cd68fc94cd4b3a79e001fb967ae"], "layout": "IPY_MODEL_bbc2c01aee5442999cc82d703029e7d1"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}