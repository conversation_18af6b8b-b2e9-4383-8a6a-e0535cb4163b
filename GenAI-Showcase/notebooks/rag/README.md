Jupyter Notebooks demonstrating various Retrieval Augmented Generation (RAG) implementations using LLM providers, frameworks and MongoDB Atlas Vector Search.

| Title | Stack | Notebook |
|-------|-------|----------|
| Self-Querying with LangGraph | MongoDB Atlas, LangGraph, Unstructured | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/self_querying_mongodb_unstructured_langgraph.ipynb) |
| RAG with Gemma | MongoDB Atlas, Hugging Face, Gemma | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_with_hugging_face_gemma_mongodb.ipynb) |
| RAG with Llama 3 | MongoDB Atlas, Hugging Face, Llama 3 | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_mongodb_llama3_huggingface_open_source.ipynb) |
| RAG Chunking Strategies | MongoDB Atlas, LangChain | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_chunking_strategies.ipynb) |
| OpenAI Text Embeddings | MongoDB Atlas, OpenAI | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/openai_text_3_emebdding.ipynb) |
| Naive RAG Implementation | MongoDB Atlas, LlamaIndex | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/naive_rag_implemenation_llamaindex.ipynb) |
| LangChain.js Memory | MongoDB Atlas, LangChain.js | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/mongodb-langchain-js-memory.ipynb) |
| DeepSeek RAG Pipeline | MongoDB Atlas, DeepSeek | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/deepseek_r1_rag_pipeline_with_mongodb.ipynb) |
| LangChain Cache Memory | MongoDB Atlas, LangChain | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/mongodb-langchain-cache-memory.ipynb) |
| RAG with LlamaIndex | MongoDB Atlas, LlamaIndex | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/building_RAG_with_LlamaIndex_and_MongoDB_Vector_Database.ipynb) |
| Swig Menu Scraping | MongoDB Atlas, Playwright, OpenAI | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/SwigMenu_Playwright_OpenAI_MongoDB.ipynb) |
| RAG with Gemma 2 | MongoDB Atlas, Gemma 2 | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_with_gemma2_mongodb_open_models.ipynb) |
| RAG with Gemma 2 2B | MongoDB Atlas, Gemma 2 2B | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_with_gemma2_2b_mongodb_open_models.ipynb) |
| Keras NLP RAG Pipeline | MongoDB Atlas, Keras NLP, Gemma 2 | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_pipeline_kerasnlp_mongodb_gemma2.ipynb) |
| RAG Chatbot with Cohere | MongoDB Atlas, Cohere | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/rag_chatbot_with_cohere_and_mongodb.ipynb) |
| Haystack Cooking Advisor | MongoDB Atlas, Haystack | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/haystack_mongodb_cooking_advisor_pipeline.ipynb) |
| Graph RAG | MongoDB Atlas, OpenAI | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/graphrag_with_mongodb_and_openai.ipynb) |
| Chat with PDF | MongoDB Atlas, OpenAI, LangChain | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/chat_with_pdf_mongodb_openai_langchain_POLM_AI_Stack.ipynb) |
| Anthropic PAM AI Stack | MongoDB Atlas, Anthropic | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/anthropic_mongodb_pam_ai_stack.ipynb) |
| Trader Joe's Party Planner | MongoDB Atlas, LlamaIndex, Playwright | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/TraderJoesFallAIPartyPlanner_PlaywrightLlamaIndexVectorSearch.ipynb) |
| Haystack RAG | MongoDB Atlas, Haystack | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/Haystack_MongoDB_Atlas_RAG.ipynb) |
