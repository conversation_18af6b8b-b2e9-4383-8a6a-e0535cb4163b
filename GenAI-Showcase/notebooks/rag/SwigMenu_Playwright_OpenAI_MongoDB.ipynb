{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "view-in-github"}, "source": ["<a href=\"https://colab.research.google.com/github/anaiyaraisin/GenAI-Showcase/blob/main/notebooks/rag/SwigMenu_Playwright_OpenAI_MongoDB.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "eAC5_aMyRILN"}, "source": ["[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/playwright-structured-outputs-atlas-search/)"]}, {"cell_type": "markdown", "metadata": {"id": "k4ID8L8NApVj"}, "source": ["## Overview\n", "\n", "In this tutorial we are going to scrape the popular Utah \"dirty\" soda website, <PERSON><PERSON>, using <PERSON>wright, then we are going to feed in our drinks into OpenAI using a prompt and their structured outputs to understand which drinks from their menu are best for various seasons with reasonings, and then save this information into MongoDB Atlas so we can use Atlas Search to find specific drinks based on the fall season and ingredients we are craving."]}, {"cell_type": "markdown", "metadata": {"id": "CiDSwz9Tt0j0"}, "source": ["## Part 1: Scrape all menu items from Swig website\n", "\n", "Let's first scrape all our menu items from the Swig website. We need to install <PERSON><PERSON> and then build out our function."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "nUmfDDa6on69", "outputId": "476984f8-4f19-4ed4-9d0d-dc885fb2d8bf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting playwright\n", "  Downloading playwright-1.47.0-py3-none-manylinux1_x86_64.whl.metadata (3.5 kB)\n", "Collecting greenlet==3.0.3 (from playwright)\n", "  Downloading greenlet-3.0.3-cp310-cp310-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl.metadata (3.8 kB)\n", "Collecting pyee==12.0.0 (from playwright)\n", "  Downloading pyee-12.0.0-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from pyee==12.0.0->playwright) (4.12.2)\n", "Downloading playwright-1.47.0-py3-none-manylinux1_x86_64.whl (38.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m38.1/38.1 MB\u001b[0m \u001b[31m22.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading greenlet-3.0.3-cp310-cp310-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl (616 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m616.0/616.0 kB\u001b[0m \u001b[31m26.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyee-12.0.0-py3-none-any.whl (14 kB)\n", "Installing collected packages: pyee, greenlet, playwright\n", "  Attempting uninstall: greenlet\n", "    Found existing installation: greenlet 3.1.1\n", "    Uninstalling greenlet-3.1.1:\n", "      Successfully uninstalled greenlet-3.1.1\n", "Successfully installed greenlet-3.0.3 playwright-1.47.0 pyee-12.0.0\n", "Downloading Chromium 129.0.6668.29 (playwright build v1134)\u001b[2m from https://playwright.azureedge.net/builds/chromium/1134/chromium-linux.zip\u001b[22m\n", "\u001b[1G164 MiB [] 0% 0.0s\u001b[0K\u001b[1G164 MiB [] 0% 76.6s\u001b[0K\u001b[1G164 MiB [] 0% 69.2s\u001b[0K\u001b[1G164 MiB [] 0% 43.4s\u001b[0K\u001b[1G164 MiB [] 0% 44.2s\u001b[0K\u001b[1G164 MiB [] 0% 58.3s\u001b[0K\u001b[1G164 MiB [] 0% 63.7s\u001b[0K\u001b[1G164 MiB [] 0% 77.6s\u001b[0K\u001b[1G164 MiB [] 0% 75.8s\u001b[0K\u001b[1G164 MiB [] 0% 76.1s\u001b[0K\u001b[1G164 MiB [] 0% 76.6s\u001b[0K\u001b[1G164 MiB [] 0% 80.0s\u001b[0K\u001b[1G164 MiB [] 0% 87.5s\u001b[0K\u001b[1G164 MiB [] 0% 101.8s\u001b[0K\u001b[1G164 MiB [] 0% 106.0s\u001b[0K\u001b[1G164 MiB [] 0% 116.6s\u001b[0K\u001b[1G164 MiB [] 0% 121.2s\u001b[0K\u001b[1G164 MiB [] 0% 127.5s\u001b[0K\u001b[1G164 MiB [] 0% 136.0s\u001b[0K\u001b[1G164 MiB [] 0% 141.1s\u001b[0K\u001b[1G164 MiB [] 0% 141.7s\u001b[0K\u001b[1G164 MiB [] 0% 147.5s\u001b[0K\u001b[1G164 MiB [] 0% 150.8s\u001b[0K\u001b[1G164 MiB [] 0% 163.9s\u001b[0K\u001b[1G164 MiB [] 0% 168.9s\u001b[0K\u001b[1G164 MiB [] 0% 171.6s\u001b[0K\u001b[1G164 MiB [] 0% 173.7s\u001b[0K\u001b[1G164 MiB [] 0% 172.8s\u001b[0K\u001b[1G164 MiB [] 0% 173.4s\u001b[0K\u001b[1G164 MiB [] 0% 172.0s\u001b[0K\u001b[1G164 MiB [] 0% 161.9s\u001b[0K\u001b[1G164 MiB [] 0% 156.4s\u001b[0K\u001b[1G164 MiB [] 0% 150.1s\u001b[0K\u001b[1G164 MiB [] 0% 147.8s\u001b[0K\u001b[1G164 MiB [] 0% 152.1s\u001b[0K\u001b[1G164 MiB [] 0% 150.7s\u001b[0K\u001b[1G164 MiB [] 0% 149.8s\u001b[0K\u001b[1G164 MiB [] 1% 145.8s\u001b[0K\u001b[1G164 MiB [] 1% 136.8s\u001b[0K\u001b[1G164 MiB [] 1% 120.9s\u001b[0K\u001b[1G164 MiB [] 1% 111.8s\u001b[0K\u001b[1G164 MiB [] 1% 105.7s\u001b[0K\u001b[1G164 MiB [] 1% 106.4s\u001b[0K\u001b[1G164 MiB [] 1% 105.4s\u001b[0K\u001b[1G164 MiB [] 1% 104.5s\u001b[0K\u001b[1G164 MiB [] 1% 81.5s\u001b[0K\u001b[1G164 MiB [] 2% 70.5s\u001b[0K\u001b[1G164 MiB [] 2% 70.6s\u001b[0K\u001b[1G164 MiB [] 2% 71.0s\u001b[0K\u001b[1G164 MiB [] 2% 72.7s\u001b[0K\u001b[1G164 MiB [] 2% 73.0s\u001b[0K\u001b[1G164 MiB [] 2% 73.3s\u001b[0K\u001b[1G164 MiB [] 2% 73.4s\u001b[0K\u001b[1G164 MiB [] 2% 75.1s\u001b[0K\u001b[1G164 MiB [] 2% 75.6s\u001b[0K\u001b[1G164 MiB [] 2% 72.7s\u001b[0K\u001b[1G164 MiB [] 2% 63.0s\u001b[0K\u001b[1G164 MiB [] 3% 61.1s\u001b[0K\u001b[1G164 MiB [] 3% 58.0s\u001b[0K\u001b[1G164 MiB [] 3% 53.1s\u001b[0K\u001b[1G164 MiB [] 3% 50.8s\u001b[0K\u001b[1G164 MiB [] 4% 47.5s\u001b[0K\u001b[1G164 MiB [] 4% 45.3s\u001b[0K\u001b[1G164 MiB [] 4% 40.1s\u001b[0K\u001b[1G164 MiB [] 5% 35.9s\u001b[0K\u001b[1G164 MiB [] 5% 33.0s\u001b[0K\u001b[1G164 MiB [] 6% 30.6s\u001b[0K\u001b[1G164 MiB [] 6% 28.7s\u001b[0K\u001b[1G164 MiB [] 7% 27.4s\u001b[0K\u001b[1G164 MiB [] 7% 26.5s\u001b[0K\u001b[1G164 MiB [] 7% 25.2s\u001b[0K\u001b[1G164 MiB [] 8% 24.8s\u001b[0K\u001b[1G164 MiB [] 8% 23.7s\u001b[0K\u001b[1G164 MiB [] 8% 22.5s\u001b[0K\u001b[1G164 MiB [] 9% 22.0s\u001b[0K\u001b[1G164 MiB [] 9% 21.2s\u001b[0K\u001b[1G164 MiB [] 9% 21.1s\u001b[0K\u001b[1G164 MiB [] 9% 20.6s\u001b[0K\u001b[1G164 MiB [] 10% 20.3s\u001b[0K\u001b[1G164 MiB [] 10% 19.6s\u001b[0K\u001b[1G164 MiB [] 10% 18.9s\u001b[0K\u001b[1G164 MiB [] 11% 18.0s\u001b[0K\u001b[1G164 MiB [] 11% 17.9s\u001b[0K\u001b[1G164 MiB [] 11% 17.6s\u001b[0K\u001b[1G164 MiB [] 12% 17.3s\u001b[0K\u001b[1G164 MiB [] 12% 17.5s\u001b[0K\u001b[1G164 MiB [] 12% 17.2s\u001b[0K\u001b[1G164 MiB [] 13% 16.9s\u001b[0K\u001b[1G164 MiB [] 13% 16.5s\u001b[0K\u001b[1G164 MiB [] 13% 16.4s\u001b[0K\u001b[1G164 MiB [] 13% 16.0s\u001b[0K\u001b[1G164 MiB [] 14% 15.8s\u001b[0K\u001b[1G164 MiB [] 14% 15.2s\u001b[0K\u001b[1G164 MiB [] 15% 14.6s\u001b[0K\u001b[1G164 MiB [] 15% 14.4s\u001b[0K\u001b[1G164 MiB [] 15% 14.3s\u001b[0K\u001b[1G164 MiB [] 15% 14.1s\u001b[0K\u001b[1G164 MiB [] 16% 13.8s\u001b[0K\u001b[1G164 MiB [] 16% 13.5s\u001b[0K\u001b[1G164 MiB [] 17% 12.9s\u001b[0K\u001b[1G164 MiB [] 18% 12.2s\u001b[0K\u001b[1G164 MiB [] 18% 11.8s\u001b[0K\u001b[1G164 MiB [] 19% 12.0s\u001b[0K\u001b[1G164 MiB [] 19% 12.2s\u001b[0K\u001b[1G164 MiB [] 19% 12.3s\u001b[0K\u001b[1G164 MiB [] 19% 12.1s\u001b[0K\u001b[1G164 MiB [] 19% 12.0s\u001b[0K\u001b[1G164 MiB [] 20% 11.7s\u001b[0K\u001b[1G164 MiB [] 20% 11.5s\u001b[0K\u001b[1G164 MiB [] 20% 11.4s\u001b[0K\u001b[1G164 MiB [] 21% 10.9s\u001b[0K\u001b[1G164 MiB [] 22% 10.5s\u001b[0K\u001b[1G164 MiB [] 23% 10.2s\u001b[0K\u001b[1G164 MiB [] 23% 10.0s\u001b[0K\u001b[1G164 MiB [] 24% 9.7s\u001b[0K\u001b[1G164 MiB [] 24% 9.4s\u001b[0K\u001b[1G164 MiB [] 25% 9.2s\u001b[0K\u001b[1G164 MiB [] 25% 8.9s\u001b[0K\u001b[1G164 MiB [] 26% 8.8s\u001b[0K\u001b[1G164 MiB [] 27% 8.5s\u001b[0K\u001b[1G164 MiB [] 27% 8.3s\u001b[0K\u001b[1G164 MiB [] 28% 8.0s\u001b[0K\u001b[1G164 MiB [] 29% 7.8s\u001b[0K\u001b[1G164 MiB [] 30% 7.5s\u001b[0K\u001b[1G164 MiB [] 30% 7.4s\u001b[0K\u001b[1G164 MiB [] 31% 7.2s\u001b[0K\u001b[1G164 MiB [] 31% 7.0s\u001b[0K\u001b[1G164 MiB [] 32% 6.8s\u001b[0K\u001b[1G164 MiB [] 33% 6.5s\u001b[0K\u001b[1G164 MiB [] 34% 6.3s\u001b[0K\u001b[1G164 MiB [] 35% 6.2s\u001b[0K\u001b[1G164 MiB [] 35% 6.0s\u001b[0K\u001b[1G164 MiB [] 36% 5.9s\u001b[0K\u001b[1G164 MiB [] 37% 5.7s\u001b[0K\u001b[1G164 MiB [] 38% 5.6s\u001b[0K\u001b[1G164 MiB [] 38% 5.4s\u001b[0K\u001b[1G164 MiB [] 39% 5.2s\u001b[0K\u001b[1G164 MiB [] 40% 5.1s\u001b[0K\u001b[1G164 MiB [] 41% 4.9s\u001b[0K\u001b[1G164 MiB [] 42% 4.8s\u001b[0K\u001b[1G164 MiB [] 43% 4.6s\u001b[0K\u001b[1G164 MiB [] 44% 4.5s\u001b[0K\u001b[1G164 MiB [] 45% 4.3s\u001b[0K\u001b[1G164 MiB [] 46% 4.1s\u001b[0K\u001b[1G164 MiB [] 47% 4.0s\u001b[0K\u001b[1G164 MiB [] 48% 3.9s\u001b[0K\u001b[1G164 MiB [] 48% 3.8s\u001b[0K\u001b[1G164 MiB [] 49% 3.7s\u001b[0K\u001b[1G164 MiB [] 50% 3.6s\u001b[0K\u001b[1G164 MiB [] 51% 3.5s\u001b[0K\u001b[1G164 MiB [] 52% 3.4s\u001b[0K\u001b[1G164 MiB [] 52% 3.3s\u001b[0K\u001b[1G164 MiB [] 53% 3.2s\u001b[0K\u001b[1G164 MiB [] 54% 3.2s\u001b[0K\u001b[1G164 MiB [] 55% 3.1s\u001b[0K\u001b[1G164 MiB [] 55% 3.0s\u001b[0K\u001b[1G164 MiB [] 56% 2.9s\u001b[0K\u001b[1G164 MiB [] 57% 2.8s\u001b[0K\u001b[1G164 MiB [] 58% 2.7s\u001b[0K\u001b[1G164 MiB [] 59% 2.6s\u001b[0K\u001b[1G164 MiB [] 60% 2.6s\u001b[0K\u001b[1G164 MiB [] 60% 2.5s\u001b[0K\u001b[1G164 MiB [] 61% 2.5s\u001b[0K\u001b[1G164 MiB [] 61% 2.4s\u001b[0K\u001b[1G164 MiB [] 62% 2.4s\u001b[0K\u001b[1G164 MiB [] 62% 2.3s\u001b[0K\u001b[1G164 MiB [] 63% 2.3s\u001b[0K\u001b[1G164 MiB [] 64% 2.2s\u001b[0K\u001b[1G164 MiB [] 65% 2.2s\u001b[0K\u001b[1G164 MiB [] 65% 2.1s\u001b[0K\u001b[1G164 MiB [] 66% 2.0s\u001b[0K\u001b[1G164 MiB [] 67% 2.0s\u001b[0K\u001b[1G164 MiB [] 68% 1.9s\u001b[0K\u001b[1G164 MiB [] 69% 1.9s\u001b[0K\u001b[1G164 MiB [] 69% 1.8s\u001b[0K\u001b[1G164 MiB [] 70% 1.8s\u001b[0K\u001b[1G164 MiB [] 71% 1.7s\u001b[0K\u001b[1G164 MiB [] 72% 1.6s\u001b[0K\u001b[1G164 MiB [] 73% 1.6s\u001b[0K\u001b[1G164 MiB [] 73% 1.5s\u001b[0K\u001b[1G164 MiB [] 74% 1.5s\u001b[0K\u001b[1G164 MiB [] 75% 1.4s\u001b[0K\u001b[1G164 MiB [] 76% 1.4s\u001b[0K\u001b[1G164 MiB [] 77% 1.3s\u001b[0K\u001b[1G164 MiB [] 78% 1.2s\u001b[0K\u001b[1G164 MiB [] 79% 1.2s\u001b[0K\u001b[1G164 MiB [] 79% 1.1s\u001b[0K\u001b[1G164 MiB [] 80% 1.1s\u001b[0K\u001b[1G164 MiB [] 81% 1.0s\u001b[0K\u001b[1G164 MiB [] 82% 1.0s\u001b[0K\u001b[1G164 MiB [] 83% 0.9s\u001b[0K\u001b[1G164 MiB [] 84% 0.8s\u001b[0K\u001b[1G164 MiB [] 85% 0.8s\u001b[0K\u001b[1G164 MiB [] 86% 0.7s\u001b[0K\u001b[1G164 MiB [] 87% 0.7s\u001b[0K\u001b[1G164 MiB [] 88% 0.6s\u001b[0K\u001b[1G164 MiB [] 90% 0.5s\u001b[0K\u001b[1G164 MiB [] 91% 0.4s\u001b[0K\u001b[1G164 MiB [] 92% 0.4s\u001b[0K\u001b[1G164 MiB [] 93% 0.4s\u001b[0K\u001b[1G164 MiB [] 94% 0.3s\u001b[0K\u001b[1G164 MiB [] 95% 0.2s\u001b[0K\u001b[1G164 MiB [] 96% 0.2s\u001b[0K\u001b[1G164 MiB [] 97% 0.1s\u001b[0K\u001b[1G164 MiB [] 98% 0.1s\u001b[0K\u001b[1G164 MiB [] 99% 0.0s\u001b[0K\u001b[1G164 MiB [] 100% 0.0s\u001b[0K\n", "Chromium 129.0.6668.29 (playwright build v1134) downloaded to /root/.cache/ms-playwright/chromium-1134\n", "Downloading FFMPEG playwright build v1010\u001b[2m from https://playwright.azureedge.net/builds/ffmpeg/1010/ffmpeg-linux.zip\u001b[22m\n", "\u001b[1G2.3 MiB [] 0% 0.0s\u001b[0K\u001b[1G2.3 MiB [] 3% 0.5s\u001b[0K\u001b[1G2.3 MiB [] 9% 0.3s\u001b[0K\u001b[1G2.3 MiB [] 24% 0.2s\u001b[0K\u001b[1G2.3 MiB [] 46% 0.1s\u001b[0K\u001b[1G2.3 MiB [] 61% 0.1s\u001b[0K\u001b[1G2.3 MiB [] 82% 0.0s\u001b[0K\u001b[1G2.3 MiB [] 100% 0.0s\u001b[0K\n", "FFMPEG playwright build v1010 downloaded to /root/.cache/ms-playwright/ffmpeg-1010\n", "Downloading Firefox 130.0 (playwright build v1463)\u001b[2m from https://playwright.azureedge.net/builds/firefox/1463/firefox-ubuntu-22.04.zip\u001b[22m\n", "\u001b[1G86.4 MiB [] 0% 0.0s\u001b[0K\u001b[1G86.4 MiB [] 0% 15.9s\u001b[0K\u001b[1G86.4 MiB [] 0% 9.3s\u001b[0K\u001b[1G86.4 MiB [] 0% 10.2s\u001b[0K\u001b[1G86.4 MiB [] 1% 11.0s\u001b[0K\u001b[1G86.4 MiB [] 1% 8.0s\u001b[0K\u001b[1G86.4 MiB [] 2% 7.6s\u001b[0K\u001b[1G86.4 MiB [] 2% 6.4s\u001b[0K\u001b[1G86.4 MiB [] 3% 5.5s\u001b[0K\u001b[1G86.4 MiB [] 4% 4.8s\u001b[0K\u001b[1G86.4 MiB [] 4% 4.6s\u001b[0K\u001b[1G86.4 MiB [] 5% 4.5s\u001b[0K\u001b[1G86.4 MiB [] 5% 4.3s\u001b[0K\u001b[1G86.4 MiB [] 6% 4.5s\u001b[0K\u001b[1G86.4 MiB [] 6% 4.4s\u001b[0K\u001b[1G86.4 MiB [] 7% 4.2s\u001b[0K\u001b[1G86.4 MiB [] 8% 4.1s\u001b[0K\u001b[1G86.4 MiB [] 8% 4.2s\u001b[0K\u001b[1G86.4 MiB [] 9% 3.9s\u001b[0K\u001b[1G86.4 MiB [] 9% 3.7s\u001b[0K\u001b[1G86.4 MiB [] 10% 3.7s\u001b[0K\u001b[1G86.4 MiB [] 11% 3.3s\u001b[0K\u001b[1G86.4 MiB [] 13% 3.0s\u001b[0K\u001b[1G86.4 MiB [] 14% 2.9s\u001b[0K\u001b[1G86.4 MiB [] 15% 2.6s\u001b[0K\u001b[1G86.4 MiB [] 17% 2.5s\u001b[0K\u001b[1G86.4 MiB [] 18% 2.3s\u001b[0K\u001b[1G86.4 MiB [] 19% 2.2s\u001b[0K\u001b[1G86.4 MiB [] 21% 2.0s\u001b[0K\u001b[1G86.4 MiB [] 22% 2.0s\u001b[0K\u001b[1G86.4 MiB [] 23% 1.9s\u001b[0K\u001b[1G86.4 MiB [] 24% 1.8s\u001b[0K\u001b[1G86.4 MiB [] 26% 1.7s\u001b[0K\u001b[1G86.4 MiB [] 28% 1.6s\u001b[0K\u001b[1G86.4 MiB [] 29% 1.6s\u001b[0K\u001b[1G86.4 MiB [] 30% 1.5s\u001b[0K\u001b[1G86.4 MiB [] 32% 1.5s\u001b[0K\u001b[1G86.4 MiB [] 35% 1.3s\u001b[0K\u001b[1G86.4 MiB [] 36% 1.3s\u001b[0K\u001b[1G86.4 MiB [] 39% 1.2s\u001b[0K\u001b[1G86.4 MiB [] 41% 1.1s\u001b[0K\u001b[1G86.4 MiB [] 42% 1.1s\u001b[0K\u001b[1G86.4 MiB [] 44% 1.0s\u001b[0K\u001b[1G86.4 MiB [] 46% 1.0s\u001b[0K\u001b[1G86.4 MiB [] 47% 0.9s\u001b[0K\u001b[1G86.4 MiB [] 49% 0.9s\u001b[0K\u001b[1G86.4 MiB [] 50% 0.9s\u001b[0K\u001b[1G86.4 MiB [] 52% 0.8s\u001b[0K\u001b[1G86.4 MiB [] 54% 0.8s\u001b[0K\u001b[1G86.4 MiB [] 55% 0.7s\u001b[0K\u001b[1G86.4 MiB [] 57% 0.7s\u001b[0K\u001b[1G86.4 MiB [] 59% 0.6s\u001b[0K\u001b[1G86.4 MiB [] 61% 0.6s\u001b[0K\u001b[1G86.4 MiB [] 63% 0.6s\u001b[0K\u001b[1G86.4 MiB [] 65% 0.5s\u001b[0K\u001b[1G86.4 MiB [] 67% 0.5s\u001b[0K\u001b[1G86.4 MiB [] 68% 0.5s\u001b[0K\u001b[1G86.4 MiB [] 70% 0.5s\u001b[0K\u001b[1G86.4 MiB [] 71% 0.4s\u001b[0K\u001b[1G86.4 MiB [] 72% 0.4s\u001b[0K\u001b[1G86.4 MiB [] 73% 0.4s\u001b[0K\u001b[1G86.4 MiB [] 75% 0.4s\u001b[0K\u001b[1G86.4 MiB [] 76% 0.3s\u001b[0K\u001b[1G86.4 MiB [] 78% 0.3s\u001b[0K\u001b[1G86.4 MiB [] 79% 0.3s\u001b[0K\u001b[1G86.4 MiB [] 81% 0.3s\u001b[0K\u001b[1G86.4 MiB [] 82% 0.3s\u001b[0K\u001b[1G86.4 MiB [] 83% 0.2s\u001b[0K\u001b[1G86.4 MiB [] 85% 0.2s\u001b[0K\u001b[1G86.4 MiB [] 87% 0.2s\u001b[0K\u001b[1G86.4 MiB [] 89% 0.2s\u001b[0K\u001b[1G86.4 MiB [] 90% 0.1s\u001b[0K\u001b[1G86.4 MiB [] 92% 0.1s\u001b[0K\u001b[1G86.4 MiB [] 94% 0.1s\u001b[0K\u001b[1G86.4 MiB [] 96% 0.0s\u001b[0K\u001b[1G86.4 MiB [] 98% 0.0s\u001b[0K\u001b[1G86.4 MiB [] 100% 0.0s\u001b[0K\n", "Firefox 130.0 (playwright build v1463) downloaded to /root/.cache/ms-playwright/firefox-1463\n", "Downloading Webkit 18.0 (playwright build v2070)\u001b[2m from https://playwright.azureedge.net/builds/webkit/2070/webkit-ubuntu-22.04.zip\u001b[22m\n", "\u001b[1G88.2 MiB [] 0% 0.0s\u001b[0K\u001b[1G88.2 MiB [] 0% 34.2s\u001b[0K\u001b[1G88.2 MiB [] 0% 35.9s\u001b[0K\u001b[1G88.2 MiB [] 0% 22.6s\u001b[0K\u001b[1G88.2 MiB [] 0% 19.1s\u001b[0K\u001b[1G88.2 MiB [] 0% 23.4s\u001b[0K\u001b[1G88.2 MiB [] 0% 24.6s\u001b[0K\u001b[1G88.2 MiB [] 0% 24.5s\u001b[0K\u001b[1G88.2 MiB [] 0% 23.6s\u001b[0K\u001b[1G88.2 MiB [] 0% 22.9s\u001b[0K\u001b[1G88.2 MiB [] 0% 26.2s\u001b[0K\u001b[1G88.2 MiB [] 0% 25.0s\u001b[0K\u001b[1G88.2 MiB [] 1% 20.2s\u001b[0K\u001b[1G88.2 MiB [] 1% 25.0s\u001b[0K\u001b[1G88.2 MiB [] 1% 35.7s\u001b[0K\u001b[1G88.2 MiB [] 1% 38.3s\u001b[0K\u001b[1G88.2 MiB [] 1% 40.7s\u001b[0K\u001b[1G88.2 MiB [] 1% 43.0s\u001b[0K\u001b[1G88.2 MiB [] 1% 43.2s\u001b[0K\u001b[1G88.2 MiB [] 1% 43.4s\u001b[0K\u001b[1G88.2 MiB [] 1% 44.6s\u001b[0K\u001b[1G88.2 MiB [] 1% 50.6s\u001b[0K\u001b[1G88.2 MiB [] 1% 52.2s\u001b[0K\u001b[1G88.2 MiB [] 1% 53.1s\u001b[0K\u001b[1G88.2 MiB [] 1% 53.4s\u001b[0K\u001b[1G88.2 MiB [] 1% 53.6s\u001b[0K\u001b[1G88.2 MiB [] 1% 52.9s\u001b[0K\u001b[1G88.2 MiB [] 1% 52.3s\u001b[0K\u001b[1G88.2 MiB [] 2% 49.9s\u001b[0K\u001b[1G88.2 MiB [] 2% 49.4s\u001b[0K\u001b[1G88.2 MiB [] 2% 48.8s\u001b[0K\u001b[1G88.2 MiB [] 2% 47.9s\u001b[0K\u001b[1G88.2 MiB [] 2% 45.9s\u001b[0K\u001b[1G88.2 MiB [] 2% 43.9s\u001b[0K\u001b[1G88.2 MiB [] 2% 42.2s\u001b[0K\u001b[1G88.2 MiB [] 2% 41.3s\u001b[0K\u001b[1G88.2 MiB [] 2% 40.5s\u001b[0K\u001b[1G88.2 MiB [] 3% 37.8s\u001b[0K\u001b[1G88.2 MiB [] 3% 36.4s\u001b[0K\u001b[1G88.2 MiB [] 3% 33.9s\u001b[0K\u001b[1G88.2 MiB [] 3% 34.0s\u001b[0K\u001b[1G88.2 MiB [] 3% 33.8s\u001b[0K\u001b[1G88.2 MiB [] 3% 32.7s\u001b[0K\u001b[1G88.2 MiB [] 4% 32.2s\u001b[0K\u001b[1G88.2 MiB [] 4% 30.5s\u001b[0K\u001b[1G88.2 MiB [] 4% 30.3s\u001b[0K\u001b[1G88.2 MiB [] 4% 29.7s\u001b[0K\u001b[1G88.2 MiB [] 5% 27.2s\u001b[0K\u001b[1G88.2 MiB [] 5% 27.0s\u001b[0K\u001b[1G88.2 MiB [] 5% 27.2s\u001b[0K\u001b[1G88.2 MiB [] 5% 27.0s\u001b[0K\u001b[1G88.2 MiB [] 5% 26.5s\u001b[0K\u001b[1G88.2 MiB [] 5% 25.4s\u001b[0K\u001b[1G88.2 MiB [] 5% 25.2s\u001b[0K\u001b[1G88.2 MiB [] 6% 23.7s\u001b[0K\u001b[1G88.2 MiB [] 6% 23.4s\u001b[0K\u001b[1G88.2 MiB [] 6% 22.1s\u001b[0K\u001b[1G88.2 MiB [] 7% 21.0s\u001b[0K\u001b[1G88.2 MiB [] 7% 20.4s\u001b[0K\u001b[1G88.2 MiB [] 7% 19.7s\u001b[0K\u001b[1G88.2 MiB [] 8% 19.0s\u001b[0K\u001b[1G88.2 MiB [] 8% 18.6s\u001b[0K\u001b[1G88.2 MiB [] 9% 17.2s\u001b[0K\u001b[1G88.2 MiB [] 9% 17.1s\u001b[0K\u001b[1G88.2 MiB [] 9% 17.2s\u001b[0K\u001b[1G88.2 MiB [] 9% 16.7s\u001b[0K\u001b[1G88.2 MiB [] 10% 15.6s\u001b[0K\u001b[1G88.2 MiB [] 10% 14.7s\u001b[0K\u001b[1G88.2 MiB [] 10% 14.6s\u001b[0K\u001b[1G88.2 MiB [] 11% 14.6s\u001b[0K\u001b[1G88.2 MiB [] 11% 14.8s\u001b[0K\u001b[1G88.2 MiB [] 11% 14.9s\u001b[0K\u001b[1G88.2 MiB [] 11% 14.8s\u001b[0K\u001b[1G88.2 MiB [] 11% 14.9s\u001b[0K\u001b[1G88.2 MiB [] 11% 14.8s\u001b[0K\u001b[1G88.2 MiB [] 12% 13.9s\u001b[0K\u001b[1G88.2 MiB [] 12% 14.0s\u001b[0K\u001b[1G88.2 MiB [] 12% 13.8s\u001b[0K\u001b[1G88.2 MiB [] 13% 13.1s\u001b[0K\u001b[1G88.2 MiB [] 14% 12.2s\u001b[0K\u001b[1G88.2 MiB [] 15% 11.3s\u001b[0K\u001b[1G88.2 MiB [] 16% 10.7s\u001b[0K\u001b[1G88.2 MiB [] 17% 10.3s\u001b[0K\u001b[1G88.2 MiB [] 18% 9.9s\u001b[0K\u001b[1G88.2 MiB [] 18% 9.8s\u001b[0K\u001b[1G88.2 MiB [] 18% 9.9s\u001b[0K\u001b[1G88.2 MiB [] 19% 10.0s\u001b[0K\u001b[1G88.2 MiB [] 19% 10.1s\u001b[0K\u001b[1G88.2 MiB [] 19% 10.2s\u001b[0K\u001b[1G88.2 MiB [] 19% 10.1s\u001b[0K\u001b[1G88.2 MiB [] 19% 10.2s\u001b[0K\u001b[1G88.2 MiB [] 20% 10.2s\u001b[0K\u001b[1G88.2 MiB [] 20% 10.3s\u001b[0K\u001b[1G88.2 MiB [] 20% 10.2s\u001b[0K\u001b[1G88.2 MiB [] 20% 10.5s\u001b[0K\u001b[1G88.2 MiB [] 20% 10.6s\u001b[0K\u001b[1G88.2 MiB [] 20% 10.5s\u001b[0K\u001b[1G88.2 MiB [] 21% 10.5s\u001b[0K\u001b[1G88.2 MiB [] 21% 10.1s\u001b[0K\u001b[1G88.2 MiB [] 22% 9.9s\u001b[0K\u001b[1G88.2 MiB [] 23% 9.6s\u001b[0K\u001b[1G88.2 MiB [] 23% 9.3s\u001b[0K\u001b[1G88.2 MiB [] 24% 9.2s\u001b[0K\u001b[1G88.2 MiB [] 24% 9.0s\u001b[0K\u001b[1G88.2 MiB [] 25% 8.8s\u001b[0K\u001b[1G88.2 MiB [] 25% 8.5s\u001b[0K\u001b[1G88.2 MiB [] 26% 8.1s\u001b[0K\u001b[1G88.2 MiB [] 27% 8.3s\u001b[0K\u001b[1G88.2 MiB [] 27% 8.2s\u001b[0K\u001b[1G88.2 MiB [] 28% 8.1s\u001b[0K\u001b[1G88.2 MiB [] 28% 8.0s\u001b[0K\u001b[1G88.2 MiB [] 29% 7.8s\u001b[0K\u001b[1G88.2 MiB [] 29% 7.6s\u001b[0K\u001b[1G88.2 MiB [] 30% 7.6s\u001b[0K\u001b[1G88.2 MiB [] 30% 7.5s\u001b[0K\u001b[1G88.2 MiB [] 30% 7.4s\u001b[0K\u001b[1G88.2 MiB [] 31% 7.3s\u001b[0K\u001b[1G88.2 MiB [] 31% 7.2s\u001b[0K\u001b[1G88.2 MiB [] 32% 7.1s\u001b[0K\u001b[1G88.2 MiB [] 32% 7.0s\u001b[0K\u001b[1G88.2 MiB [] 32% 6.9s\u001b[0K\u001b[1G88.2 MiB [] 33% 6.7s\u001b[0K\u001b[1G88.2 MiB [] 33% 6.8s\u001b[0K\u001b[1G88.2 MiB [] 33% 6.9s\u001b[0K\u001b[1G88.2 MiB [] 34% 6.8s\u001b[0K\u001b[1G88.2 MiB [] 34% 6.7s\u001b[0K\u001b[1G88.2 MiB [] 35% 6.7s\u001b[0K\u001b[1G88.2 MiB [] 35% 6.8s\u001b[0K\u001b[1G88.2 MiB [] 35% 6.7s\u001b[0K\u001b[1G88.2 MiB [] 35% 6.6s\u001b[0K\u001b[1G88.2 MiB [] 36% 6.5s\u001b[0K\u001b[1G88.2 MiB [] 36% 6.4s\u001b[0K\u001b[1G88.2 MiB [] 37% 6.3s\u001b[0K\u001b[1G88.2 MiB [] 37% 6.2s\u001b[0K\u001b[1G88.2 MiB [] 38% 6.2s\u001b[0K\u001b[1G88.2 MiB [] 38% 6.1s\u001b[0K\u001b[1G88.2 MiB [] 39% 6.0s\u001b[0K\u001b[1G88.2 MiB [] 40% 5.8s\u001b[0K\u001b[1G88.2 MiB [] 41% 5.7s\u001b[0K\u001b[1G88.2 MiB [] 42% 5.5s\u001b[0K\u001b[1G88.2 MiB [] 42% 5.4s\u001b[0K\u001b[1G88.2 MiB [] 43% 5.3s\u001b[0K\u001b[1G88.2 MiB [] 43% 5.2s\u001b[0K\u001b[1G88.2 MiB [] 44% 5.1s\u001b[0K\u001b[1G88.2 MiB [] 45% 5.0s\u001b[0K\u001b[1G88.2 MiB [] 46% 4.8s\u001b[0K\u001b[1G88.2 MiB [] 47% 4.5s\u001b[0K\u001b[1G88.2 MiB [] 48% 4.3s\u001b[0K\u001b[1G88.2 MiB [] 50% 4.1s\u001b[0K\u001b[1G88.2 MiB [] 52% 3.8s\u001b[0K\u001b[1G88.2 MiB [] 53% 3.7s\u001b[0K\u001b[1G88.2 MiB [] 53% 3.6s\u001b[0K\u001b[1G88.2 MiB [] 54% 3.5s\u001b[0K\u001b[1G88.2 MiB [] 55% 3.5s\u001b[0K\u001b[1G88.2 MiB [] 55% 3.4s\u001b[0K\u001b[1G88.2 MiB [] 56% 3.4s\u001b[0K\u001b[1G88.2 MiB [] 56% 3.3s\u001b[0K\u001b[1G88.2 MiB [] 57% 3.2s\u001b[0K\u001b[1G88.2 MiB [] 58% 3.1s\u001b[0K\u001b[1G88.2 MiB [] 60% 2.9s\u001b[0K\u001b[1G88.2 MiB [] 61% 2.9s\u001b[0K\u001b[1G88.2 MiB [] 61% 2.8s\u001b[0K\u001b[1G88.2 MiB [] 62% 2.7s\u001b[0K\u001b[1G88.2 MiB [] 63% 2.6s\u001b[0K\u001b[1G88.2 MiB [] 64% 2.5s\u001b[0K\u001b[1G88.2 MiB [] 65% 2.4s\u001b[0K\u001b[1G88.2 MiB [] 66% 2.3s\u001b[0K\u001b[1G88.2 MiB [] 67% 2.2s\u001b[0K\u001b[1G88.2 MiB [] 68% 2.1s\u001b[0K\u001b[1G88.2 MiB [] 69% 2.1s\u001b[0K\u001b[1G88.2 MiB [] 70% 2.0s\u001b[0K\u001b[1G88.2 MiB [] 70% 1.9s\u001b[0K\u001b[1G88.2 MiB [] 71% 1.9s\u001b[0K\u001b[1G88.2 MiB [] 72% 1.8s\u001b[0K\u001b[1G88.2 MiB [] 73% 1.7s\u001b[0K\u001b[1G88.2 MiB [] 74% 1.6s\u001b[0K\u001b[1G88.2 MiB [] 75% 1.5s\u001b[0K\u001b[1G88.2 MiB [] 76% 1.4s\u001b[0K\u001b[1G88.2 MiB [] 78% 1.4s\u001b[0K\u001b[1G88.2 MiB [] 79% 1.3s\u001b[0K\u001b[1G88.2 MiB [] 80% 1.2s\u001b[0K\u001b[1G88.2 MiB [] 81% 1.1s\u001b[0K\u001b[1G88.2 MiB [] 82% 1.1s\u001b[0K\u001b[1G88.2 MiB [] 83% 1.0s\u001b[0K\u001b[1G88.2 MiB [] 85% 0.9s\u001b[0K\u001b[1G88.2 MiB [] 86% 0.8s\u001b[0K\u001b[1G88.2 MiB [] 87% 0.7s\u001b[0K\u001b[1G88.2 MiB [] 88% 0.7s\u001b[0K\u001b[1G88.2 MiB [] 89% 0.6s\u001b[0K\u001b[1G88.2 MiB [] 90% 0.6s\u001b[0K\u001b[1G88.2 MiB [] 90% 0.5s\u001b[0K\u001b[1G88.2 MiB [] 91% 0.5s\u001b[0K\u001b[1G88.2 MiB [] 92% 0.4s\u001b[0K\u001b[1G88.2 MiB [] 93% 0.3s\u001b[0K\u001b[1G88.2 MiB [] 95% 0.2s\u001b[0K\u001b[1G88.2 MiB [] 96% 0.2s\u001b[0K\u001b[1G88.2 MiB [] 98% 0.1s\u001b[0K\u001b[1G88.2 MiB [] 99% 0.0s\u001b[0K\u001b[1G88.2 MiB [] 100% 0.0s\u001b[0K\n", "Webkit 18.0 (playwright build v2070) downloaded to /root/.cache/ms-playwright/webkit-2070\n", "Playwright Host validation warning: \n", "╔══════════════════════════════════════════════════════╗\n", "║ Host system is missing dependencies to run browsers. ║\n", "║ Missing libraries:                                   ║\n", "║     libwoff2dec.so.1.0.2                             ║\n", "║     libgstgl-1.0.so.0                                ║\n", "║     libgstcodecparsers-1.0.so.0                      ║\n", "║     libharfbuzz-icu.so.0                             ║\n", "║     libenchant-2.so.2                                ║\n", "║     libsecret-1.so.0                                 ║\n", "║     libhyphen.so.0                                   ║\n", "║     libmanette-0.2.so.0                              ║\n", "╚══════════════════════════════════════════════════════╝\n", "    at validateDependenciesLinux (/usr/local/lib/python3.10/dist-packages/playwright/driver/package/lib/server/registry/dependencies.js:216:9)\n", "    at async Registry._validateHostRequirements (/usr/local/lib/python3.10/dist-packages/playwright/driver/package/lib/server/registry/index.js:626:43)\n", "    at async Registry._validateHostRequirementsForExecutableIfNeeded (/usr/local/lib/python3.10/dist-packages/playwright/driver/package/lib/server/registry/index.js:724:7)\n", "    at async Registry.validateHostRequirementsForExecutablesIfNeeded (/usr/local/lib/python3.10/dist-packages/playwright/driver/package/lib/server/registry/index.js:713:43)\n", "    at async t.<anonymous> (/usr/local/lib/python3.10/dist-packages/playwright/driver/package/lib/cli/program.js:119:7)\n"]}], "source": ["!pip install playwright\n", "!playwright install"]}, {"cell_type": "markdown", "metadata": {"id": "YJPj7vLyt7id"}, "source": ["We have to use async since we are using Google Colab. If you're\n", "not using a notebook you can use sync instead. Please refer to the article written to understand where our selectors came from."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ie45-zFDpa29"}, "outputs": [], "source": ["from playwright.async_api import async_playwright"]}, {"cell_type": "markdown", "metadata": {"id": "JwTwNM7Ft_Pc"}, "source": ["We are using the URL that is inside of the websites iframe, and we are using selectors to make sure we are waiting for the information we want to load. We want to grab the name of each menu item along with its description. Please refer to the written article to understand this function better if necessary!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "TzxJi354ppIl", "outputId": "cc86141e-4fc6-404a-facf-6937db009f20"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: , Description: \n", "Name: <PERSON><PERSON>, Description: Create Your Own Dirty Soda:\n", "Soda + Flavors, Fruits, & Creams\n", "Name: DDD, Description: Diet Dr Pepper + Coconut (25 - 70 Calories)\n", "Name: Dirty Dr Pepper, Description: <PERSON> Pepper + Coconut (120 - 440 Calories)\n", "Name: Dirty S.O.<PERSON>, Description: Dr Pepper + Coconut + Peach (120 - 440 Calories)\n", "Name: Dr <PERSON>, Description: Dr Pepper + Cinnamon + Coconut + Cinnamon Stick + Half & Half (140 - 490 Calories)\n", "Name: Life's a Peach, Description: Dr Pepper + Vanilla + Peach + Half & Half (130 - 480 Calories)\n", "Name: Naughty & Nice, Description: <PERSON> + English Toffee + Half & Half (130 - 470 Calories)\n", "Name: Princess <PERSON><PERSON>, Description: Dr Pepper + Peach + Coconut Cream (140 - 510 Calories)\n", "Name: Raspberry Dream, Description: Dr Pepper + Raspberry Puree + Coconut Cream (150 - 550 Calories)\n", "Name: Save Me Jade, Description: Diet Dr Pepper + Sugar Free Vanilla + Sugar Free Coconut (0  Calories)\n", "Name: Spring Fling, Description: Dr Pepper + Vanilla + Strawberry Puree + Coconut Cream (170 - 610 Calories)\n", "Name: Texas Tab, Description: Dr Pepper + Vanilla + Coconut Cream (140 - 500 Calories)\n", "Name: The Heartbreaker, Description: Dr Pepper + Blackberry + Coconut + Half & Half (130 - 470 Calories)\n", "Name: The Ring King, Description: Dr Pepper + Cupcake + Strawberry Puree + Vanilla Cream (170 -  620 Calories)\n", "Name: Big Al, Description: Diet Coke + Coconut + Fresh Lime (15 - 40 Calories)\n", "Name: This Bliss, Description: Diet Coke + Cranberry + Fresh Lime (25 - 80 Calories)\n", "Name: The Founder, Description: Diet Coke + Sugar Free Coconut + Fresh Lime + Coconut Cream (20 - 80 Calories)\n", "Name: Just Peach<PERSON>, Description: Coke Zero Sugar + Pineapple + Peach Puree + Fresh Lime + Coconut Cream (60 - 220 Calories)\n", "Name: <PERSON><PERSON><PERSON><PERSON>, Description: Coke + Pineapple + Coconut Cream (150 - 540 Calories)\n", "Name: Cherry Bomb, Description: Pepsi + Cherry + Coconut + Vanilla Cream (150 - 520 Calories)\n", "Name: <PERSON> Babe, Description: Mountain Dew + Raspberry + Peach + Vanilla Cream (160 - 570 Calories)\n", "Name: <PERSON> Wild, Description: Mountain Dew + Mango Puree + Strawberry Puree (150 - 590 Calories)\n", "Name: <PERSON><PERSON>, Description: Mountain Dew + Pineapple + Fresh Lime + Raspberry Puree + Coconut Cream (170 -  650 Calories)\n", "Name: Endless Summer, Description: Mountain Dew + Pomegranate + Grapefruit + Fresh Lime (140 - 500 Calories)\n", "Name: Guava Have It!, Description: Mountain Dew + Guava + Strawberry + Coconut Cream (160 - 560 Calories)\n", "Name: <PERSON><PERSON>, Description: Mountain Dew + Passion Fruit + Strawberry Puree + Fresh Orange (160 - 590 Calories)\n", "Name: The Rocket, Description: Mountain Dew + Raspberry + Coconut + Blackberry + Vanilla Cream (160 - 570 Calories)\n", "Name: Watermelon Sugar, Description: Mountain Dew + Mango + Watermelon + Coconut Cream + Passion Fruit Popping Pearls (180 - 590 Calories)\n", "Name: <PERSON><PERSON>, Description: Sprite + Lemonade + Mango + Pineapple + Strawberry + Coconut Cream (160 - 600 Calories)\n", "Name: Loop-T-<PERSON>, Description: Sprite + Strawberry + Watermelon + Peach (120 - 500 Calories)\n", "Name: <PERSON><PERSON><PERSON> Pineap<PERSON>, Description: Sprite + Pineapple + Watermelon + Strawberry Popping Pearls (150 - 520 Calories)\n", "Name: R<PERSON><PERSON>, Description: Sprite + Cranberry + Raspberry Puree + Fresh Lime (140 - 590 Calories)\n", "Name: Shark Attack, Description: Sprite + Lemonade + Blue Raspberry + G<PERSON>my Shark (140 - 460 Calories)\n", "Name: Unlucky <PERSON><PERSON>, Description: Sprite + Lemonade + Strawberry + Gummy Shark (140 - 450 Calories)\n", "Name: <PERSON> Bum, Description: Lemonade + Guava + Grapefruit + Fresh Orange\n", "Name: The Tropic, Description: Lemonade + Mango + Passion Fruit + Vanilla Cream\n", "Name: Buttery Beer, Description: Root Beer + Butterscotch + Vanilla Creme (140 - 490 Calories)\n", "Name: <PERSON>, Description: Root Beer + Toasted Marshmallow + Half & Half (130 - 460 Calories)\n", "Name: Cinnamon Cider, Description: Ginger Ale + Lemonade + Cinnamon + Apple + Cinnamon Stick (190 - 460 Calories)\n", "Name: Island Time, Description: Fresca + Passion Fruit + Fresh Orange + Mango Puree + Coconut Cream (60 - 240 Calories)\n", "Name: <PERSON>, Description: Fresca + Peach + Strawberry+ Raspberry + Fresh Lemon (25 - 80 Calories)\n", "Name: <PERSON><PERSON> All Day, Description: Fresca + Lemonade + Pomegranate + Peach + Strawberry Puree + Fresh Lemon (60 - 200 Calories)\n", "Name: Reviver, Description: Create Your Own\n", "Reviver Energy + Flavors, Fruits, & Creams\n", "Name: <PERSON> Queen, Description: Sugar Free Reviver Energy + Sugar Free Strawberry + Sugar Free Peach + Sugar Free Coconut + Light Lemonade (80 - 210 Calories)\n", "Name: Mountain Man, Description: Reviver Energy + Blackberry + Coconut + Fresh Lime + Half & Half + Mountain Dew (290 - 630 Calories)\n", "Name: P.O.G., Description: Reviver Energy + Passion Fruit + Guava + Fresh Orange (220 - 430 Calories)\n", "Name: Ride or Die, Description: Reviver Energy + Peach + Pineapple + Coconut Cream (230 - 480 Calories)\n", "Name: Super Trooper, Description: Reviver Energy + Lemonade + Mango + Strawberry Puree + Coconut Cream (340 - 760 Calories)\n", "Name: Surf's Up, Description: Reviver Energy + Lemonade + Peach + Mango + Raspberry + Fresh Orange (280 - 560 Calories)\n", "Name: Weekender, Description: Reviver + Strawberry + Coconut + Coconut Cream + Sprite (280 - 580 Calories)\n", "Name: All Star, Description: Blended Reviver Energy + Lemonade + Guava + Strawberry + Coconut Cream (280 - 550 Calories)\n", "Name: Cloud 9, Description: Blended Reviver Energy + Mountain Dew + Coconut + Pineapple + Blue Raspberry + Half & Half (280 - 550 Calories)\n", "Name: <PERSON>, Description: Blended Reviver Energy + Mountain Dew + Pineapple + Strawberry Puree + Coconut Cream (390 - 760 Calories)\n", "Name: <PERSON><PERSON><PERSON><PERSON>, Description: Blended Reviver Energy + Sprite + Grapefruit + Pineapple + Mango Puree + Coconut Cream (300 - 600 Calories)\n", "Name: Peaches n Cream, Description: Blended Reviver Energy + Sprite + Peach Puree + Vanilla Cream (250 - 500 Calories)\n", "Name: Polar Punch, Description: Blended Reviver Energy + Ginger Ale + Blackberry + Strawberry + Raspberry + Vanilla Cream (270 - 540 Calories)\n", "Name: Su<PERSON> Punch, Description: Blended Reviver Energy + Lemonade + Grapefruit + Mango (250 - 490 Calories)\n", "Name: Refresher, Description: Create Your Own: Water + Flavors, Fruits, & Creams\n", "Name: Autumn Blush, Description: Water + Apple + Raspberry + Mango Puree + Vanilla Cream (120 - 420 Calories)\n", "Name: <PERSON>, Description: Water + Sugar Free Strawberry + Fresh Lime (0 - 10 Calories)\n", "Name: <PERSON>, Description: Water + Cranberry + Sugar Free Vanilla + Fresh Orange (30 - 90 Calories)\n", "Name: Fruit Water, Description: Water + Sugar Free Coconut + Sugar Free Vanilla + Frozen Strawberry + Frozen Mango (25 - 80 Calories)\n", "Name: <PERSON><PERSON>, Description: Water + Sugar Free Coconut + Sugar Free Vanilla + Mango Puree + Frozen Mango + Coconut Cream (110 - 340 Calories)\n", "Name: Pretty in Pink, Description: Water + Guava + Grapefruit + Fresh Orange (50 - 140 Calories)\n", "Name: <PERSON>, Description: Water + Sugar Free Peach + Fresh Lemon (0 - 5 Calories)\n", "Name: Strawberry Breeze, Description: Water + Sugar Free Coconut + Sugar Free Vanilla + Strawberry Puree + Frozen Strawberry + Coconut Cream (90 - 310 Calories)\n", "Name: Summer Splash, Description: Water + Sugar Free Pineapple + Strawberry Puree + Fresh Lemon (60 - 230 Calories)\n", "Name: Tiki Tiki, Description: Water + Sugar Free Coconut + Fresh Orange (10 - 25 Calories)\n", "Name: The Fighter, Description: Water +  Sugar Free Vanilla + Sugar Free Peach + Sugar Free Pineapple + Fresh Lime + Raspberry Puree (60 - 230 Calories)\n", "Name: Unbreakable, Description: Water +  Grapefruit + Mango Puree + Fresh Lemon (100 - 350 Calories)\n", "Name: Water Cup, Description: Cup of Water or Ice\n", "Name: Salted Pretzel Bites, Description: Warm, buttery, salted pretzel bits. (410 Calories)\n", "Name: Strawberry Cream Cheese, Description: Cold Strawberry Cream Cheese Dip.  Great for a morning snack. Makes the pretzel bites taste like a delicious bagel. (60 Calories)\n", "Name: White Cheddar, Description: Warm White Cheddar Cheese Dip (110 Calories)\n", "Name: <PERSON>, Description: 410 Calories\n", "Name: Kid's <PERSON><PERSON>, Description: One mini frosted Sugar Cookie (240 Calories)\n", "Name: Chocolate Chip - Mini, Description: Mini Chocolate Chip Cookies made with a mix of milk chocolate and semi-sweet chips. Available in a Cup (12 mini's - 600 Calories) or Party Pack (60 minis).\n", "Name: Pumpkin Chocolate Chip - Mini, Description: Mini Pumpkin Chocolate Chip Cookies are back for a limited time! You will not want to miss our most anticipated cookie of the year! Available in a Cup (6 mini's - 480 Calories) or Party Pack (30 minis).\n", "Name: Hot - Hot Chocolate, Description: \n", "Name: Frozen - Hot Chocolate, Description: Create Your Own:\n", "Frozen Hot Chocolate + Flavors, Fruits, & Creams\n", "[{'name': '', 'description': ''}, {'name': 'Soda', 'description': 'Create Your Own Dirty Soda:\\nSoda + Flavors, Fruits, & Creams'}, {'name': 'DDD', 'description': 'Diet Dr Pepper + Coconut (25 - 70 Calories)'}, {'name': 'Dirty Dr Pepper', 'description': 'Dr Pepper + Coconut (120 - 440 Calories)'}, {'name': 'Dirty S.O.P', 'description': 'Dr Pepper + Coconut + Peach (120 - 440 Calories)'}, {'name': 'Dr Spice', 'description': 'Dr Pepper + Cinnamon + Coconut + Cinnamon Stick + Half & Half (140 - 490 Calories)'}, {'name': \"Life's a Peach\", 'description': 'Dr Pepper + Vanilla + Peach + Half & Half (130 - 480 Calories)'}, {'name': 'Naughty & Nice', 'description': 'Dr Pepper + English Toffee + Half & Half (130 - 470 Calories)'}, {'name': 'Princess Peach', 'description': 'Dr Pepper + Peach + Coconut Cream (140 - 510 Calories)'}, {'name': 'Raspberry Dream', 'description': 'Dr Pepper + Raspberry Puree + Coconut Cream (150 - 550 Calories)'}, {'name': 'Save Me Jade', 'description': 'Diet Dr Pepper + Sugar Free Vanilla + Sugar Free Coconut (0  Calories)'}, {'name': 'Spring Fling', 'description': 'Dr Pepper + Vanilla + Strawberry Puree + Coconut Cream (170 - 610 Calories)'}, {'name': 'Texas Tab', 'description': 'Dr Pepper + Vanilla + Coconut Cream (140 - 500 Calories)'}, {'name': 'The Heartbreaker', 'description': 'Dr Pepper + Blackberry + Coconut + Half & Half (130 - 470 Calories)'}, {'name': 'The Ring King', 'description': 'Dr Pepper + Cupcake + Strawberry Puree + Vanilla Cream (170 -  620 Calories)'}, {'name': 'Big Al', 'description': 'Diet Coke + Coconut + Fresh Lime (15 - 40 Calories)'}, {'name': 'This Bliss', 'description': 'Diet Coke + Cranberry + Fresh Lime (25 - 80 Calories)'}, {'name': 'The Founder', 'description': 'Diet Coke + Sugar Free Coconut + Fresh Lime + Coconut Cream (20 - 80 Calories)'}, {'name': 'Just Peachy', 'description': 'Coke Zero Sugar + Pineapple + Peach Puree + Fresh Lime + Coconut Cream (60 - 220 Calories)'}, {'name': 'Waikiki', 'description': 'Coke + Pineapple + Coconut Cream (150 - 540 Calories)'}, {'name': 'Cherry Bomb', 'description': 'Pepsi + Cherry + Coconut + Vanilla Cream (150 - 520 Calories)'}, {'name': 'Beach Babe', 'description': 'Mountain Dew + Raspberry + Peach + Vanilla Cream (160 - 570 Calories)'}, {'name': 'Bloody Wild', 'description': 'Mountain Dew + Mango Puree + Strawberry Puree (150 - 590 Calories)'}, {'name': 'Dew Gooder', 'description': 'Mountain Dew + Pineapple + Fresh Lime + Raspberry Puree + Coconut Cream (170 -  650 Calories)'}, {'name': 'Endless Summer', 'description': 'Mountain Dew + Pomegranate + Grapefruit + Fresh Lime (140 - 500 Calories)'}, {'name': 'Guava Have It!', 'description': 'Mountain Dew + Guava + Strawberry + Coconut Cream (160 - 560 Calories)'}, {'name': 'Jolly Elf', 'description': 'Mountain Dew + Passion Fruit + Strawberry Puree + Fresh Orange (160 - 590 Calories)'}, {'name': 'The Rocket', 'description': 'Mountain Dew + Raspberry + Coconut + Blackberry + Vanilla Cream (160 - 570 Calories)'}, {'name': 'Watermelon Sugar', 'description': 'Mountain Dew + Mango + Watermelon + Coconut Cream + Passion Fruit Popping Pearls (180 - 590 Calories)'}, {'name': 'Hula Girl', 'description': 'Sprite + Lemonade + Mango + Pineapple + Strawberry + Coconut Cream (160 - 600 Calories)'}, {'name': 'Loop-T-Loop', 'description': 'Sprite + Strawberry + Watermelon + Peach (120 - 500 Calories)'}, {'name': \"Poppin' Pineapple\", 'description': 'Sprite + Pineapple + Watermelon + Strawberry Popping Pearls (150 - 520 Calories)'}, {'name': 'Riptide', 'description': 'Sprite + Cranberry + Raspberry Puree + Fresh Lime (140 - 590 Calories)'}, {'name': 'Shark Attack', 'description': 'Sprite + Lemonade + Blue Raspberry + Gummy Shark (140 - 460 Calories)'}, {'name': 'Unlucky Ducky', 'description': 'Sprite + Lemonade + Strawberry + Gummy Shark (140 - 450 Calories)'}, {'name': 'Beach Bum', 'description': 'Lemonade + Guava + Grapefruit + Fresh Orange'}, {'name': 'The Tropic', 'description': 'Lemonade + Mango + Passion Fruit + Vanilla Cream'}, {'name': 'Buttery Beer', 'description': 'Root Beer + Butterscotch + Vanilla Creme (140 - 490 Calories)'}, {'name': 'Happy Camper', 'description': 'Root Beer + Toasted Marshmallow + Half & Half (130 - 460 Calories)'}, {'name': 'Cinnamon Cider', 'description': 'Ginger Ale + Lemonade + Cinnamon + Apple + Cinnamon Stick (190 - 460 Calories)'}, {'name': 'Island Time', 'description': 'Fresca + Passion Fruit + Fresh Orange + Mango Puree + Coconut Cream (60 - 240 Calories)'}, {'name': 'Pink Bahama', 'description': 'Fresca + Peach + Strawberry+ Raspberry + Fresh Lemon (25 - 80 Calories)'}, {'name': 'Sleigh All Day', 'description': 'Fresca + Lemonade + Pomegranate + Peach + Strawberry Puree + Fresh Lemon (60 - 200 Calories)'}, {'name': 'Reviver', 'description': 'Create Your Own\\nReviver Energy + Flavors, Fruits, & Creams'}, {'name': 'Drama Queen', 'description': 'Sugar Free Reviver Energy + Sugar Free Strawberry + Sugar Free Peach + Sugar Free Coconut + Light Lemonade (80 - 210 Calories)'}, {'name': 'Mountain Man', 'description': 'Reviver Energy + Blackberry + Coconut + Fresh Lime + Half & Half + Mountain Dew (290 - 630 Calories)'}, {'name': 'P.O.G.', 'description': 'Reviver Energy + Passion Fruit + Guava + Fresh Orange (220 - 430 Calories)'}, {'name': 'Ride or Die', 'description': 'Reviver Energy + Peach + Pineapple + Coconut Cream (230 - 480 Calories)'}, {'name': 'Super Trooper', 'description': 'Reviver Energy + Lemonade + Mango + Strawberry Puree + Coconut Cream (340 - 760 Calories)'}, {'name': \"Surf's Up\", 'description': 'Reviver Energy + Lemonade + Peach + Mango + Raspberry + Fresh Orange (280 - 560 Calories)'}, {'name': 'Weekender', 'description': 'Reviver + Strawberry + Coconut + Coconut Cream + Sprite (280 - 580 Calories)'}, {'name': 'All Star', 'description': 'Blended Reviver Energy + Lemonade + Guava + Strawberry + Coconut Cream (280 - 550 Calories)'}, {'name': 'Cloud 9', 'description': 'Blended Reviver Energy + Mountain Dew + Coconut + Pineapple + Blue Raspberry + Half & Half (280 - 550 Calories)'}, {'name': 'Daisy Duke', 'description': 'Blended Reviver Energy + Mountain Dew + Pineapple + Strawberry Puree + Coconut Cream (390 - 760 Calories)'}, {'name': 'Flirty Flamango', 'description': 'Blended Reviver Energy + Sprite + Grapefruit + Pineapple + Mango Puree + Coconut Cream (300 - 600 Calories)'}, {'name': 'Peaches n Cream', 'description': 'Blended Reviver Energy + Sprite + Peach Puree + Vanilla Cream (250 - 500 Calories)'}, {'name': 'Polar Punch', 'description': 'Blended Reviver Energy + Ginger Ale + Blackberry + Strawberry + Raspberry + Vanilla Cream (270 - 540 Calories)'}, {'name': 'Sucker Punch', 'description': 'Blended Reviver Energy + Lemonade + Grapefruit + Mango (250 - 490 Calories)'}, {'name': 'Refresher', 'description': 'Create Your Own: Water + Flavors, Fruits, & Creams'}, {'name': 'Autumn Blush', 'description': 'Water + Apple + Raspberry + Mango Puree + Vanilla Cream (120 - 420 Calories)'}, {'name': 'Berry Nice', 'description': 'Water + Sugar Free Strawberry + Fresh Lime (0 - 10 Calories)'}, {'name': 'Berry Swigmas', 'description': 'Water + Cranberry + Sugar Free Vanilla + Fresh Orange (30 - 90 Calories)'}, {'name': 'Fruit Water', 'description': 'Water + Sugar Free Coconut + Sugar Free Vanilla + Frozen Strawberry + Frozen Mango (25 - 80 Calories)'}, {'name': 'Mango Breeze', 'description': 'Water + Sugar Free Coconut + Sugar Free Vanilla + Mango Puree + Frozen Mango + Coconut Cream (110 - 340 Calories)'}, {'name': 'Pretty in Pink', 'description': 'Water + Guava + Grapefruit + Fresh Orange (50 - 140 Calories)'}, {'name': 'Sandy Cheeks', 'description': 'Water + Sugar Free Peach + Fresh Lemon (0 - 5 Calories)'}, {'name': 'Strawberry Breeze', 'description': 'Water + Sugar Free Coconut + Sugar Free Vanilla + Strawberry Puree + Frozen Strawberry + Coconut Cream (90 - 310 Calories)'}, {'name': 'Summer Splash', 'description': 'Water + Sugar Free Pineapple + Strawberry Puree + Fresh Lemon (60 - 230 Calories)'}, {'name': 'Tiki Tiki', 'description': 'Water + Sugar Free Coconut + Fresh Orange (10 - 25 Calories)'}, {'name': 'The Fighter', 'description': 'Water +  Sugar Free Vanilla + Sugar Free Peach + Sugar Free Pineapple + Fresh Lime + Raspberry Puree (60 - 230 Calories)'}, {'name': 'Unbreakable', 'description': 'Water +  Grapefruit + Mango Puree + Fresh Lemon (100 - 350 Calories)'}, {'name': 'Water Cup', 'description': 'Cup of Water or Ice'}, {'name': 'Salted Pretzel Bites', 'description': 'Warm, buttery, salted pretzel bits. (410 Calories)'}, {'name': 'Strawberry Cream Cheese', 'description': 'Cold Strawberry Cream Cheese Dip.  Great for a morning snack. Makes the pretzel bites taste like a delicious bagel. (60 Calories)'}, {'name': 'White Cheddar', 'description': 'Warm White Cheddar Cheese Dip (110 Calories)'}, {'name': 'Sugar Cookie', 'description': '410 Calories'}, {'name': \"Kid's Cookie\", 'description': 'One mini frosted Sugar Cookie (240 Calories)'}, {'name': 'Chocolate Chip - Mini', 'description': \"Mini Chocolate Chip Cookies made with a mix of milk chocolate and semi-sweet chips. Available in a Cup (12 mini's - 600 Calories) or Party Pack (60 minis).\"}, {'name': 'Pumpkin Chocolate Chip - Mini', 'description': \"Mini Pumpkin Chocolate Chip Cookies are back for a limited time! You will not want to miss our most anticipated cookie of the year! Available in a Cup (6 mini's - 480 Calories) or Party Pack (30 minis).\"}, {'name': 'Hot - Hot Chocolate', 'description': ''}, {'name': 'Frozen - Hot Chocolate', 'description': 'Create Your Own:\\nFrozen Hot Chocolate + Flavors, Fruits, & Creams'}]\n"]}], "source": ["async def s<PERSON><PERSON><PERSON><PERSON><PERSON>():\n", "    async with async_playwright() as playwright:\n", "        # use headless mode since we are using Colab\n", "        browser = await playwright.chromium.launch(headless=True)\n", "        page = await browser.new_page()\n", "\n", "        # make sure to have the correct URL\n", "        await page.goto(\"https://swig-orders.crispnow.com/tabs/locations/menu\")\n", "\n", "        # let page load\n", "        await page.wait_for_selector(\n", "            \"ion-card-content\", state=\"attached\", timeout=60000\n", "        )\n", "\n", "        # ion-card-content has all of our names and descriptions\n", "        items = await page.query_selector_all(\"ion-card-content\")\n", "\n", "        menu = []\n", "\n", "        # loop through the html and take what we need\n", "        for item in items:\n", "            result = {}\n", "\n", "            name = await item.query_selector(\"p.text-h3\")\n", "            description = await item.query_selector(\"p.text-b2\")\n", "\n", "            # just get the inner text\n", "            if name and description:\n", "                result = {}\n", "                result[\"name\"] = await name.inner_text()\n", "                result[\"description\"] = await description.inner_text()\n", "                menu.append(result)\n", "\n", "        for item in menu:\n", "            print(f\"Name: {item['name']}, Description: {item['description']}\")\n", "\n", "        await browser.close()\n", "        return menu\n", "\n", "\n", "scraped_menu = await swigScraper()\n", "\n", "print(scraped_menu)"]}, {"cell_type": "markdown", "metadata": {"id": "XokJcgcStspj"}, "source": ["Now that we have all of our menu options, let's use OpenAI to tell us which drinks are best for fall based on their descriptions."]}, {"cell_type": "markdown", "metadata": {"id": "G_LKwMPsuZuq"}, "source": ["## Step 2: OpenAI Structured Schema Outputs\n", "Please refer to the documentation to understand OpenAI's structured schema outputs. We want to emulate the section where they are extracting structured data from unstructured data.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "WYai0nJWx9V9", "outputId": "64575af7-e654-46e8-e835-232b9327b28e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting openai\n", "  Downloading openai-1.48.0-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai) (1.7.0)\n", "Collecting httpx<1,>=0.23.0 (from openai)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting jiter<1,>=0.4.0 (from openai)\n", "  Downloading jiter-0.5.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from openai) (2.9.2)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai) (4.66.5)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /usr/local/lib/python3.10/dist-packages (from openai) (4.12.2)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (3.10)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (1.2.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai) (2024.8.30)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->openai)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai) (2.23.4)\n", "Downloading openai-1.48.0-py3-none-any.whl (376 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m376.1/376.1 kB\u001b[0m \u001b[31m10.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jiter-0.5.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (318 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m318.9/318.9 kB\u001b[0m \u001b[31m22.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: jiter, h11, httpcore, httpx, openai\n", "Successfully installed h11-0.14.0 httpcore-1.0.5 httpx-0.27.2 jiter-0.5.0 openai-1.48.0\n"]}], "source": ["!pip install openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3JqzlgUssOgA"}, "outputs": [], "source": ["import getpass\n", "import json\n", "\n", "import openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qZ1NBaOMyDob", "outputId": "c0ebed2a-3832-4360-c464-d67f1d646aeb"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Put in OpenAI API Key here··········\n"]}], "source": ["# put in your OpenAI API key here\n", "openai_api_key = getpass.getpass(prompt=\"Put in OpenAI API Key here\")"]}, {"cell_type": "markdown", "metadata": {"id": "EjMFHksjC0vg"}, "source": ["Here we are formatting our menu from when we scraped it, putting everything into a single string for OpenAI to understand, and then creating a prompt helping our model understand what we are hoping to achieve."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6IAtNAE11UhU"}, "outputs": [], "source": ["def swigJoined(scraped_menu):\n", "    drink_list = []\n", "\n", "    # just formatting our menu from above\n", "    for drink in scraped_menu:\n", "        drink_format = f\"{drink['name']}: {drink['description']}]\"\n", "        drink_list.append(drink_format)\n", "\n", "    # put all the drinks into a single string for OpenAI to understand it\n", "    drink_string = \"\\n\".join(drink_list)\n", "\n", "    # we have to tell OpenAI which drinks/combinations are available\n", "    prompt = (\n", "        \"You are the best soda mixologist Utah has ever seen! This is a list of sodas and their descriptions, or ingredients:\\n\"\n", "        f\"{drink_string}\\n\\n Please sort each and every drink provided into spring, summer, fall, or winter seasons based on their ingredients\\n\"\n", "        \"and give me reasonings as to why by stating which ingredients make it best for each season. For example, cinnamon is more fall, but peach\\n\"\n", "        \"is more summer.\"\n", "    )\n", "\n", "    return prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HrMXcEeQ3gbg"}, "outputs": [], "source": ["# generate our prompt using the menu we scraped\n", "my_prompt = swigJoined(scraped_menu)\n", "\n", "openai.api_key = openai_api_key"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MBjRdJiw5eCw"}, "outputs": [], "source": ["# now we are doing our structured call (taken from the documentation)\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4o-2024-08-06\",\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are the best soda mixologist Utah has ever seen!\",\n", "        },\n", "        {\"role\": \"user\", \"content\": my_prompt},\n", "    ],\n", "    response_format={\n", "        \"type\": \"json_schema\",\n", "        \"json_schema\": {\n", "            \"name\": \"drink_response\",\n", "            \"strict\": True,\n", "            \"schema\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"seasonal_drinks\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"drink\": {\"type\": \"string\"},\n", "                                \"reason\": {\"type\": \"string\"},\n", "                            },\n", "                            \"required\": [\"drink\", \"reason\"],\n", "                            \"additionalProperties\": <PERSON><PERSON><PERSON>,\n", "                        },\n", "                    }\n", "                },\n", "                \"required\": [\"seasonal_drinks\"],\n", "                \"additionalProperties\": <PERSON><PERSON><PERSON>,\n", "            },\n", "        },\n", "    },\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "pVYumOO_Cvhh"}, "source": ["Let's check and see our full response and see if it's structured the way we want."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "5MDDnPAH7KTj", "outputId": "c22ac7cc-8f96-4998-a7b8-4c6283063f71"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"id\": \"chatcmpl-ABj64ekfxsyu1n7kY1q73GK3reMFT\",\n", "  \"choices\": [\n", "    {\n", "      \"finish_reason\": \"stop\",\n", "      \"index\": 0,\n", "      \"logprobs\": null,\n", "      \"message\": {\n", "        \"content\": \"{\\\"seasonal_drinks\\\":[{\\\"drink\\\":\\\"Dirty S.O.P: Dr Pepper + Coconut + Peach\\\",\\\"reason\\\":\\\"The inclusion of peach makes this drink more suited for summer, as peach is typically associated with warm weather and summer harvests.\\\"},{\\\"drink\\\":\\\"Dr Spice: Dr Pepper + Cinnamon + Coconut + Cinnamon Stick + Half & Half\\\",\\\"reason\\\":\\\"Cinnamon and cinnamon stick are warm spices typically associated with fall and winter, making this drink best suited for chillier weather.\\\"},{\\\"drink\\\":\\\"Life's a Peach: Dr Pepper + Vanilla + Peach + Half & Half\\\",\\\"reason\\\":\\\"The peach flavor suggests a summer drink, as peach is a classic summer fruit. The vanilla adds a creamy note that works well in warmer temperatures.\\\"},{\\\"drink\\\":\\\"Naughty & Nice: Dr Pepper + English Toffee + Half & Half\\\",\\\"reason\\\":\\\"The English toffee brings a rich, dessert-like quality suitable for winter, when people tend to crave warmer, indulgent flavors.\\\"},{\\\"drink\\\":\\\"Princess Peach: Dr Pepper + Peach + Coconut Cream\\\",\\\"reason\\\":\\\"The tropical flavors of peach and coconut are perfect for summer, evoking a sense of beachside relaxation.\\\"},{\\\"drink\\\":\\\"Raspberry Dream: Dr Pepper + Raspberry Puree + Coconut Cream\\\",\\\"reason\\\":\\\"Raspberry is often considered a summer fruit, making this drink ideal for the warmer months.\\\"},{\\\"drink\\\":\\\"Save Me Jade: Diet Dr Pepper + Sugar Free Vanilla + Sugar Free Coconut\\\",\\\"reason\\\":\\\"Coconut and vanilla make this drink light and refreshing, suited for both spring and summer.\\\"},{\\\"drink\\\":\\\"Spring Fling: Dr Pepper + Vanilla + Strawberry Puree + Coconut Cream\\\",\\\"reason\\\":\\\"Strawberries are typically associated with springtime, making this a fitting beverage for this season.\\\"},{\\\"drink\\\":\\\"Texas Tab: Dr Pepper + Vanilla + Coconut Cream\\\",\\\"reason\\\":\\\"Vanilla and coconut create a refreshing combination suitable for both spring and summer.\\\"},{\\\"drink\\\":\\\"The Heartbreaker: Dr Pepper + Blackberry + Coconut + Half & Half\\\",\\\"reason\\\":\\\"Blackberry is often associated with late summer, aligning this drink with the latter part of the season.\\\"},{\\\"drink\\\":\\\"The Ring King: Dr Pepper + Cupcake + Strawberry Puree + Vanilla Cream\\\",\\\"reason\\\":\\\"Strawberry puree points to a summer association, while the rich cupcake and vanilla flavors also hint at a celebratory summer drink.\\\"},{\\\"drink\\\":\\\"Big Al: Diet Coke + Coconut + Fresh Lime\\\",\\\"reason\\\":\\\"The coconut and lime combination gives a tropical feel, perfect for summer.\\\"},{\\\"drink\\\":\\\"This Bliss: Diet Coke + Cranberry + Fresh Lime\\\",\\\"reason\\\":\\\"Cranberry is typically a winter fruit, making this drink apt for colder months leading into winter.\\\"},{\\\"drink\\\":\\\"The Founder: Diet Coke + Sugar Free Coconut + Fresh Lime + Coconut Cream\\\",\\\"reason\\\":\\\"The coconut ingredients make this drink feel like a summer escape.\\\"},{\\\"drink\\\":\\\"Just Peachy: Coke Zero Sugar + Pineapple + Peach Puree + Fresh Lime + Coconut Cream\\\",\\\"reason\\\":\\\"Peach and pineapple evoke the tropical glow of summer, aligning this drink perfectly with the season.\\\"},{\\\"drink\\\":\\\"Waikiki: Coke + Pineapple + Coconut Cream\\\",\\\"reason\\\":\\\"Named after a famous beach, the pineapple and coconut scream summer vacation.\\\"},{\\\"drink\\\":\\\"Cherry Bomb: Pepsi + Cherry + Coconut + Vanilla Cream\\\",\\\"reason\\\":\\\"Cherries are associated with summer, making this drink ideal for the warmer months.\\\"},{\\\"drink\\\":\\\"Beach Babe: Mountain Dew + Raspberry + Peach + Vanilla Cream\\\",\\\"reason\\\":\\\"Raspberry and peach both hint at summer vibes, complementing the fruity beach theme.\\\"},{\\\"drink\\\":\\\"Bloody Wild: Mountain Dew + Mango Puree + Strawberry Puree\\\",\\\"reason\\\":\\\"Mango and strawberries are summer fruits, providing a refreshing drink fitting for the season.\\\"},{\\\"drink\\\":\\\"Dew Gooder: Mountain Dew + Pineapple + Fresh Lime + Raspberry Puree + Coconut Cream\\\",\\\"reason\\\":\\\"Pineapple and coconut have distinctive tropical, summery connotations.\\\"},{\\\"drink\\\":\\\"Endless Summer: Mountain Dew + Pomegranate + Grapefruit + Fresh Lime\\\",\\\"reason\\\":\\\"Pomegranate can bridge between fall and winter, but grapefruit and lime suggest summer, making this a versatile drink.\\\"},{\\\"drink\\\":\\\"Guava Have It!: Mountain Dew + Guava + Strawberry + Coconut Cream\\\",\\\"reason\\\":\\\"Guava and strawberry, alongside the coconut, strongly suggest a summer cocktail.\\\"},{\\\"drink\\\":\\\"Jolly Elf: Mountain Dew + Passion Fruit + Strawberry Puree + Fresh Orange\\\",\\\"reason\\\":\\\"Despite its name hinting at winter, the passion fruit and strawberry make it suitable for summer.\\\"},{\\\"drink\\\":\\\"The Rocket: Mountain Dew + Raspberry + Coconut + Blackberry + Vanilla Cream\\\",\\\"reason\\\":\\\"The blend of berry and coconut flavors feels summery, despite vanilla's winter flexibilities.\\\"},{\\\"drink\\\":\\\"Watermelon Sugar: Mountain Dew + Mango + Watermelon + Coconut Cream + Passion Fruit Popping Pearls\\\",\\\"reason\\\":\\\"Watermelon is iconic for summer refreshment, paired with mango and passion fruit.\\\"},{\\\"drink\\\":\\\"Hula Girl: Sprite + Lemonade + Mango + Pineapple + Strawberry + Coconut Cream\\\",\\\"reason\\\":\\\"Lemonade, mango, pineapple, and coconut are classic summer refreshers.\\\"},{\\\"drink\\\":\\\"Loop-T-Loop: Sprite + Strawberry + Watermelon + Peach\\\",\\\"reason\\\":\\\"All three fruits - strawberry, watermelon, and peach - firmly cement this drink in summer.\\\"},{\\\"drink\\\":\\\"Poppin' Pineapple: Sprite + Pineapple + Watermelon + Strawberry Popping Pearls\\\",\\\"reason\\\":\\\"Pineapple and watermelon are key summer fruits, with popping pearls adding a fun twist.\\\"},{\\\"drink\\\":\\\"Riptide: Sprite + Cranberry + Raspberry Puree + Fresh Lime\\\",\\\"reason\\\":\\\"Cranberries indicate winter to fall, but lime and raspberry shift it towards summer.\\\"},{\\\"drink\\\":\\\"Shark Attack: Sprite + Lemonade + Blue Raspberry + Gummy Shark\\\",\\\"reason\\\":\\\"The blue raspberry gives a playful summer vibe alongside classic lemonade.\\\"},{\\\"drink\\\":\\\"Unlucky Ducky: Sprite + Lemonade + Strawberry + Gummy Shark\\\",\\\"reason\\\":\\\"Strawberries paired with lemonade invite a refreshing summer image.\\\"},{\\\"drink\\\":\\\"Beach Bum: Lemonade + Guava + Grapefruit + Fresh Orange\\\",\\\"reason\\\":\\\"Citrus and guava's refreshing notes herald sunny, summer days.\\\"},{\\\"drink\\\":\\\"The Tropic: Lemonade + Mango + Passion Fruit + Vanilla Cream\\\",\\\"reason\\\":\\\"Mango and passion fruit scream summer island paradise.\\\"},{\\\"drink\\\":\\\"Buttery Beer: Root Beer + Butterscotch + Vanilla Creme\\\",\\\"reason\\\":\\\"The rich and creamy notes are more typically associated with winter, offering a warming effect.\\\"},{\\\"drink\\\":\\\"Happy Camper: Root Beer + Toasted Marshmallow + Half & Half\\\",\\\"reason\\\":\\\"Toasted marshmallow aligns with fall campfires and cooler weather traditions.\\\"},{\\\"drink\\\":\\\"Cinnamon Cider: Ginger Ale + Lemonade + Cinnamon + Apple + Cinnamon Stick\\\",\\\"reason\\\":\\\"Cinnamon and apple make this a quintessential fall drink, reflecting traditional fall flavors like cider.\\\"},{\\\"drink\\\":\\\"Island Time: Fresca + Passion Fruit + Fresh Orange + Mango Puree + Coconut Cream\\\",\\\"reason\\\":\\\"The tropical fruits signal a summery escape.\\\"},{\\\"drink\\\":\\\"Pink Bahama: Fresca + Peach + Strawberry+ Raspberry + Fresh Lemon\\\",\\\"reason\\\":\\\"The berry and fruity mix with lemon suggest a spring to summer transition.\\\"},{\\\"drink\\\":\\\"Sleigh All Day: Fresca + Lemonade + Pomegranate + Peach + Strawberry Puree + Fresh Lemon\\\",\\\"reason\\\":\\\"Blending pomegranate with peach suggests a winter drink with a bright spring twist.\\\"},{\\\"drink\\\":\\\"Drama Queen: Sugar Free Reviver Energy + Sugar Free Strawberry + Sugar Free Peach + Sugar Free Coconut + Light Lemonade\\\",\\\"reason\\\":\\\"Peach and coconut ingredients indicate a summer preference.\\\"},{\\\"drink\\\":\\\"Mountain Man: Reviver Energy + Blackberry + Coconut + Fresh Lime + Half & Half + Mountain Dew\\\",\\\"reason\\\":\\\"Despite coconut suggesting summer, blackberry can veer toward fall, providing versatility.\\\"},{\\\"drink\\\":\\\"P.O.G.: Reviver Energy + Passion Fruit + Guava + Fresh Orange\\\",\\\"reason\\\":\\\"Passion fruit and guava designate a summer drink, reminiscent of tropical islands.\\\"},{\\\"drink\\\":\\\"Ride or Die: Reviver Energy + Peach + Pineapple + Coconut Cream\\\",\\\"reason\\\":\\\"Peach and pineapple strongly support a summer alignment.\\\"},{\\\"drink\\\":\\\"Super Trooper: Reviver Energy + Lemonade + Mango + Strawberry Puree + Coconut Cream\\\",\\\"reason\\\":\\\"Lemonade, mango, and strawberry are summer favorites, making this seasonally aligned.\\\"},{\\\"drink\\\":\\\"Surf's Up: Reviver Energy + Lemonade + Peach + Mango + Raspberry + Fresh Orange\\\",\\\"reason\\\":\\\"The cocktail of spring/summer fruits places this drink firmly in summer.\\\"},{\\\"drink\\\":\\\"Weekender: Reviver + Strawberry + Coconut + Coconut Cream + Sprite\\\",\\\"reason\\\":\\\"Strawberry and coconut epitomize summer freshness and leisure.\\\"},{\\\"drink\\\":\\\"All Star: Blended Reviver Energy + Lemonade + Guava + Strawberry + Coconut Cream\\\",\\\"reason\\\":\\\"Featuring lemonade, guava, strawberry, and coconut - peak summer.\\\"},{\\\"drink\\\":\\\"Cloud 9: Blended Reviver Energy + Mountain Dew + Coconut + Pineapple + Blue Raspberry + Half & Half\\\",\\\"reason\\\":\\\"Pineapple and coconut make this another summer-themed drink.\\\"},{\\\"drink\\\":\\\"Daisy Duke: Blended Reviver Energy + Mountain Dew + Pineapple + Strawberry Puree + Coconut Cream\\\",\\\"reason\\\":\\\"The tropical pineapple and soothing coconut are made for summer sipping.\\\"},{\\\"drink\\\":\\\"Flirty Flamango: Blended Reviver Energy + Sprite + Grapefruit + Pineapple + Mango Puree + Coconut Cream\\\",\\\"reason\\\":\\\"The balance of pineapple and mango hints strongly towards summer.\\\"},{\\\"drink\\\":\\\"Peaches n Cream: Blended Reviver Energy + Sprite + Peach Puree + Vanilla Cream\\\",\\\"reason\\\":\\\"Peach puree creates a summery vibe, contrasting vanilla's winter warmth.\\\"},{\\\"drink\\\":\\\"Polar Punch: Blended Reviver Energy + Ginger Ale + Blackberry + Strawberry + Raspberry + Vanilla Cream\\\",\\\"reason\\\":\\\"The mix of berries offers flexibility across the latter part of summer and into fall.\\\"},{\\\"drink\\\":\\\"Sucker Punch: Blended Reviver Energy + Lemonade + Grapefruit + Mango\\\",\\\"reason\\\":\\\"The lemonade and mango dual are ideal for summer afternoons.\\\"},{\\\"drink\\\":\\\"Autumn Blush: Water + Apple + Raspberry + Mango Puree + Vanilla Cream\\\",\\\"reason\\\":\\\"Apple and vanilla cream indicate fall, despite raspberry and mango's touches of summer.\\\"},{\\\"drink\\\":\\\"Berry Nice: Water + Sugar Free Strawberry + Fresh Lime\\\",\\\"reason\\\":\\\"Strawberry aligns with late spring into summer due to its refreshing qualities.\\\"},{\\\"drink\\\":\\\"Berry Swigmas: Water + Cranberry + Sugar Free Vanilla + Fresh Orange\\\",\\\"reason\\\":\\\"Cranberry and vanilla both suggest a winter theme, reminiscent of holiday flavors.\\\"},{\\\"drink\\\":\\\"Fruit Water: Water + Sugar Free Coconut + Sugar Free Vanilla + Frozen Strawberry + Frozen Mango\\\",\\\"reason\\\":\\\"Strawberry, mango, and coconut signify summer.\\\"},{\\\"drink\\\":\\\"Mango Breeze: Water + Sugar Free Coconut + Sugar Free Vanilla + Mango Puree + Frozen Mango + Coconut Cream\\\",\\\"reason\\\":\\\"Mango and coconut make it clearly summer oriented.\\\"},{\\\"drink\\\":\\\"Pretty in Pink: Water + Guava + Grapefruit + Fresh Orange\\\",\\\"reason\\\":\\\"The summery fruit mix makes it ideal from late spring into summer.\\\"},{\\\"drink\\\":\\\"Sandy Cheeks: Water + Sugar Free Peach + Fresh Lemon\\\",\\\"reason\\\":\\\"Peach and lemon are bright and suited for spring and summer.\\\"},{\\\"drink\\\":\\\"Strawberry Breeze: Water + Sugar Free Coconut + Sugar Free Vanilla + Strawberry Puree + Frozen Strawberry + Coconut Cream\\\",\\\"reason\\\":\\\"Strawberries and coconut embody summer refreshment.\\\"},{\\\"drink\\\":\\\"Summer Splash: Water + Sugar Free Pineapple + Strawberry Puree + Fresh Lemon\\\",\\\"reason\\\":\\\"Pineapple and strawberry define it as a burst of summer.\\\"},{\\\"drink\\\":\\\"Tiki Tiki: Water + Sugar Free Coconut + Fresh Orange\\\",\\\"reason\\\":\\\"A light, refreshing mix aligned with summer.\\\"},{\\\"drink\\\":\\\"The Fighter: Water + Sugar Free Vanilla + Sugar Free Peach + Sugar Free Pineapple + Fresh Lime + Raspberry Puree\\\",\\\"reason\\\":\\\"Peach, lime, and pineapple aim this towards summer tones.\\\"},{\\\"drink\\\":\\\"Unbreakable: Water + Grapefruit + Mango Puree + Fresh Lemon\\\",\\\"reason\\\":\\\"Mango and lemon throwback to summer days.\\\"}]}\",\n", "        \"refusal\": null,\n", "        \"role\": \"assistant\",\n", "        \"function_call\": null,\n", "        \"tool_calls\": null\n", "      }\n", "    }\n", "  ],\n", "  \"created\": 1727358324,\n", "  \"model\": \"gpt-4o-2024-08-06\",\n", "  \"object\": \"chat.completion\",\n", "  \"service_tier\": null,\n", "  \"system_fingerprint\": \"fp_5050236cbd\",\n", "  \"usage\": {\n", "    \"completion_tokens\": 2473,\n", "    \"prompt_tokens\": 1973,\n", "    \"total_tokens\": 4446,\n", "    \"completion_tokens_details\": {\n", "      \"reasoning_tokens\": 0\n", "    }\n", "  }\n", "}\n"]}], "source": ["# full response\n", "print(json.dumps(response.model_dump(), indent=2))"]}, {"cell_type": "markdown", "metadata": {"id": "ch0f2g2mDAPz"}, "source": ["It is structured nicely, but all our fall drinks with their reasonings are under the \"content\" part. Let's open this up so we can better read it."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_P9u4reu80Yz", "outputId": "38fa647f-f9a1-473e-a6fe-0e246e4ed3cd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"seasonal_drinks\":[{\"drink\":\"Dirty S.O.P: Dr Pepper + Coconut + Peach\",\"reason\":\"The inclusion of peach makes this drink more suited for summer, as peach is typically associated with warm weather and summer harvests.\"},{\"drink\":\"Dr Spice: Dr Pepper + Cinnamon + Coconut + Cinnamon Stick + Half & Half\",\"reason\":\"Cinnamon and cinnamon stick are warm spices typically associated with fall and winter, making this drink best suited for chillier weather.\"},{\"drink\":\"Life's a Peach: Dr Pepper + Vanilla + Peach + Half & Half\",\"reason\":\"The peach flavor suggests a summer drink, as peach is a classic summer fruit. The vanilla adds a creamy note that works well in warmer temperatures.\"},{\"drink\":\"Naughty & Nice: Dr Pepper + English Toffee + Half & Half\",\"reason\":\"The English toffee brings a rich, dessert-like quality suitable for winter, when people tend to crave warmer, indulgent flavors.\"},{\"drink\":\"Princess Peach: Dr Pepper + Peach + Coconut Cream\",\"reason\":\"The tropical flavors of peach and coconut are perfect for summer, evoking a sense of beachside relaxation.\"},{\"drink\":\"Raspberry Dream: Dr Pepper + Raspberry Puree + Coconut Cream\",\"reason\":\"Raspberry is often considered a summer fruit, making this drink ideal for the warmer months.\"},{\"drink\":\"Save Me Jade: Diet Dr Pepper + Sugar Free Vanilla + Sugar Free Coconut\",\"reason\":\"Coconut and vanilla make this drink light and refreshing, suited for both spring and summer.\"},{\"drink\":\"Spring Fling: Dr Pepper + Vanilla + Strawberry Puree + Coconut Cream\",\"reason\":\"Strawberries are typically associated with springtime, making this a fitting beverage for this season.\"},{\"drink\":\"Texas Tab: Dr Pepper + Vanilla + Coconut Cream\",\"reason\":\"Vanilla and coconut create a refreshing combination suitable for both spring and summer.\"},{\"drink\":\"The Heartbreaker: Dr Pepper + Blackberry + Coconut + Half & Half\",\"reason\":\"Blackberry is often associated with late summer, aligning this drink with the latter part of the season.\"},{\"drink\":\"The Ring King: Dr Pepper + Cupcake + Strawberry Puree + Vanilla Cream\",\"reason\":\"Strawberry puree points to a summer association, while the rich cupcake and vanilla flavors also hint at a celebratory summer drink.\"},{\"drink\":\"Big Al: Diet Coke + Coconut + Fresh Lime\",\"reason\":\"The coconut and lime combination gives a tropical feel, perfect for summer.\"},{\"drink\":\"This Bliss: Diet Coke + Cranberry + Fresh Lime\",\"reason\":\"Cranberry is typically a winter fruit, making this drink apt for colder months leading into winter.\"},{\"drink\":\"The Founder: Diet Coke + Sugar Free Coconut + Fresh Lime + Coconut Cream\",\"reason\":\"The coconut ingredients make this drink feel like a summer escape.\"},{\"drink\":\"Just Peachy: Coke Zero Sugar + Pineapple + Peach Puree + Fresh Lime + Coconut Cream\",\"reason\":\"Peach and pineapple evoke the tropical glow of summer, aligning this drink perfectly with the season.\"},{\"drink\":\"Waikiki: Coke + Pineapple + Coconut Cream\",\"reason\":\"Named after a famous beach, the pineapple and coconut scream summer vacation.\"},{\"drink\":\"Cherry Bomb: Pepsi + Cherry + Coconut + Vanilla Cream\",\"reason\":\"Cherries are associated with summer, making this drink ideal for the warmer months.\"},{\"drink\":\"Beach Babe: Mountain Dew + Raspberry + Peach + Vanilla Cream\",\"reason\":\"Raspberry and peach both hint at summer vibes, complementing the fruity beach theme.\"},{\"drink\":\"Bloody Wild: Mountain Dew + Mango Puree + Strawberry Puree\",\"reason\":\"Mango and strawberries are summer fruits, providing a refreshing drink fitting for the season.\"},{\"drink\":\"Dew Gooder: Mountain Dew + Pineapple + Fresh Lime + Raspberry Puree + Coconut Cream\",\"reason\":\"Pineapple and coconut have distinctive tropical, summery connotations.\"},{\"drink\":\"Endless Summer: Mountain Dew + Pomegranate + Grapefruit + Fresh Lime\",\"reason\":\"Pomegranate can bridge between fall and winter, but grapefruit and lime suggest summer, making this a versatile drink.\"},{\"drink\":\"Guava Have It!: Mountain Dew + Guava + Strawberry + Coconut Cream\",\"reason\":\"Guava and strawberry, alongside the coconut, strongly suggest a summer cocktail.\"},{\"drink\":\"Jolly Elf: Mountain Dew + Passion Fruit + Strawberry Puree + Fresh Orange\",\"reason\":\"Despite its name hinting at winter, the passion fruit and strawberry make it suitable for summer.\"},{\"drink\":\"The Rocket: Mountain Dew + Raspberry + Coconut + Blackberry + Vanilla Cream\",\"reason\":\"The blend of berry and coconut flavors feels summery, despite vanilla's winter flexibilities.\"},{\"drink\":\"Watermelon Sugar: Mountain Dew + Mango + Watermelon + Coconut Cream + Passion Fruit Popping Pearls\",\"reason\":\"Watermelon is iconic for summer refreshment, paired with mango and passion fruit.\"},{\"drink\":\"Hula Girl: Sprite + Lemonade + Mango + Pineapple + Strawberry + Coconut Cream\",\"reason\":\"Lemonade, mango, pineapple, and coconut are classic summer refreshers.\"},{\"drink\":\"Loop-T-Loop: Sprite + Strawberry + Watermelon + Peach\",\"reason\":\"All three fruits - strawberry, watermelon, and peach - firmly cement this drink in summer.\"},{\"drink\":\"Poppin' Pineapple: Sprite + Pineapple + Watermelon + Strawberry Popping Pearls\",\"reason\":\"Pineapple and watermelon are key summer fruits, with popping pearls adding a fun twist.\"},{\"drink\":\"Riptide: Sprite + Cranberry + Raspberry Puree + Fresh Lime\",\"reason\":\"Cranberries indicate winter to fall, but lime and raspberry shift it towards summer.\"},{\"drink\":\"Shark Attack: Sprite + Lemonade + Blue Raspberry + Gummy Shark\",\"reason\":\"The blue raspberry gives a playful summer vibe alongside classic lemonade.\"},{\"drink\":\"Unlucky Ducky: Sprite + Lemonade + Strawberry + Gummy Shark\",\"reason\":\"Strawberries paired with lemonade invite a refreshing summer image.\"},{\"drink\":\"Beach Bum: Lemonade + Guava + Grapefruit + Fresh Orange\",\"reason\":\"Citrus and guava's refreshing notes herald sunny, summer days.\"},{\"drink\":\"The Tropic: Lemonade + Mango + Passion Fruit + Vanilla Cream\",\"reason\":\"Mango and passion fruit scream summer island paradise.\"},{\"drink\":\"Buttery Beer: Root Beer + Butterscotch + Vanilla Creme\",\"reason\":\"The rich and creamy notes are more typically associated with winter, offering a warming effect.\"},{\"drink\":\"Happy Camper: Root Beer + Toasted Marshmallow + Half & Half\",\"reason\":\"Toasted marshmallow aligns with fall campfires and cooler weather traditions.\"},{\"drink\":\"Cinnamon Cider: Ginger Ale + Lemonade + Cinnamon + Apple + Cinnamon Stick\",\"reason\":\"Cinnamon and apple make this a quintessential fall drink, reflecting traditional fall flavors like cider.\"},{\"drink\":\"Island Time: Fresca + Passion Fruit + Fresh Orange + Mango Puree + Coconut Cream\",\"reason\":\"The tropical fruits signal a summery escape.\"},{\"drink\":\"Pink Bahama: Fresca + Peach + Strawberry+ Raspberry + Fresh Lemon\",\"reason\":\"The berry and fruity mix with lemon suggest a spring to summer transition.\"},{\"drink\":\"Sleigh All Day: Fresca + Lemonade + Pomegranate + Peach + Strawberry Puree + Fresh Lemon\",\"reason\":\"Blending pomegranate with peach suggests a winter drink with a bright spring twist.\"},{\"drink\":\"Drama Queen: Sugar Free Reviver Energy + Sugar Free Strawberry + Sugar Free Peach + Sugar Free Coconut + Light Lemonade\",\"reason\":\"Peach and coconut ingredients indicate a summer preference.\"},{\"drink\":\"Mountain Man: Reviver Energy + Blackberry + Coconut + Fresh Lime + Half & Half + Mountain Dew\",\"reason\":\"Despite coconut suggesting summer, blackberry can veer toward fall, providing versatility.\"},{\"drink\":\"P.O.G.: Reviver Energy + Passion Fruit + Guava + Fresh Orange\",\"reason\":\"Passion fruit and guava designate a summer drink, reminiscent of tropical islands.\"},{\"drink\":\"Ride or Die: Reviver Energy + Peach + Pineapple + Coconut Cream\",\"reason\":\"Peach and pineapple strongly support a summer alignment.\"},{\"drink\":\"Super Trooper: Reviver Energy + Lemonade + Mango + Strawberry Puree + Coconut Cream\",\"reason\":\"Lemonade, mango, and strawberry are summer favorites, making this seasonally aligned.\"},{\"drink\":\"Surf's Up: Reviver Energy + Lemonade + Peach + Mango + Raspberry + Fresh Orange\",\"reason\":\"The cocktail of spring/summer fruits places this drink firmly in summer.\"},{\"drink\":\"Weekender: Reviver + Strawberry + Coconut + Coconut Cream + Sprite\",\"reason\":\"Strawberry and coconut epitomize summer freshness and leisure.\"},{\"drink\":\"All Star: Blended Reviver Energy + Lemonade + Guava + Strawberry + Coconut Cream\",\"reason\":\"Featuring lemonade, guava, strawberry, and coconut - peak summer.\"},{\"drink\":\"Cloud 9: Blended Reviver Energy + Mountain Dew + Coconut + Pineapple + Blue Raspberry + Half & Half\",\"reason\":\"Pineapple and coconut make this another summer-themed drink.\"},{\"drink\":\"Daisy Duke: Blended Reviver Energy + Mountain Dew + Pineapple + Strawberry Puree + Coconut Cream\",\"reason\":\"The tropical pineapple and soothing coconut are made for summer sipping.\"},{\"drink\":\"Flirty Flamango: Blended Reviver Energy + Sprite + Grapefruit + Pineapple + Mango Puree + Coconut Cream\",\"reason\":\"The balance of pineapple and mango hints strongly towards summer.\"},{\"drink\":\"Peaches n Cream: Blended Reviver Energy + Sprite + Peach Puree + Vanilla Cream\",\"reason\":\"Peach puree creates a summery vibe, contrasting vanilla's winter warmth.\"},{\"drink\":\"Polar Punch: Blended Reviver Energy + Ginger Ale + Blackberry + Strawberry + Raspberry + Vanilla Cream\",\"reason\":\"The mix of berries offers flexibility across the latter part of summer and into fall.\"},{\"drink\":\"Sucker Punch: Blended Reviver Energy + Lemonade + Grapefruit + Mango\",\"reason\":\"The lemonade and mango dual are ideal for summer afternoons.\"},{\"drink\":\"Autumn Blush: Water + Apple + Raspberry + Mango Puree + Vanilla Cream\",\"reason\":\"Apple and vanilla cream indicate fall, despite raspberry and mango's touches of summer.\"},{\"drink\":\"Berry Nice: Water + Sugar Free Strawberry + Fresh Lime\",\"reason\":\"Strawberry aligns with late spring into summer due to its refreshing qualities.\"},{\"drink\":\"Berry Swigmas: Water + Cranberry + Sugar Free Vanilla + Fresh Orange\",\"reason\":\"Cranberry and vanilla both suggest a winter theme, reminiscent of holiday flavors.\"},{\"drink\":\"Fruit Water: Water + Sugar Free Coconut + Sugar Free Vanilla + Frozen Strawberry + Frozen Mango\",\"reason\":\"Strawberry, mango, and coconut signify summer.\"},{\"drink\":\"Mango Breeze: Water + Sugar Free Coconut + Sugar Free Vanilla + Mango Puree + Frozen Mango + Coconut Cream\",\"reason\":\"Mango and coconut make it clearly summer oriented.\"},{\"drink\":\"Pretty in Pink: Water + Guava + Grapefruit + Fresh Orange\",\"reason\":\"The summery fruit mix makes it ideal from late spring into summer.\"},{\"drink\":\"Sandy Cheeks: Water + Sugar Free Peach + Fresh Lemon\",\"reason\":\"Peach and lemon are bright and suited for spring and summer.\"},{\"drink\":\"Strawberry Breeze: Water + Sugar Free Coconut + Sugar Free Vanilla + Strawberry Puree + Frozen Strawberry + Coconut Cream\",\"reason\":\"Strawberries and coconut embody summer refreshment.\"},{\"drink\":\"Summer Splash: Water + Sugar Free Pineapple + Strawberry Puree + Fresh Lemon\",\"reason\":\"Pineapple and strawberry define it as a burst of summer.\"},{\"drink\":\"Tiki Tiki: Water + Sugar Free Coconut + Fresh Orange\",\"reason\":\"A light, refreshing mix aligned with summer.\"},{\"drink\":\"The Fighter: Water + Sugar Free Vanilla + Sugar Free Peach + Sugar Free Pineapple + Fresh Lime + Raspberry Puree\",\"reason\":\"Peach, lime, and pineapple aim this towards summer tones.\"},{\"drink\":\"Unbreakable: Water + Grapefruit + Mango Puree + Fresh Lemon\",\"reason\":\"Mango and lemon throwback to summer days.\"}]}\n"]}], "source": ["# content only response\n", "content = response.model_dump()[\"choices\"][0][\"message\"][\"content\"]\n", "print(content)"]}, {"cell_type": "markdown", "metadata": {"id": "H75NQDUdDKJW"}, "source": ["So it's still in one line. Let's print them out nicely for better readability and so when we input it into MongoDB Atlas everything is in different documents."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "7crh8By09s3m", "outputId": "f350c6b7-9c3d-4bfc-a7cd-5988b902bd67"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"drink\": \"Dirty S.O.P: Dr Pepper + Coconut + Peach\",\n", "    \"reason\": \"The inclusion of peach makes this drink more suited for summer, as peach is typically associated with warm weather and summer harvests.\"\n", "  },\n", "  {\n", "    \"drink\": \"Dr Spice: Dr Pepper + Cinnamon + Coconut + Cinnamon Stick + Half & Half\",\n", "    \"reason\": \"Cinnamon and cinnamon stick are warm spices typically associated with fall and winter, making this drink best suited for chillier weather.\"\n", "  },\n", "  {\n", "    \"drink\": \"Life's a Peach: Dr Pepper + Vanilla + Peach + Half & Half\",\n", "    \"reason\": \"The peach flavor suggests a summer drink, as peach is a classic summer fruit. The vanilla adds a creamy note that works well in warmer temperatures.\"\n", "  },\n", "  {\n", "    \"drink\": \"Naughty & Nice: Dr Pepper + English Toffee + Half & Half\",\n", "    \"reason\": \"The English toffee brings a rich, dessert-like quality suitable for winter, when people tend to crave warmer, indulgent flavors.\"\n", "  },\n", "  {\n", "    \"drink\": \"Princess Peach: Dr Pepper + Peach + Coconut Cream\",\n", "    \"reason\": \"The tropical flavors of peach and coconut are perfect for summer, evoking a sense of beachside relaxation.\"\n", "  },\n", "  {\n", "    \"drink\": \"Raspberry Dream: Dr Pepper + Raspberry Puree + Coconut Cream\",\n", "    \"reason\": \"Raspberry is often considered a summer fruit, making this drink ideal for the warmer months.\"\n", "  },\n", "  {\n", "    \"drink\": \"Save Me Jade: Diet Dr Pepper + Sugar Free Vanilla + Sugar Free Coconut\",\n", "    \"reason\": \"Coconut and vanilla make this drink light and refreshing, suited for both spring and summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Spring Fling: Dr Pepper + Vanilla + Strawberry Puree + Coconut Cream\",\n", "    \"reason\": \"Strawberries are typically associated with springtime, making this a fitting beverage for this season.\"\n", "  },\n", "  {\n", "    \"drink\": \"Texas Tab: Dr Pepper + Vanilla + Coconut Cream\",\n", "    \"reason\": \"Vanilla and coconut create a refreshing combination suitable for both spring and summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"The Heartbreaker: Dr Pepper + Blackberry + Coconut + Half & Half\",\n", "    \"reason\": \"Blackberry is often associated with late summer, aligning this drink with the latter part of the season.\"\n", "  },\n", "  {\n", "    \"drink\": \"The Ring King: Dr Pepper + Cupcake + Strawberry Puree + Vanilla Cream\",\n", "    \"reason\": \"Strawberry puree points to a summer association, while the rich cupcake and vanilla flavors also hint at a celebratory summer drink.\"\n", "  },\n", "  {\n", "    \"drink\": \"Big Al: Diet Coke + Coconut + Fresh Lime\",\n", "    \"reason\": \"The coconut and lime combination gives a tropical feel, perfect for summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"This Bliss: Diet Coke + Cranberry + Fresh Lime\",\n", "    \"reason\": \"Cranberry is typically a winter fruit, making this drink apt for colder months leading into winter.\"\n", "  },\n", "  {\n", "    \"drink\": \"The Founder: Diet Coke + Sugar Free Coconut + Fresh Lime + Coconut Cream\",\n", "    \"reason\": \"The coconut ingredients make this drink feel like a summer escape.\"\n", "  },\n", "  {\n", "    \"drink\": \"Just Peachy: Coke Zero Sugar + Pineapple + Peach Puree + Fresh Lime + Coconut Cream\",\n", "    \"reason\": \"Peach and pineapple evoke the tropical glow of summer, aligning this drink perfectly with the season.\"\n", "  },\n", "  {\n", "    \"drink\": \"Waikiki: Coke + Pineapple + Coconut Cream\",\n", "    \"reason\": \"Named after a famous beach, the pineapple and coconut scream summer vacation.\"\n", "  },\n", "  {\n", "    \"drink\": \"Cherry Bomb: Pepsi + Cherry + Coconut + Vanilla Cream\",\n", "    \"reason\": \"Cherries are associated with summer, making this drink ideal for the warmer months.\"\n", "  },\n", "  {\n", "    \"drink\": \"Beach Babe: Mountain Dew + Raspberry + Peach + Vanilla Cream\",\n", "    \"reason\": \"Raspberry and peach both hint at summer vibes, complementing the fruity beach theme.\"\n", "  },\n", "  {\n", "    \"drink\": \"Bloody Wild: Mountain Dew + Mango Puree + Strawberry Puree\",\n", "    \"reason\": \"Mango and strawberries are summer fruits, providing a refreshing drink fitting for the season.\"\n", "  },\n", "  {\n", "    \"drink\": \"Dew Gooder: Mountain Dew + Pineapple + Fresh Lime + Raspberry Puree + Coconut Cream\",\n", "    \"reason\": \"Pineapple and coconut have distinctive tropical, summery connotations.\"\n", "  },\n", "  {\n", "    \"drink\": \"Endless Summer: Mountain Dew + Pomegranate + Grapefruit + Fresh Lime\",\n", "    \"reason\": \"Pomegranate can bridge between fall and winter, but grapefruit and lime suggest summer, making this a versatile drink.\"\n", "  },\n", "  {\n", "    \"drink\": \"Guava Have It!: Mountain Dew + Guava + Strawberry + Coconut Cream\",\n", "    \"reason\": \"Guava and strawberry, alongside the coconut, strongly suggest a summer cocktail.\"\n", "  },\n", "  {\n", "    \"drink\": \"Jolly Elf: Mountain Dew + Passion Fruit + Strawberry Puree + Fresh Orange\",\n", "    \"reason\": \"Despite its name hinting at winter, the passion fruit and strawberry make it suitable for summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"The Rocket: Mountain Dew + Raspberry + Coconut + Blackberry + Vanilla Cream\",\n", "    \"reason\": \"The blend of berry and coconut flavors feels summery, despite vanilla's winter flexibilities.\"\n", "  },\n", "  {\n", "    \"drink\": \"Watermelon Sugar: Mountain Dew + Mango + Watermelon + Coconut Cream + Passion Fruit Popping Pearls\",\n", "    \"reason\": \"Watermelon is iconic for summer refreshment, paired with mango and passion fruit.\"\n", "  },\n", "  {\n", "    \"drink\": \"Hula Girl: Sprite + Lemonade + Mango + Pineapple + Strawberry + Coconut Cream\",\n", "    \"reason\": \"Lemonade, mango, pineapple, and coconut are classic summer refreshers.\"\n", "  },\n", "  {\n", "    \"drink\": \"Loop-T-Loop: Sprite + Strawberry + Watermelon + Peach\",\n", "    \"reason\": \"All three fruits - strawberry, watermelon, and peach - firmly cement this drink in summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Poppin' Pineapple: Sprite + Pineapple + Watermelon + Strawberry Popping Pearls\",\n", "    \"reason\": \"Pineapple and watermelon are key summer fruits, with popping pearls adding a fun twist.\"\n", "  },\n", "  {\n", "    \"drink\": \"Riptide: Sprite + Cranberry + Raspberry Puree + Fresh Lime\",\n", "    \"reason\": \"Cranberries indicate winter to fall, but lime and raspberry shift it towards summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Shark Attack: Sprite + Lemonade + Blue Raspberry + Gummy Shark\",\n", "    \"reason\": \"The blue raspberry gives a playful summer vibe alongside classic lemonade.\"\n", "  },\n", "  {\n", "    \"drink\": \"Unlucky Ducky: Sprite + Lemonade + Strawberry + Gummy Shark\",\n", "    \"reason\": \"Strawberries paired with lemonade invite a refreshing summer image.\"\n", "  },\n", "  {\n", "    \"drink\": \"Beach Bum: Lemonade + Guava + Grapefruit + Fresh Orange\",\n", "    \"reason\": \"Citrus and guava's refreshing notes herald sunny, summer days.\"\n", "  },\n", "  {\n", "    \"drink\": \"The Tropic: Lemonade + Mango + Passion Fruit + Vanilla Cream\",\n", "    \"reason\": \"Mango and passion fruit scream summer island paradise.\"\n", "  },\n", "  {\n", "    \"drink\": \"Buttery Beer: Root Beer + Butterscotch + Vanilla Creme\",\n", "    \"reason\": \"The rich and creamy notes are more typically associated with winter, offering a warming effect.\"\n", "  },\n", "  {\n", "    \"drink\": \"Happy Camper: Root Beer + Toasted Marshmallow + Half & Half\",\n", "    \"reason\": \"Toasted marshmallow aligns with fall campfires and cooler weather traditions.\"\n", "  },\n", "  {\n", "    \"drink\": \"Cinnamon Cider: Ginger Ale + Lemonade + Cinnamon + Apple + Cinnamon Stick\",\n", "    \"reason\": \"Cinnamon and apple make this a quintessential fall drink, reflecting traditional fall flavors like cider.\"\n", "  },\n", "  {\n", "    \"drink\": \"Island Time: Fresca + Passion Fruit + Fresh Orange + Mango Puree + Coconut Cream\",\n", "    \"reason\": \"The tropical fruits signal a summery escape.\"\n", "  },\n", "  {\n", "    \"drink\": \"Pink Bahama: Fresca + Peach + Strawberry+ Raspberry + Fresh Lemon\",\n", "    \"reason\": \"The berry and fruity mix with lemon suggest a spring to summer transition.\"\n", "  },\n", "  {\n", "    \"drink\": \"Sleigh All Day: Fresca + Lemonade + Pomegranate + Peach + Strawberry Puree + Fresh Lemon\",\n", "    \"reason\": \"Blending pomegranate with peach suggests a winter drink with a bright spring twist.\"\n", "  },\n", "  {\n", "    \"drink\": \"Drama Queen: Sugar Free Reviver Energy + Sugar Free Strawberry + Sugar Free Peach + Sugar Free Coconut + Light Lemonade\",\n", "    \"reason\": \"Peach and coconut ingredients indicate a summer preference.\"\n", "  },\n", "  {\n", "    \"drink\": \"Mountain Man: Reviver Energy + Blackberry + Coconut + Fresh Lime + Half & Half + Mountain Dew\",\n", "    \"reason\": \"Despite coconut suggesting summer, blackberry can veer toward fall, providing versatility.\"\n", "  },\n", "  {\n", "    \"drink\": \"P.O.G.: Reviver Energy + Passion Fruit + Guava + Fresh Orange\",\n", "    \"reason\": \"Passion fruit and guava designate a summer drink, reminiscent of tropical islands.\"\n", "  },\n", "  {\n", "    \"drink\": \"Ride or Die: Reviver Energy + Peach + Pineapple + Coconut Cream\",\n", "    \"reason\": \"Peach and pineapple strongly support a summer alignment.\"\n", "  },\n", "  {\n", "    \"drink\": \"Super Trooper: Reviver Energy + Lemonade + Mango + Strawberry Puree + Coconut Cream\",\n", "    \"reason\": \"Lemonade, mango, and strawberry are summer favorites, making this seasonally aligned.\"\n", "  },\n", "  {\n", "    \"drink\": \"Surf's Up: Reviver Energy + Lemonade + Peach + Mango + Raspberry + Fresh Orange\",\n", "    \"reason\": \"The cocktail of spring/summer fruits places this drink firmly in summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Weekender: Reviver + Strawberry + Coconut + Coconut Cream + Sprite\",\n", "    \"reason\": \"Strawberry and coconut epitomize summer freshness and leisure.\"\n", "  },\n", "  {\n", "    \"drink\": \"All Star: Blended Reviver Energy + Lemonade + Guava + Strawberry + Coconut Cream\",\n", "    \"reason\": \"Featuring lemonade, guava, strawberry, and coconut - peak summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Cloud 9: Blended Reviver Energy + Mountain Dew + Coconut + Pineapple + Blue Raspberry + Half & Half\",\n", "    \"reason\": \"Pineapple and coconut make this another summer-themed drink.\"\n", "  },\n", "  {\n", "    \"drink\": \"Daisy Duke: Blended Reviver Energy + Mountain Dew + Pineapple + Strawberry Puree + Coconut Cream\",\n", "    \"reason\": \"The tropical pineapple and soothing coconut are made for summer sipping.\"\n", "  },\n", "  {\n", "    \"drink\": \"Flirty Flamango: Blended Reviver Energy + Sprite + Grapefruit + Pineapple + Mango Puree + Coconut Cream\",\n", "    \"reason\": \"The balance of pineapple and mango hints strongly towards summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Peaches n Cream: Blended Reviver Energy + Sprite + Peach Puree + Vanilla Cream\",\n", "    \"reason\": \"Peach puree creates a summery vibe, contrasting vanilla's winter warmth.\"\n", "  },\n", "  {\n", "    \"drink\": \"Polar Punch: Blended Reviver Energy + Ginger Ale + Blackberry + Strawberry + Raspberry + Vanilla Cream\",\n", "    \"reason\": \"The mix of berries offers flexibility across the latter part of summer and into fall.\"\n", "  },\n", "  {\n", "    \"drink\": \"Sucker Punch: Blended Reviver Energy + Lemonade + Grapefruit + Mango\",\n", "    \"reason\": \"The lemonade and mango dual are ideal for summer afternoons.\"\n", "  },\n", "  {\n", "    \"drink\": \"Autumn Blush: Water + Apple + Raspberry + Mango Puree + Vanilla Cream\",\n", "    \"reason\": \"Apple and vanilla cream indicate fall, despite raspberry and mango's touches of summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Berry Nice: Water + Sugar Free Strawberry + Fresh Lime\",\n", "    \"reason\": \"Strawberry aligns with late spring into summer due to its refreshing qualities.\"\n", "  },\n", "  {\n", "    \"drink\": \"Berry Swigmas: Water + Cranberry + Sugar Free Vanilla + Fresh Orange\",\n", "    \"reason\": \"Cranberry and vanilla both suggest a winter theme, reminiscent of holiday flavors.\"\n", "  },\n", "  {\n", "    \"drink\": \"Fruit Water: Water + Sugar Free Coconut + Sugar Free Vanilla + Frozen Strawberry + Frozen Mango\",\n", "    \"reason\": \"Strawberry, mango, and coconut signify summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Mango Breeze: Water + Sugar Free Coconut + Sugar Free Vanilla + Mango Puree + Frozen Mango + Coconut Cream\",\n", "    \"reason\": \"Mango and coconut make it clearly summer oriented.\"\n", "  },\n", "  {\n", "    \"drink\": \"Pretty in Pink: Water + Guava + Grapefruit + Fresh Orange\",\n", "    \"reason\": \"The summery fruit mix makes it ideal from late spring into summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Sandy Cheeks: Water + Sugar Free Peach + Fresh Lemon\",\n", "    \"reason\": \"Peach and lemon are bright and suited for spring and summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Strawberry Breeze: Water + Sugar Free Coconut + Sugar Free Vanilla + Strawberry Puree + Frozen Strawberry + Coconut Cream\",\n", "    \"reason\": \"Strawberries and coconut embody summer refreshment.\"\n", "  },\n", "  {\n", "    \"drink\": \"Summer Splash: Water + Sugar Free Pineapple + Strawberry Puree + Fresh Lemon\",\n", "    \"reason\": \"Pineapple and strawberry define it as a burst of summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"Tiki Tiki: Water + Sugar Free Coconut + Fresh Orange\",\n", "    \"reason\": \"A light, refreshing mix aligned with summer.\"\n", "  },\n", "  {\n", "    \"drink\": \"The Fighter: Water + Sugar Free Vanilla + Sugar Free Peach + Sugar Free Pineapple + Fresh Lime + Raspberry Puree\",\n", "    \"reason\": \"Peach, lime, and pineapple aim this towards summer tones.\"\n", "  },\n", "  {\n", "    \"drink\": \"Unbreakable: Water + Grapefruit + Mango Puree + Fresh Lemon\",\n", "    \"reason\": \"Mango and lemon throwback to summer days.\"\n", "  }\n", "]\n"]}], "source": ["# print the drinks out nicely for Atlas\n", "parsed_drinks = json.loads(content)\n", "seasonal_drinks_pretty = parsed_drinks[\"seasonal_drinks\"]\n", "print(json.dumps(seasonal_drinks_pretty, indent=2))"]}, {"cell_type": "markdown", "metadata": {"id": "rp_xZtC4DSXp"}, "source": ["Now that our drinks with their reasonings are printed out nicely, let's upload them into MongoDB Atlas so we can use Atlas Search and take a look at drinks based off their ingredients!"]}, {"cell_type": "markdown", "metadata": {"id": "uObYzbmu_Mjd"}, "source": ["## Step 3: Store into MongoDB and use Atlas Search"]}, {"cell_type": "markdown", "metadata": {"id": "yi2oIS7kDmzA"}, "source": ["For this section a MongoDB Atlas cluster is required. Please make sure you have your connection string saved somewhere safe."]}, {"cell_type": "markdown", "metadata": {"id": "FxcZWmcUDb69"}, "source": ["First install PyMongo to make things easier for ourselves."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tDKxFSnZ_MX6", "outputId": "2799ad4f-7ee4-4ea3-f00f-c574eaa6db08"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pym<PERSON>o\n", "  Downloading pymongo-4.9.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (22 kB)\n", "Collecting dnspython<3.0.0,>=1.16.0 (from pymongo)\n", "  Downloading dnspython-2.6.1-py3-none-any.whl.metadata (5.8 kB)\n", "Downloading pymongo-4.9.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m7.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dnspython-2.6.1-py3-none-any.whl (307 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m24.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: dnspython, pymongo\n", "Successfully installed dnspython-2.6.1 pymongo-4.9.1\n"]}], "source": ["!pip install pymongo"]}, {"cell_type": "markdown", "metadata": {"id": "9zyVsZxLDfoX"}, "source": ["Set up our MongoDB connection, name your database and collection, and insert your documents into your cluster."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "l5crq0gzB7pF", "outputId": "8601422d-63cf-4cc5-8615-d4668482d8a2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter connection string WITH USER + PASS here··········\n"]}, {"data": {"text/plain": ["Insert<PERSON><PERSON><PERSON><PERSON><PERSON>([ObjectId('66f567a76d78f892e158abed'), ObjectId('66f567a76d78f892e158abee'), ObjectId('66f567a76d78f892e158abef'), ObjectId('66f567a76d78f892e158abf0'), ObjectId('66f567a76d78f892e158abf1'), ObjectId('66f567a76d78f892e158abf2'), ObjectId('66f567a76d78f892e158abf3'), ObjectId('66f567a76d78f892e158abf4'), ObjectId('66f567a76d78f892e158abf5'), ObjectId('66f567a76d78f892e158abf6'), ObjectId('66f567a76d78f892e158abf7'), ObjectId('66f567a76d78f892e158abf8'), ObjectId('66f567a76d78f892e158abf9'), ObjectId('66f567a76d78f892e158abfa'), ObjectId('66f567a76d78f892e158abfb'), ObjectId('66f567a76d78f892e158abfc'), ObjectId('66f567a76d78f892e158abfd'), ObjectId('66f567a76d78f892e158abfe'), ObjectId('66f567a76d78f892e158abff'), ObjectId('66f567a76d78f892e158ac00'), ObjectId('66f567a76d78f892e158ac01'), ObjectId('66f567a76d78f892e158ac02'), ObjectId('66f567a76d78f892e158ac03'), ObjectId('66f567a76d78f892e158ac04'), ObjectId('66f567a76d78f892e158ac05'), ObjectId('66f567a76d78f892e158ac06'), ObjectId('66f567a76d78f892e158ac07'), ObjectId('66f567a76d78f892e158ac08'), ObjectId('66f567a76d78f892e158ac09'), ObjectId('66f567a76d78f892e158ac0a'), ObjectId('66f567a76d78f892e158ac0b'), ObjectId('66f567a76d78f892e158ac0c'), ObjectId('66f567a76d78f892e158ac0d'), ObjectId('66f567a76d78f892e158ac0e'), ObjectId('66f567a76d78f892e158ac0f'), ObjectId('66f567a76d78f892e158ac10'), ObjectId('66f567a76d78f892e158ac11'), ObjectId('66f567a76d78f892e158ac12'), ObjectId('66f567a76d78f892e158ac13'), ObjectId('66f567a76d78f892e158ac14'), ObjectId('66f567a76d78f892e158ac15'), ObjectId('66f567a76d78f892e158ac16'), ObjectId('66f567a76d78f892e158ac17'), ObjectId('66f567a76d78f892e158ac18'), ObjectId('66f567a76d78f892e158ac19'), ObjectId('66f567a76d78f892e158ac1a'), ObjectId('66f567a76d78f892e158ac1b'), ObjectId('66f567a76d78f892e158ac1c'), ObjectId('66f567a76d78f892e158ac1d'), ObjectId('66f567a76d78f892e158ac1e'), ObjectId('66f567a76d78f892e158ac1f'), ObjectId('66f567a76d78f892e158ac20'), ObjectId('66f567a76d78f892e158ac21'), ObjectId('66f567a76d78f892e158ac22'), ObjectId('66f567a76d78f892e158ac23'), ObjectId('66f567a76d78f892e158ac24'), ObjectId('66f567a76d78f892e158ac25'), ObjectId('66f567a76d78f892e158ac26'), ObjectId('66f567a76d78f892e158ac27'), ObjectId('66f567a76d78f892e158ac28'), ObjectId('66f567a76d78f892e158ac29'), ObjectId('66f567a76d78f892e158ac2a'), ObjectId('66f567a76d78f892e158ac2b'), ObjectId('66f567a76d78f892e158ac2c'), ObjectId('66f567a76d78f892e158ac2d')], acknowledged=True)"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["from pymongo import MongoClient\n", "\n", "# set up your MongoDB connection\n", "connection_string = getpass.getpass(\n", "    prompt=\"Enter connection string WITH USER + PASS here\"\n", ")\n", "client = MongoClient(connection_string, appname=\"devrel.showcase.swig_menu\")\n", "\n", "\n", "# name your database and collection anything you want since it will be created when you enter your data\n", "database = client[\"swig_menu\"]\n", "collection = database[\"seasonal_drinks\"]\n", "\n", "# insert our fall drinks\n", "collection.insert_many(seasonal_drinks_pretty)"]}, {"cell_type": "markdown", "metadata": {"id": "wFUitWvnFb4Z"}, "source": ["Create an Atlas Search index on your collection\n", "and create an aggregation pipeline. We are using the operator $search.\n", "\n", "Do NOT run this part in your notebook. This is done in the Atlas UI.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "g2oVyQ2KB9Ot"}, "source": ["This finds drinks that have \"fall\" in them"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-mHI8TztJIIt"}, "outputs": [], "source": ["{\"text\": {\"query\": \"fall\", \"path\": \"reason\"}}"]}, {"cell_type": "markdown", "metadata": {"id": "cInKS2XFB5mq"}, "source": ["This finds drinks that are fall AND have apple as an ingredient"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eVONuCpYB0hJ"}, "outputs": [], "source": ["{\n", "    \"compound\": {\n", "        \"must\": [\n", "            {\"text\": {\"query\": \"fall\", \"path\": \"reason\"}},\n", "            {\"text\": {\"query\": \"apple\", \"path\": \"reason\"}},\n", "        ],\n", "    }\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "K4uvA4-gJKGA"}, "source": ["Now you can find drinks that are fall themed that are specific to any ingredients you want!"]}], "metadata": {"colab": {"authorship_tag": "ABX9TyP4TSv2S0hFJAzLA+JYTOpx", "include_colab_link": true, "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 0}