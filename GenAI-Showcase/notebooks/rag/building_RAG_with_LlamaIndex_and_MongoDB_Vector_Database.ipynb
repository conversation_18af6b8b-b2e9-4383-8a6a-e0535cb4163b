{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/building_RAG_with_LlamaIndex_and_MongoDB_Vector_Database.ipynb) \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jwCBOcXw_nBh"}, "outputs": [], "source": ["!pip install llama-index\n", "!pip install llama-index-vector-stores-mongodb\n", "!pip install llama-index-embeddings-openai\n", "!pip install pymongo\n", "!pip install datasets\n", "!pip install pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QVKJl4Yn_4Nj"}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk...\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1MWkFKGy__ut"}, "outputs": [], "source": ["import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# Make sure you have an Hugging Face token(HF_TOKEN) in your development environemnt before running the code below\n", "# How to get a token: https://huggingface.co/docs/hub/en/security-tokens\n", "\n", "# https://huggingface.co/datasets/MongoDB/airbnb_embeddings\n", "dataset = load_dataset(\"MongoDB/airbnb_embeddings\")\n", "\n", "# Convert the dataset to a pandas dataframe\n", "dataset_df = pd.DataFrame(dataset[\"train\"])\n", "\n", "dataset_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mo8vflfofyr3"}, "outputs": [], "source": ["dataset_df = dataset_df.drop(columns=[\"text_embeddings\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1NaLPvfWAFJ4"}, "outputs": [], "source": ["from llama_index.core.settings import Settings\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "from llama_index.llms.openai import OpenAI\n", "\n", "embed_model = OpenAIEmbedding(model=\"text-embedding-3-small\", dimensions=256)\n", "llm = OpenAI()\n", "\n", "Settings.llm = llm\n", "Settings.embed_model = embed_model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AWpooso1Amft", "outputId": "c353d389-2dec-44a5-d43d-cf5d30a1fead"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "The LLM sees this: \n", " Metadata: listing_url=>https://www.airbnb.com/rooms/10006546\n", "name=><PERSON><PERSON><PERSON> Du<PERSON>\n", "summary=>Fantastic duplex apartment with three bedrooms, located in the historic area of Porto, Ribeira (Cube) - UNESCO World Heritage Site. Centenary building fully rehabilitated, without losing their original character.\n", "space=>Privileged views of the Douro River and Ribeira square, our apartment offers the perfect conditions to discover the history and the charm of Porto. Apartment comfortable, charming, romantic and cozy in the heart of Ribeira. Within walking distance of all the most emblematic places of the city of Porto. The apartment is fully equipped to host 8 people, with cooker, oven, washing machine, dishwasher, microwave, coffee machine (Nespresso) and kettle. The apartment is located in a very typical area of the city that allows to cross with the most picturesque population of the city, welcoming, genuine and happy people that fills the streets with his outspoken speech and contagious with your sincere generosity, wrapped in a only parochial spirit.\n", "description=>Fantastic duplex apartment with three bedrooms, located in the historic area of Porto, Ribeira (Cube) - UNESCO World Heritage Site. Centenary building fully rehabilitated, without losing their original character. Privileged views of the Douro River and Ribeira square, our apartment offers the perfect conditions to discover the history and the charm of Porto. Apartment comfortable, charming, romantic and cozy in the heart of Ribeira. Within walking distance of all the most emblematic places of the city of Porto. The apartment is fully equipped to host 8 people, with cooker, oven, washing machine, dishwasher, microwave, coffee machine (Nespresso) and kettle. The apartment is located in a very typical area of the city that allows to cross with the most picturesque population of the city, welcoming, genuine and happy people that fills the streets with his outspoken speech and contagious with your sincere generosity, wrapped in a only parochial spirit. We are always available to help guests\n", "neighborhood_overview=>In the neighborhood of the river, you can find several restaurants as varied flavors, but without forgetting the so traditional northern food. You can also find several bars and pubs to unwind after a day's visit to the magnificent Port. To enjoy the Douro River can board the boats that daily make the ride of six bridges. You can also embark towards Régua, Barca d'Alva, Pinhão, etc and enjoy the Douro Wine Region, World Heritage of Humanity. The <PERSON><PERSON><PERSON>'s house is a few meters and no doubt it deserves a visit. They abound grocery stores, bakeries, etc. to make your meals. Souvenir shop, wine cellars, etc. to bring some souvenirs.\n", "notes=>Lose yourself in the narrow streets and staircases zone, have lunch in pubs and typical restaurants, and find the renovated cafes and shops in town. If you like exercise, rent a bicycle in the area and ride along the river to the sea, where it will enter beautiful beaches and terraces for everyone. The area is safe, find the bus stops 1min and metro line 5min. The bustling nightlife is a 10 min walk, where the streets are filled with people and entertainment for all. But Porto is much more than the historical center, here is modern museums, concert halls, clean and cared for beaches and surf all year round. Walk through the Ponte D. Luis and visit the different Caves of Port wine, where you will enjoy the famous port wine. Porto is a spoken city everywhere in the world as the best to be visited and savored by all ... natural beauty, culture, tradition, river, sea, beach, single people, typical food, and we are among those who best receive tourists, confirm! Come visit us and feel at ho\n", "access=>We are always available to help guests. The house is fully available to guests. We are always ready to assist guests. when possible we pick the guests at the airport.  This service transfer have a cost per person. We will also have service \"meal at home\" with a diverse menu and the taste of each. Enjoy the moment!\n", "interaction=>Cot - 10 € / night Dog - € 7,5 / night\n", "house_rules=>Make the house your home...\n", "property_type=>House\n", "room_type=>Entire home/apt\n", "bed_type=>Real Bed\n", "accommodates=>8\n", "bedrooms=>3.0\n", "beds=>5.0\n", "number_of_reviews=>51\n", "bathrooms=>1.0\n", "amenities=>[\"TV\", \"Cable TV\", \"Wifi\", \"Kitchen\", \"Paid parking off premises\", \"Smoking allowed\", \"Pets allowed\", \"Buzzer/wireless intercom\", \"Heating\", \"Family/kid friendly\", \"Washer\", \"First aid kit\", \"Fire extinguisher\", \"Essentials\", \"Hangers\", \"Hair dryer\", \"Iron\", \"Pack \\u2019n Play/travel crib\", \"Room-darkening shades\", \"Hot water\", \"Bed linens\", \"Extra pillows and blankets\", \"Microwave\", \"Coffee maker\", \"Refrigerator\", \"Dishwasher\", \"Dishes and silverware\", \"Cooking basics\", \"Oven\", \"Stove\", \"Cleaning before checkout\", \"Waterfront\"]\n", "price=>80\n", "extra_people=>15\n", "images=>{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/e83e702f-ef49-40fb-8fa0-6512d7e26e9b.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}\n", "address=>{\"street\": \"Porto, Porto, Portugal\", \"suburb\": \"\", \"government_area\": \"Cedofeita, Ildefonso, S\\u00e9, Miragaia, Nicolau, Vit\\u00f3ria\", \"market\": \"Porto\", \"country\": \"Portugal\", \"country_code\": \"PT\", \"location\": {\"type\": \"Point\", \"coordinates\": [-8.61308, 41.1413], \"is_location_exact\": false}}\n", "review_scores=>{\"review_scores_accuracy\": 9, \"review_scores_cleanliness\": 9, \"review_scores_checkin\": 10, \"review_scores_communication\": 10, \"review_scores_location\": 10, \"review_scores_value\": 9, \"review_scores_rating\": 89}\n", "weekly_price=>None\n", "monthly_price=>None\n", "-----\n", "Content: Fantastic duplex apartment with three bedrooms, located in the historic area of Porto, Ribeira (Cube) - UNESCO World Heritage Site. Centenary building fully rehabilitated, without losing their original character. Privileged views of the Douro River and Ribeira square, our apartment offers the perfect conditions to discover the history and the charm of Porto. Apartment comfortable, charming, romantic and cozy in the heart of Ribeira. Within walking distance of all the most emblematic places of the city of Porto. The apartment is fully equipped to host 8 people, with cooker, oven, washing machine, dishwasher, microwave, coffee machine (Nespresso) and kettle. The apartment is located in a very typical area of the city that allows to cross with the most picturesque population of the city, welcoming, genuine and happy people that fills the streets with his outspoken speech and contagious with your sincere generosity, wrapped in a only parochial spirit. We are always available to help guests\n", "\n", "The Embedding model sees this: \n", " Metadata: listing_url=>https://www.airbnb.com/rooms/10006546\n", "name=><PERSON><PERSON><PERSON> Du<PERSON>\n", "summary=>Fantastic duplex apartment with three bedrooms, located in the historic area of Porto, Ribeira (Cube) - UNESCO World Heritage Site. Centenary building fully rehabilitated, without losing their original character.\n", "space=>Privileged views of the Douro River and Ribeira square, our apartment offers the perfect conditions to discover the history and the charm of Porto. Apartment comfortable, charming, romantic and cozy in the heart of Ribeira. Within walking distance of all the most emblematic places of the city of Porto. The apartment is fully equipped to host 8 people, with cooker, oven, washing machine, dishwasher, microwave, coffee machine (Nespresso) and kettle. The apartment is located in a very typical area of the city that allows to cross with the most picturesque population of the city, welcoming, genuine and happy people that fills the streets with his outspoken speech and contagious with your sincere generosity, wrapped in a only parochial spirit.\n", "description=>Fantastic duplex apartment with three bedrooms, located in the historic area of Porto, Ribeira (Cube) - UNESCO World Heritage Site. Centenary building fully rehabilitated, without losing their original character. Privileged views of the Douro River and Ribeira square, our apartment offers the perfect conditions to discover the history and the charm of Porto. Apartment comfortable, charming, romantic and cozy in the heart of Ribeira. Within walking distance of all the most emblematic places of the city of Porto. The apartment is fully equipped to host 8 people, with cooker, oven, washing machine, dishwasher, microwave, coffee machine (Nespresso) and kettle. The apartment is located in a very typical area of the city that allows to cross with the most picturesque population of the city, welcoming, genuine and happy people that fills the streets with his outspoken speech and contagious with your sincere generosity, wrapped in a only parochial spirit. We are always available to help guests\n", "neighborhood_overview=>In the neighborhood of the river, you can find several restaurants as varied flavors, but without forgetting the so traditional northern food. You can also find several bars and pubs to unwind after a day's visit to the magnificent Port. To enjoy the Douro River can board the boats that daily make the ride of six bridges. You can also embark towards Régua, Barca d'Alva, Pinhão, etc and enjoy the Douro Wine Region, World Heritage of Humanity. The <PERSON><PERSON><PERSON>'s house is a few meters and no doubt it deserves a visit. They abound grocery stores, bakeries, etc. to make your meals. Souvenir shop, wine cellars, etc. to bring some souvenirs.\n", "notes=>Lose yourself in the narrow streets and staircases zone, have lunch in pubs and typical restaurants, and find the renovated cafes and shops in town. If you like exercise, rent a bicycle in the area and ride along the river to the sea, where it will enter beautiful beaches and terraces for everyone. The area is safe, find the bus stops 1min and metro line 5min. The bustling nightlife is a 10 min walk, where the streets are filled with people and entertainment for all. But Porto is much more than the historical center, here is modern museums, concert halls, clean and cared for beaches and surf all year round. Walk through the Ponte D. Luis and visit the different Caves of Port wine, where you will enjoy the famous port wine. Porto is a spoken city everywhere in the world as the best to be visited and savored by all ... natural beauty, culture, tradition, river, sea, beach, single people, typical food, and we are among those who best receive tourists, confirm! Come visit us and feel at ho\n", "access=>We are always available to help guests. The house is fully available to guests. We are always ready to assist guests. when possible we pick the guests at the airport.  This service transfer have a cost per person. We will also have service \"meal at home\" with a diverse menu and the taste of each. Enjoy the moment!\n", "interaction=>Cot - 10 € / night Dog - € 7,5 / night\n", "house_rules=>Make the house your home...\n", "property_type=>House\n", "room_type=>Entire home/apt\n", "bed_type=>Real Bed\n", "accommodates=>8\n", "bedrooms=>3.0\n", "beds=>5.0\n", "number_of_reviews=>51\n", "bathrooms=>1.0\n", "amenities=>[\"TV\", \"Cable TV\", \"Wifi\", \"Kitchen\", \"Paid parking off premises\", \"Smoking allowed\", \"Pets allowed\", \"Buzzer/wireless intercom\", \"Heating\", \"Family/kid friendly\", \"Washer\", \"First aid kit\", \"Fire extinguisher\", \"Essentials\", \"Hangers\", \"Hair dryer\", \"Iron\", \"Pack \\u2019n Play/travel crib\", \"Room-darkening shades\", \"Hot water\", \"Bed linens\", \"Extra pillows and blankets\", \"Microwave\", \"Coffee maker\", \"Refrigerator\", \"Dishwasher\", \"Dishes and silverware\", \"Cooking basics\", \"Oven\", \"Stove\", \"Cleaning before checkout\", \"Waterfront\"]\n", "price=>80\n", "extra_people=>15\n", "images=>{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/e83e702f-ef49-40fb-8fa0-6512d7e26e9b.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}\n", "address=>{\"street\": \"Porto, Porto, Portugal\", \"suburb\": \"\", \"government_area\": \"Cedofeita, Ildefonso, S\\u00e9, Miragaia, Nicolau, Vit\\u00f3ria\", \"market\": \"Porto\", \"country\": \"Portugal\", \"country_code\": \"PT\", \"location\": {\"type\": \"Point\", \"coordinates\": [-8.61308, 41.1413], \"is_location_exact\": false}}\n", "review_scores=>{\"review_scores_accuracy\": 9, \"review_scores_cleanliness\": 9, \"review_scores_checkin\": 10, \"review_scores_communication\": 10, \"review_scores_location\": 10, \"review_scores_value\": 9, \"review_scores_rating\": 89}\n", "weekly_price=>None\n", "monthly_price=>None\n", "-----\n", "Content: Fantastic duplex apartment with three bedrooms, located in the historic area of Porto, Ribeira (Cube) - UNESCO World Heritage Site. Centenary building fully rehabilitated, without losing their original character. Privileged views of the Douro River and Ribeira square, our apartment offers the perfect conditions to discover the history and the charm of Porto. Apartment comfortable, charming, romantic and cozy in the heart of Ribeira. Within walking distance of all the most emblematic places of the city of Porto. The apartment is fully equipped to host 8 people, with cooker, oven, washing machine, dishwasher, microwave, coffee machine (Nespresso) and kettle. The apartment is located in a very typical area of the city that allows to cross with the most picturesque population of the city, welcoming, genuine and happy people that fills the streets with his outspoken speech and contagious with your sincere generosity, wrapped in a only parochial spirit. We are always available to help guests\n"]}], "source": ["import json\n", "\n", "from llama_index.core import Document\n", "from llama_index.core.schema import MetadataMode\n", "\n", "# Convert the DataFrame to a JSON string representation\n", "documents_json = dataset_df.to_json(orient=\"records\")\n", "\n", "# Load the JSON string into a Python list of dictionaries\n", "documents_list = json.loads(documents_json)\n", "\n", "llama_documents = []\n", "\n", "for document in documents_list:\n", "    # Value for metadata must be one of (str, int, float, None)\n", "    document[\"amenities\"] = json.dumps(document[\"amenities\"])\n", "    document[\"images\"] = json.dumps(document[\"images\"])\n", "    document[\"host\"] = json.dumps(document[\"host\"])\n", "    document[\"address\"] = json.dumps(document[\"address\"])\n", "    document[\"availability\"] = json.dumps(document[\"availability\"])\n", "    document[\"review_scores\"] = json.dumps(document[\"review_scores\"])\n", "    document[\"reviews\"] = json.dumps(document[\"reviews\"])\n", "    document[\"image_embeddings\"] = json.dumps(document[\"image_embeddings\"])\n", "\n", "    # Create a Document object with the text and excluded metadata for llm and embedding models\n", "    llama_document = Document(\n", "        text=document[\"description\"],\n", "        metadata=document,\n", "        excluded_llm_metadata_keys=[\n", "            \"_id\",\n", "            \"transit\",\n", "            \"minimum_nights\",\n", "            \"maximum_nights\",\n", "            \"cancellation_policy\",\n", "            \"last_scraped\",\n", "            \"calendar_last_scraped\",\n", "            \"first_review\",\n", "            \"last_review\",\n", "            \"security_deposit\",\n", "            \"cleaning_fee\",\n", "            \"guests_included\",\n", "            \"host\",\n", "            \"availability\",\n", "            \"reviews\",\n", "            \"image_embeddings\",\n", "        ],\n", "        excluded_embed_metadata_keys=[\n", "            \"_id\",\n", "            \"transit\",\n", "            \"minimum_nights\",\n", "            \"maximum_nights\",\n", "            \"cancellation_policy\",\n", "            \"last_scraped\",\n", "            \"calendar_last_scraped\",\n", "            \"first_review\",\n", "            \"last_review\",\n", "            \"security_deposit\",\n", "            \"cleaning_fee\",\n", "            \"guests_included\",\n", "            \"host\",\n", "            \"availability\",\n", "            \"reviews\",\n", "            \"image_embeddings\",\n", "        ],\n", "        metadata_template=\"{key}=>{value}\",\n", "        text_template=\"Metadata: {metadata_str}\\n-----\\nContent: {content}\",\n", "    )\n", "\n", "    llama_documents.append(llama_document)\n", "\n", "# Observing an example of what the LLM and Embedding model receive as input\n", "print(\n", "    \"\\nThe LLM sees this: \\n\",\n", "    llama_documents[0].get_content(metadata_mode=MetadataMode.LLM),\n", ")\n", "print(\n", "    \"\\nThe Embedding model sees this: \\n\",\n", "    llama_documents[0].get_content(metadata_mode=MetadataMode.EMBED),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xC4ufUjvAp2G"}, "outputs": [], "source": ["llama_documents[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JmCuxyQjAsLs"}, "outputs": [], "source": ["from llama_index.core.node_parser import SentenceSplitter\n", "from llama_index.core.schema import MetadataMode\n", "\n", "parser = SentenceSplitter(chunk_size=5000)\n", "nodes = parser.get_nodes_from_documents(llama_documents)\n", "\n", "for node in nodes:\n", "    node_embedding = embed_model.get_text_embedding(\n", "        node.get_content(metadata_mode=MetadataMode.EMBED)\n", "    )\n", "    node.embedding = node_embedding"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON><PERSON> VECTOR DATABASE CONNECTION AND SETUP\n", "\n", "MongoDB acts as both an operational and a vector database for the RAG system. \n", "MongoDB Atlas specifically provides a database solution that efficiently stores, queries and retrieves vector embeddings.\n", "\n", "Creating a database and collection within MongoDB is made simple with MongoDB Atlas.\n", "\n", "1. First, register for a [MongoDB Atlas account](https://www.mongodb.com/cloud/atlas/register). For existing users, sign into MongoDB Atlas.\n", "2. [Follow the instructions](https://www.mongodb.com/docs/atlas/tutorial/deploy-free-tier-cluster/). Select Atlas UI as the procedure to deploy your first cluster. \n", "3. Create the database: `airbnb`.\n", "4. Within the database` airbnb`, create the collection ‘listings_reviews’. \n", "5. Create a [vector search index](https://www.mongodb.com/docs/atlas/atlas-vector-search/create-index/#procedure/) named vector_index for the ‘listings_reviews’ collection. This index enables the RAG application to retrieve records as additional context to supplement user queries via vector search. Below is the JSON definition of the data collection vector search index. \n", "\n", "Follow MongoDB’s [steps to get the connection](https://www.mongodb.com/docs/manual/reference/connection-string/) string from the Atlas UI. After setting up the database and obtaining the Atlas cluster connection URI, securely store the URI within your development environment.\n", "\n", "This guide uses Google Colab, which offers a feature for securely storing environment secrets. These secrets can then be accessed within the development environment. Specifically, the line mongo_uri = userdata.get('MONGO_URI') retrieves the URI from the secure storage."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iCqflLPNBZe4", "outputId": "830f33d0-04e4-458e-eaf6-a483365f8003"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["import pymongo\n", "from google.colab import userdata\n", "\n", "\n", "def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish connection to the MongoDB.\"\"\"\n", "    try:\n", "        client = pymongo.MongoClient(\n", "            mongo_uri, appname=\"devrel.showcase.rag_llamaindex_mongodb\"\n", "        )\n", "        print(\"Connection to MongoDB successful\")\n", "        return client\n", "    except pymongo.errors.ConnectionFailure as e:\n", "        print(f\"Connection failed: {e}\")\n", "        return None\n", "\n", "\n", "mongo_uri = userdata.get(\"MONGO_URI\")\n", "if not mongo_uri:\n", "    print(\"MONGO_URI not set in environment variables\")\n", "\n", "mongo_client = get_mongo_client(mongo_uri)\n", "\n", "DB_NAME = \"airbnb\"\n", "COLLECTION_NAME = \"listings_reviews\"\n", "\n", "db = mongo_client[DB_NAME]\n", "collection = db[COLLECTION_NAME]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "D5sne8YMBa80", "outputId": "5c72edd0-6641-41ca-a41c-d35d2d3d1a5a"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 0, 'electionId': ObjectId('7fffffff0000000000000014'), 'opTime': {'ts': Timestamp(1711370832, 1), 't': 20}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1711370832, 1), 'signature': {'hash': b'\\xa3\\xe8\\xd6\\xb8\\xe2=\\xae\\xcc\\x8f\\xeap\\xc6\\xca\\x14\\x97KR\\xde\\xc0\\x18', 'keyId': 7320226449804230661}}, 'operationTime': Timestamp(1711370832, 1)}, acknowledged=True)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# To ensure we are working with a fresh collection\n", "# delete any existing records in the collection\n", "collection.delete_many({})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aj4M9doOBc9f"}, "outputs": [], "source": ["from llama_index.vector_stores.mongodb import MongoDBAtlasVectorSearch\n", "\n", "vector_store = MongoDBAtlasVectorSearch(\n", "    mongo_client,\n", "    db_name=DB_NAME,\n", "    collection_name=COLLECTION_NAME,\n", "    index_name=\"vector_index\",\n", ")\n", "vector_store.add(nodes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "s9mKDlRSBe3J"}, "outputs": [], "source": ["from llama_index.core import VectorStoreIndex\n", "\n", "index = VectorStoreIndex.from_vector_store(vector_store)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 153}, "id": "8s-juQ03BgjA", "outputId": "5b0927af-a694-4a65-9584-4719e3407053"}, "outputs": [{"data": {"text/markdown": ["**`Final Response:`** I recommend the first option, \"Good Home! Near Downtown Montreal, Unlimited Wifi,\" as it offers a warm and friendly atmosphere with the host treating guests like family members. Additionally, it is close to restaurants, pubs, malls, and other amenities, providing a convenient and welcoming stay for guests looking for a cozy and friendly environment near dining options."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[NodeWithScore(node=TextNode(id_='7bee48d7-9474-40de-9cc1-f7f018a380fa', embedding=None, metadata={'_id': 30104792, 'listing_url': 'https://www.airbnb.com/rooms/30104792', 'name': 'Good Home! Near Downtown Montreal, Unlimited Wifi', 'summary': 'You need to feel at home? Come to our home!! Cozy Room With Wifi In A Family Resident. Possibility of breakfast at $6 per day, per guest if desired. Home near restaurants, pubs, malls etc.', 'space': \"Home away from home! Close to bus stops, Metro Angrignon, walking distance to nice restaurants e.g buffet, grocery stores etc. Close to Malls, downtown, movie theatres, recreation parks, Airport. Decent, peaceful, clean area in Lasalle. Parking available. 2 bedrooms in same house for hosting guests. I'm a down to earth peace-loving person with lovely family who will make your stay fun! Pick up from Trudeau Airport/bus stations is available for $50 fees. Please message host to discuss, thanks.\", 'description': \"You need to feel at home? Come to our home!! Cozy Room With Wifi In A Family Resident. Possibility of breakfast at $6 per day, per guest if desired. Home near restaurants, pubs, malls etc. Home away from home! Close to bus stops, Metro Angrignon, walking distance to nice restaurants e.g buffet, grocery stores etc. Close to Malls, downtown, movie theatres, recreation parks, Airport. Decent, peaceful, clean area in Lasalle. Parking available. 2 bedrooms in same house for hosting guests. I'm a down to earth peace-loving person with lovely family who will make your stay fun! Pick up from Trudeau Airport/bus stations is available for $50 fees. Please message host to discuss, thanks. Close to airport, restaurants, malls, parks etc. Easy access to transportations, downtown Montreal, historical sights, tourist centres, hospitals, etc. We love to make our guests very comfortable, treating them as family members. Depending on the guests, my children and I can interact or give space. Calm, safe, \", 'neighborhood_overview': 'Calm, safe, clean, peaceful neighbourhood. Near Downtown. Street parking is always available. Easy access to many tourists centres, bubbling and important places in Montreal.', 'notes': 'The Guest room is upstairs. Few stairs to climb', 'transit': 'Easy breezy way of getting around. Close to bus stops, trains, subways, underground train station.', 'access': 'Close to airport, restaurants, malls, parks etc. Easy access to transportations, downtown Montreal, historical sights, tourist centres, hospitals, etc.', 'interaction': 'We love to make our guests very comfortable, treating them as family members. Depending on the guests, my children and I can interact or give space.', 'house_rules': '- Only decent responsible people that appreciates godly values should apply to stay at my places.', 'property_type': 'Apartment', 'room_type': 'Private room', 'bed_type': 'Real Bed', 'minimum_nights': 2, 'maximum_nights': 240, 'cancellation_policy': 'flexible', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1546318800000, 'last_review': 1546318800000, 'accommodates': 6, 'bedrooms': 2.0, 'beds': 4.0, 'number_of_reviews': 1, 'bathrooms': 1.0, 'amenities': '[\"TV\", \"Wifi\", \"Kitchen\", \"Gym\", \"Breakfast\", \"Free street parking\", \"Heating\", \"Washer\", \"Dryer\", \"Smoke detector\", \"Carbon monoxide detector\", \"First aid kit\", \"Fire extinguisher\", \"Essentials\", \"Shampoo\", \"Lock on bedroom door\", \"Hangers\", \"Hair dryer\", \"Iron\", \"Laptop friendly workspace\", \"Private living room\", \"Private entrance\", \"Hot water\", \"Bed linens\", \"Extra pillows and blankets\", \"Microwave\", \"Coffee maker\", \"Refrigerator\", \"Dishes and silverware\", \"Cooking basics\", \"Oven\", \"Stove\", \"Patio or balcony\", \"Garden or backyard\", \"Luggage dropoff allowed\", \"Long term stays allowed\", \"Cleaning before checkout\"]', 'price': 20, 'security_deposit': 0.0, 'cleaning_fee': 0.0, 'extra_people': 19, 'guests_included': 1, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/cbb9eaa3-af7d-408a-9fb8-559585c1f77b.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"124221126\", \"host_url\": \"https://www.airbnb.com/users/show/124221126\", \"host_name\": \"Sister Mariam\", \"host_location\": \"Montreal, Qu\\\\u00e9bec, Canada\", \"host_about\": \"I\\'m a god-fearing Christian who loves to treat others the way I would like to be treated. I\\'m a kind, approachable, beautiful person that loves adventure and life. I dislike troublemakers, fraudsters, boogie-men/women, and negative people. I love to praise God and pray. I love to engage in activities that can improve my life, my happiness, that of others, and make this world a better place. I am a widow with children.\\\\r\\\\n\\\\r\\\\nOnly if you can--Tips for provided good ambiance in my home is welcome, lol!  Nothing\\'s bad in appreciation.\", \"host_response_time\": \"within a day\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/9bdcb74e-429c-4328-bf8b-1961b3ff1a80.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/9bdcb74e-429c-4328-bf8b-1961b3ff1a80.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"LaSalle\", \"host_response_rate\": 100, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 5, \"host_total_listings_count\": 5, \"host_verifications\": [\"email\", \"phone\", \"reviews\"]}', 'address': '{\"street\": \"Montr\\\\u00e9al, Qu\\\\u00e9bec, Canada\", \"suburb\": \"LaSalle\", \"government_area\": \"LaSalle\", \"market\": \"Montreal\", \"country\": \"Canada\", \"country_code\": \"CA\", \"location\": {\"type\": \"Point\", \"coordinates\": [-73.6332, 45.43943], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 30, \"availability_60\": 60, \"availability_90\": 90, \"availability_365\": 365}', 'review_scores': '{\"review_scores_accuracy\": 2, \"review_scores_cleanliness\": 2, \"review_scores_checkin\": 2, \"review_scores_communication\": 2, \"review_scores_location\": 2, \"review_scores_value\": 2, \"review_scores_rating\": 40}', 'reviews': '[{\"_id\": \"365682443\", \"date\": 1546318800000, \"listing_id\": \"30104792\", \"reviewer_id\": \"195976319\", \"reviewer_name\": \"Feroz\", \"comments\": \"I wish I had spent a little more money and gone to a motel because this was an absolute nightmare of a house. There was dirt literally everywhere and it looked like the common areas, bathroom, and room hadn\\\\u2019t been swept in ages. The bathroom had grime and hair on the sink, floor and in the bathtub. The room was supposed to be two queen beds but instead was one twin and one queen and was super cramped and uncomfortable. The room was also supposed to have a \\\\u201cview\\\\u201d according to the listing but all we saw were the neighbors\\\\u2019 backyard as well as a huge pile of random trash. The soap dispenser in the bathroom had soap so diluted with water that we ended up using hand sanitizer to clean our hands. The listing also said it was close to downtown but it ended up being a 45-1 hour trip to downtown one way (bus and then train), and it was super inaccessible. Overall I\\\\u2019m really disappointed because the listing looked lovely and instead it ended up being super gross and really uncomfortable for all of us. Additionally there were a ton of people there and the lock on the door for our room was super broken down and could easily have been opened. I\\\\u2019m also pretty sure that mould/dust in the house is the reason my friend got sick the first night we were there.  \\\\n\\\\nI have stayed in cheaper places that have been nicer than this and it was so awful to have this trip ruined for us. Our friend group was meeting for the first time in over 8 months and it was such a let down as the host seemed really nice and down to earth. We definitely learned our lesson to do more research and to plan further in advance to book a better place for New Years in the future.\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[0.0087131597, 0.3087266386, -0.0964476615, -0.0669194907, 0.1173319966, -0.1665966809, 0.086722374, 0.4590649009, 0.2897408903, -0.1544781178, 0.2854067385, -0.0510558411, 0.4696467221, -0.3689635396, 0.3675877452, 0.0054137995, -0.2415781766, 0.2203628421, 0.1895287782, 0.0515499972, 0.1919480264, -0.2030119747, 0.249973014, 0.0406546369, -0.0685396567, 0.1578861773, -0.0733809099, -0.595286727, 0.2316341549, -0.0476222448, 0.4949313402, 0.6323911548, -0.3126279414, 0.0821399763, -0.4169414639, 0.4399569333, -0.0552489683, 0.026208695, -0.1228978261, 1.0988657475, -0.3570894003, 0.0187279209, -0.1041573137, -0.0130204074, -0.2048081458, -0.5983131528, -0.2823227048, 0.3279002607, 0.1365908682, 0.293061763, 0.3089332283, 0.2111629099, 0.081511125, -0.094217971, -0.3981106281, -0.2627288699, -0.1727879345, -0.0233033951, -0.0018610135, 0.3391076922, -1.4292603731, -0.2940623462, 0.1225190982, -0.1401946843, -0.2410475612, -0.1591062844, 0.8402563334, 0.3864677548, 0.2241325974, 0.0170251578, 0.00985891, -0.009158059, 0.1126279905, 0.2719528675, 0.0580085889, 0.1361515075, -0.1054801345, 0.0510322452, 0.0086921081, -0.4975850284, 0.2089439631, -0.069592692, -0.0722439364, 0.656088531, 0.3341910839, 0.0256420504, 0.5607144237, -0.4029604495, -0.2382489443, -0.406386286, 0.2946088314, -0.2408376634, -7.3814001083, -0.0643983632, -0.209194839, 0.3202933073, 0.807333529, 0.1154819429, 0.182734251, 0.2429952323, 0.0029309466, 0.3034316599, -0.3545795381, -0.1250625551, 0.3126533628, -0.0722367913, -0.7878907919, 0.2470676005, 0.2950665057, 0.0616634451, -0.2932725251, 0.3719895184, -0.0867528319, 0.3275797069, -0.1720134467, -0.0732217431, 0.0469984338, 0.0047199018, 0.4511741698, 0.2929455042, 0.15383479, 0.0344798341, 0.1928770244, 0.0408457294, -0.1070331484, -0.2809849083, 0.1236862913, -0.432941258, -0.0341565311, -0.1076848954, 0.2614488602, 0.3981456161, -0.3941384852, 0.9058333039, 0.2332912982, 0.4331364632, -0.1066410914, 0.5422659516, -0.1027099639, 0.3368598521, 0.3953680694, 0.1647342145, 0.5184728503, 0.3749254942, 0.0332119949, 0.2060611546, -0.1420429796, -0.414906919, -0.1520182937, 0.0492876098, 0.0485379472, 0.0316226706, 0.8616147637, -0.2951617539, 0.3912798464, -0.0243016053, 0.013491923, -0.1786512583, 0.1144237518, -0.3361517787, 0.1472538263, -0.4519759715, 0.0840991884, -0.0158431567, 0.1176095083, 0.1342675239, -0.0847330242, 0.1846175939, 0.0146619789, -0.183891803, -0.111066699, 0.09125413, -0.0165000856, -0.0383719876, 0.0038356883, 0.2562685013, 0.9297307134, 0.0399527214, -0.4834015369, 0.0824832618, -0.0756136551, -0.3027047813, 0.2335678786, -0.3643769324, -0.2616147697, 0.2554442883, 0.191571027, -0.0952179953, 0.1213712096, -0.0239444263, -0.2138979584, -0.2318549156, 0.0913313925, -0.1152945012, 0.2044678777, -0.1504226923, 0.1957125366, -0.0211993232, -0.9899336696, -0.1360519975, 0.3001494706, 0.1466243714, -0.0941517651, 0.0859374031, 0.4368960857, 0.03337707, 0.064840734, 0.0336746871, -0.0513269752, 0.2873356342, -0.3250787556, 0.6446890831, -0.0550217107, 0.1198315024, 0.0422508791, -0.6399012804, 0.1424319446, 0.2680268884, 1.4176570177, -0.2855177522, -0.0831143558, 0.4350849688, -0.20688878, -0.3852417469, -0.0129499678, 0.1180446446, -0.1220332533, 0.1823897362, -0.3035959005, 0.0560395382, 0.0681687742, 0.248410061, 0.0503316075, 0.5450854301, 0.2209971249, 0.7950102091, -0.0767294392, 0.1819058955, -0.3505471647, 0.209254086, -0.0566805527, 0.0300040767, 0.0789228976, -0.0439238995, -0.2168816477, 0.9719731212, -0.1201313585, -0.0864691362, 0.1438076943, 0.1200009882, 0.3339183033, -0.4484075904, 0.2933944762, -0.0574935861, -0.1381185949, 0.2616750896, 0.1026837528, 0.3332834244, 0.6589193344, -0.0722313076, 0.0560983196, 0.3434185982, 0.3966342211, 0.828354001, -0.3419497609, -0.5721539855, -0.1842368543, 0.1956673563, -0.0543695837, 0.3923288584, -0.1785305589, 0.2310208827, 0.2705766857, 0.1588563025, 0.2409881204, 0.045501627, -0.1668832302, -0.0238160249, -0.0226109959, -0.2395197153, 0.2980213463, 0.5108668208, 0.1274812073, 0.1288464665, 0.2831356227, -0.0815863609, 0.9121878147, -0.0077936472, -0.0139129404, -0.4150970578, 0.4286291003, -0.1344895065, 0.3812022805, 0.0515977517, -0.4295915961, 0.1802729219, -0.2357324958, 0.0894462839, -0.0696362555, -0.2323539853, -0.2395325452, 0.221670568, -0.2656365037, -0.4655920565, 0.7793779373, -0.3077155948, 0.0014950503, -0.4018220901, 0.6661109924, 0.1216943711, -0.1918688715, 0.2270762473, 0.90490973, -0.0013707168, -0.2471050769, 0.2353613377, 0.621488452, 0.9619912505, -0.0009955429, -0.1839095354, 0.1130869687, 1.6457654238, -0.1885524392, 0.2271868587, 0.2662683725, -0.010388555, 0.2222096324, -0.4788481295, 0.2301119417, 0.1892809272, 0.0134985596, -0.3070843816, 0.2983486354, -0.692476511, -0.5125385523, 0.145054698, 0.1712953746, 0.2402451336, -0.2263351232, 0.3781630695, -0.1790316999, -0.7433581948, -0.1466355622, -0.1812521666, -0.2092121094, -0.2599362135, -0.2493156195, 0.1471805423, 0.1973813474, -0.6109083891, 0.7207445502, 0.2279732078, -0.104049243, 0.2357980758, 0.1196252853, -0.8928000331, 0.0300499946, 0.5876648426, -0.1018871218, -0.1181044951, -1.2712432146, 0.0555443987, -0.0720012933, 0.44822824, -0.4737504721, 0.4043300152, -0.0087917745, -0.4324485362, -0.0415574685, 0.0342851765, 0.149513647, 0.1345567107, -0.2637580335, -0.1772202104, 0.1381409168, 0.5743621588, 1.2489147186, -0.1596563905, 0.2492544055, 0.2417871058, -0.0758709759, -0.3223493695, -0.1412379146, 0.1743490845, 0.2755281329, -0.7658397555, -0.0651617795, -0.265383184, 0.1389195025, 0.2403477579, -0.4273844063, 0.6185761094, -0.3723655045, 0.1966599524, -0.330529958, 0.0931155384, -0.0528663844, -0.0652609766, 0.1823382378, 0.1786832511, 0.2994839549, 0.1845416129, 1.5713065863, 0.2745104134, -0.0825947002, 0.0755056292, -0.0375615582, -0.1294188648, -0.8250491023, -0.0517890155, 0.1319655478, 0.4264459908, 0.1041415632, 0.0570227467, -0.3753731847, -0.4335963428, -0.2069931179, 0.2388263345, -0.3778446913, 0.0421822853, -0.0629177094, 0.1733497679, 0.4677535295, 0.46030882, -0.3025812209, 0.4757867157, -0.1583559513, 0.3667739332, -2.0504369736, 0.1882988811, 0.0182618946, -0.3281486332, 0.8391265273, 0.1826014072, 0.1111886874, -0.2568766773, 0.0725941882, -0.1435123682, -0.0751155615, 0.286762923, -0.4887376428, 0.0900786817, 0.2651269436, 0.4729520679, 0.2159009576, -0.0535720885, -0.2547558546, 0.0618954189, 0.038335748, -0.2312321514, 0.6065238118, 0.1614649594, 0.0785366893, 0.504627049, 0.3070814908, 0.0426609553, 0.5949465632, -0.1837699413, 0.4706873596, 0.2063554376, -0.1087655574, 0.1847458184, -0.010036502, -0.2983679771, 0.1220055223, -0.4577333629, -0.3015633225, -0.1162549332, -0.494130224, -0.3066190481, 0.2148017436, 0.0188383907, 0.3792521656, -0.8130715489, -0.1798872054, -0.4672985971, 0.1650904566, -0.3949325383, -0.0192587487, 0.1821415573, -0.0667862743, -0.6882886291, 0.219771415, 0.0542834438, 0.8225620985, 0.1127012372, -0.2661329806, 0.396173954, -0.1167109013, 0.0773602724, -0.1842371672, 0.0874487236, 0.5003326535, -0.4608216286, -0.2168773413, -0.2362791747, 0.2771558464, 0.149546966, -0.190461576, 0.1775102615, -0.1223850027, -0.2622747123, 0.0818929896, 0.1634190828, 0.2843185067, -0.3552679121, -0.2757597268, -0.0187404249, -0.2880458236, 0.7036882043, 0.0217038132, -0.1720914841]'}, excluded_embed_metadata_keys=['_id', 'transit', 'minimum_nights', 'maximum_nights', 'cancellation_policy', 'last_scraped', 'calendar_last_scraped', 'first_review', 'last_review', 'security_deposit', 'cleaning_fee', 'guests_included', 'host', 'availability', 'reviews', 'image_embeddings'], excluded_llm_metadata_keys=['_id', 'transit', 'minimum_nights', 'maximum_nights', 'cancellation_policy', 'last_scraped', 'calendar_last_scraped', 'first_review', 'last_review', 'security_deposit', 'cleaning_fee', 'guests_included', 'host', 'availability', 'reviews', 'image_embeddings'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='16a35caa-c82a-4349-99ac-fb8836d27a1f', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'_id': 30104792, 'listing_url': 'https://www.airbnb.com/rooms/30104792', 'name': 'Good Home! Near Downtown Montreal, Unlimited Wifi', 'summary': 'You need to feel at home? Come to our home!! Cozy Room With Wifi In A Family Resident. Possibility of breakfast at $6 per day, per guest if desired. Home near restaurants, pubs, malls etc.', 'space': \"Home away from home! Close to bus stops, Metro Angrignon, walking distance to nice restaurants e.g buffet, grocery stores etc. Close to Malls, downtown, movie theatres, recreation parks, Airport. Decent, peaceful, clean area in Lasalle. Parking available. 2 bedrooms in same house for hosting guests. I'm a down to earth peace-loving person with lovely family who will make your stay fun! Pick up from Trudeau Airport/bus stations is available for $50 fees. Please message host to discuss, thanks.\", 'description': \"You need to feel at home? Come to our home!! Cozy Room With Wifi In A Family Resident. Possibility of breakfast at $6 per day, per guest if desired. Home near restaurants, pubs, malls etc. Home away from home! Close to bus stops, Metro Angrignon, walking distance to nice restaurants e.g buffet, grocery stores etc. Close to Malls, downtown, movie theatres, recreation parks, Airport. Decent, peaceful, clean area in Lasalle. Parking available. 2 bedrooms in same house for hosting guests. I'm a down to earth peace-loving person with lovely family who will make your stay fun! Pick up from Trudeau Airport/bus stations is available for $50 fees. Please message host to discuss, thanks. Close to airport, restaurants, malls, parks etc. Easy access to transportations, downtown Montreal, historical sights, tourist centres, hospitals, etc. We love to make our guests very comfortable, treating them as family members. Depending on the guests, my children and I can interact or give space. Calm, safe, \", 'neighborhood_overview': 'Calm, safe, clean, peaceful neighbourhood. Near Downtown. Street parking is always available. Easy access to many tourists centres, bubbling and important places in Montreal.', 'notes': 'The Guest room is upstairs. Few stairs to climb', 'transit': 'Easy breezy way of getting around. Close to bus stops, trains, subways, underground train station.', 'access': 'Close to airport, restaurants, malls, parks etc. Easy access to transportations, downtown Montreal, historical sights, tourist centres, hospitals, etc.', 'interaction': 'We love to make our guests very comfortable, treating them as family members. Depending on the guests, my children and I can interact or give space.', 'house_rules': '- Only decent responsible people that appreciates godly values should apply to stay at my places.', 'property_type': 'Apartment', 'room_type': 'Private room', 'bed_type': 'Real Bed', 'minimum_nights': 2, 'maximum_nights': 240, 'cancellation_policy': 'flexible', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1546318800000, 'last_review': 1546318800000, 'accommodates': 6, 'bedrooms': 2.0, 'beds': 4.0, 'number_of_reviews': 1, 'bathrooms': 1.0, 'amenities': '[\"TV\", \"Wifi\", \"Kitchen\", \"Gym\", \"Breakfast\", \"Free street parking\", \"Heating\", \"Washer\", \"Dryer\", \"Smoke detector\", \"Carbon monoxide detector\", \"First aid kit\", \"Fire extinguisher\", \"Essentials\", \"Shampoo\", \"Lock on bedroom door\", \"Hangers\", \"Hair dryer\", \"Iron\", \"Laptop friendly workspace\", \"Private living room\", \"Private entrance\", \"Hot water\", \"Bed linens\", \"Extra pillows and blankets\", \"Microwave\", \"Coffee maker\", \"Refrigerator\", \"Dishes and silverware\", \"Cooking basics\", \"Oven\", \"Stove\", \"Patio or balcony\", \"Garden or backyard\", \"Luggage dropoff allowed\", \"Long term stays allowed\", \"Cleaning before checkout\"]', 'price': 20, 'security_deposit': 0.0, 'cleaning_fee': 0.0, 'extra_people': 19, 'guests_included': 1, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/cbb9eaa3-af7d-408a-9fb8-559585c1f77b.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"124221126\", \"host_url\": \"https://www.airbnb.com/users/show/124221126\", \"host_name\": \"Sister Mariam\", \"host_location\": \"Montreal, Qu\\\\u00e9bec, Canada\", \"host_about\": \"I\\'m a god-fearing Christian who loves to treat others the way I would like to be treated. I\\'m a kind, approachable, beautiful person that loves adventure and life. I dislike troublemakers, fraudsters, boogie-men/women, and negative people. I love to praise God and pray. I love to engage in activities that can improve my life, my happiness, that of others, and make this world a better place. I am a widow with children.\\\\r\\\\n\\\\r\\\\nOnly if you can--Tips for provided good ambiance in my home is welcome, lol!  Nothing\\'s bad in appreciation.\", \"host_response_time\": \"within a day\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/9bdcb74e-429c-4328-bf8b-1961b3ff1a80.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/9bdcb74e-429c-4328-bf8b-1961b3ff1a80.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"LaSalle\", \"host_response_rate\": 100, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 5, \"host_total_listings_count\": 5, \"host_verifications\": [\"email\", \"phone\", \"reviews\"]}', 'address': '{\"street\": \"Montr\\\\u00e9al, Qu\\\\u00e9bec, Canada\", \"suburb\": \"LaSalle\", \"government_area\": \"LaSalle\", \"market\": \"Montreal\", \"country\": \"Canada\", \"country_code\": \"CA\", \"location\": {\"type\": \"Point\", \"coordinates\": [-73.6332, 45.43943], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 30, \"availability_60\": 60, \"availability_90\": 90, \"availability_365\": 365}', 'review_scores': '{\"review_scores_accuracy\": 2, \"review_scores_cleanliness\": 2, \"review_scores_checkin\": 2, \"review_scores_communication\": 2, \"review_scores_location\": 2, \"review_scores_value\": 2, \"review_scores_rating\": 40}', 'reviews': '[{\"_id\": \"365682443\", \"date\": 1546318800000, \"listing_id\": \"30104792\", \"reviewer_id\": \"195976319\", \"reviewer_name\": \"Feroz\", \"comments\": \"I wish I had spent a little more money and gone to a motel because this was an absolute nightmare of a house. There was dirt literally everywhere and it looked like the common areas, bathroom, and room hadn\\\\u2019t been swept in ages. The bathroom had grime and hair on the sink, floor and in the bathtub. The room was supposed to be two queen beds but instead was one twin and one queen and was super cramped and uncomfortable. The room was also supposed to have a \\\\u201cview\\\\u201d according to the listing but all we saw were the neighbors\\\\u2019 backyard as well as a huge pile of random trash. The soap dispenser in the bathroom had soap so diluted with water that we ended up using hand sanitizer to clean our hands. The listing also said it was close to downtown but it ended up being a 45-1 hour trip to downtown one way (bus and then train), and it was super inaccessible. Overall I\\\\u2019m really disappointed because the listing looked lovely and instead it ended up being super gross and really uncomfortable for all of us. Additionally there were a ton of people there and the lock on the door for our room was super broken down and could easily have been opened. I\\\\u2019m also pretty sure that mould/dust in the house is the reason my friend got sick the first night we were there.  \\\\n\\\\nI have stayed in cheaper places that have been nicer than this and it was so awful to have this trip ruined for us. Our friend group was meeting for the first time in over 8 months and it was such a let down as the host seemed really nice and down to earth. We definitely learned our lesson to do more research and to plan further in advance to book a better place for New Years in the future.\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[0.0087131597, 0.3087266386, -0.0964476615, -0.0669194907, 0.1173319966, -0.1665966809, 0.086722374, 0.4590649009, 0.2897408903, -0.1544781178, 0.2854067385, -0.0510558411, 0.4696467221, -0.3689635396, 0.3675877452, 0.0054137995, -0.2415781766, 0.2203628421, 0.1895287782, 0.0515499972, 0.1919480264, -0.2030119747, 0.249973014, 0.0406546369, -0.0685396567, 0.1578861773, -0.0733809099, -0.595286727, 0.2316341549, -0.0476222448, 0.4949313402, 0.6323911548, -0.3126279414, 0.0821399763, -0.4169414639, 0.4399569333, -0.0552489683, 0.026208695, -0.1228978261, 1.0988657475, -0.3570894003, 0.0187279209, -0.1041573137, -0.0130204074, -0.2048081458, -0.5983131528, -0.2823227048, 0.3279002607, 0.1365908682, 0.293061763, 0.3089332283, 0.2111629099, 0.081511125, -0.094217971, -0.3981106281, -0.2627288699, -0.1727879345, -0.0233033951, -0.0018610135, 0.3391076922, -1.4292603731, -0.2940623462, 0.1225190982, -0.1401946843, -0.2410475612, -0.1591062844, 0.8402563334, 0.3864677548, 0.2241325974, 0.0170251578, 0.00985891, -0.009158059, 0.1126279905, 0.2719528675, 0.0580085889, 0.1361515075, -0.1054801345, 0.0510322452, 0.0086921081, -0.4975850284, 0.2089439631, -0.069592692, -0.0722439364, 0.656088531, 0.3341910839, 0.0256420504, 0.5607144237, -0.4029604495, -0.2382489443, -0.406386286, 0.2946088314, -0.2408376634, -7.3814001083, -0.0643983632, -0.209194839, 0.3202933073, 0.807333529, 0.1154819429, 0.182734251, 0.2429952323, 0.0029309466, 0.3034316599, -0.3545795381, -0.1250625551, 0.3126533628, -0.0722367913, -0.7878907919, 0.2470676005, 0.2950665057, 0.0616634451, -0.2932725251, 0.3719895184, -0.0867528319, 0.3275797069, -0.1720134467, -0.0732217431, 0.0469984338, 0.0047199018, 0.4511741698, 0.2929455042, 0.15383479, 0.0344798341, 0.1928770244, 0.0408457294, -0.1070331484, -0.2809849083, 0.1236862913, -0.432941258, -0.0341565311, -0.1076848954, 0.2614488602, 0.3981456161, -0.3941384852, 0.9058333039, 0.2332912982, 0.4331364632, -0.1066410914, 0.5422659516, -0.1027099639, 0.3368598521, 0.3953680694, 0.1647342145, 0.5184728503, 0.3749254942, 0.0332119949, 0.2060611546, -0.1420429796, -0.414906919, -0.1520182937, 0.0492876098, 0.0485379472, 0.0316226706, 0.8616147637, -0.2951617539, 0.3912798464, -0.0243016053, 0.013491923, -0.1786512583, 0.1144237518, -0.3361517787, 0.1472538263, -0.4519759715, 0.0840991884, -0.0158431567, 0.1176095083, 0.1342675239, -0.0847330242, 0.1846175939, 0.0146619789, -0.183891803, -0.111066699, 0.09125413, -0.0165000856, -0.0383719876, 0.0038356883, 0.2562685013, 0.9297307134, 0.0399527214, -0.4834015369, 0.0824832618, -0.0756136551, -0.3027047813, 0.2335678786, -0.3643769324, -0.2616147697, 0.2554442883, 0.191571027, -0.0952179953, 0.1213712096, -0.0239444263, -0.2138979584, -0.2318549156, 0.0913313925, -0.1152945012, 0.2044678777, -0.1504226923, 0.1957125366, -0.0211993232, -0.9899336696, -0.1360519975, 0.3001494706, 0.1466243714, -0.0941517651, 0.0859374031, 0.4368960857, 0.03337707, 0.064840734, 0.0336746871, -0.0513269752, 0.2873356342, -0.3250787556, 0.6446890831, -0.0550217107, 0.1198315024, 0.0422508791, -0.6399012804, 0.1424319446, 0.2680268884, 1.4176570177, -0.2855177522, -0.0831143558, 0.4350849688, -0.20688878, -0.3852417469, -0.0129499678, 0.1180446446, -0.1220332533, 0.1823897362, -0.3035959005, 0.0560395382, 0.0681687742, 0.248410061, 0.0503316075, 0.5450854301, 0.2209971249, 0.7950102091, -0.0767294392, 0.1819058955, -0.3505471647, 0.209254086, -0.0566805527, 0.0300040767, 0.0789228976, -0.0439238995, -0.2168816477, 0.9719731212, -0.1201313585, -0.0864691362, 0.1438076943, 0.1200009882, 0.3339183033, -0.4484075904, 0.2933944762, -0.0574935861, -0.1381185949, 0.2616750896, 0.1026837528, 0.3332834244, 0.6589193344, -0.0722313076, 0.0560983196, 0.3434185982, 0.3966342211, 0.828354001, -0.3419497609, -0.5721539855, -0.1842368543, 0.1956673563, -0.0543695837, 0.3923288584, -0.1785305589, 0.2310208827, 0.2705766857, 0.1588563025, 0.2409881204, 0.045501627, -0.1668832302, -0.0238160249, -0.0226109959, -0.2395197153, 0.2980213463, 0.5108668208, 0.1274812073, 0.1288464665, 0.2831356227, -0.0815863609, 0.9121878147, -0.0077936472, -0.0139129404, -0.4150970578, 0.4286291003, -0.1344895065, 0.3812022805, 0.0515977517, -0.4295915961, 0.1802729219, -0.2357324958, 0.0894462839, -0.0696362555, -0.2323539853, -0.2395325452, 0.221670568, -0.2656365037, -0.4655920565, 0.7793779373, -0.3077155948, 0.0014950503, -0.4018220901, 0.6661109924, 0.1216943711, -0.1918688715, 0.2270762473, 0.90490973, -0.0013707168, -0.2471050769, 0.2353613377, 0.621488452, 0.9619912505, -0.0009955429, -0.1839095354, 0.1130869687, 1.6457654238, -0.1885524392, 0.2271868587, 0.2662683725, -0.010388555, 0.2222096324, -0.4788481295, 0.2301119417, 0.1892809272, 0.0134985596, -0.3070843816, 0.2983486354, -0.692476511, -0.5125385523, 0.145054698, 0.1712953746, 0.2402451336, -0.2263351232, 0.3781630695, -0.1790316999, -0.7433581948, -0.1466355622, -0.1812521666, -0.2092121094, -0.2599362135, -0.2493156195, 0.1471805423, 0.1973813474, -0.6109083891, 0.7207445502, 0.2279732078, -0.104049243, 0.2357980758, 0.1196252853, -0.8928000331, 0.0300499946, 0.5876648426, -0.1018871218, -0.1181044951, -1.2712432146, 0.0555443987, -0.0720012933, 0.44822824, -0.4737504721, 0.4043300152, -0.0087917745, -0.4324485362, -0.0415574685, 0.0342851765, 0.149513647, 0.1345567107, -0.2637580335, -0.1772202104, 0.1381409168, 0.5743621588, 1.2489147186, -0.1596563905, 0.2492544055, 0.2417871058, -0.0758709759, -0.3223493695, -0.1412379146, 0.1743490845, 0.2755281329, -0.7658397555, -0.0651617795, -0.265383184, 0.1389195025, 0.2403477579, -0.4273844063, 0.6185761094, -0.3723655045, 0.1966599524, -0.330529958, 0.0931155384, -0.0528663844, -0.0652609766, 0.1823382378, 0.1786832511, 0.2994839549, 0.1845416129, 1.5713065863, 0.2745104134, -0.0825947002, 0.0755056292, -0.0375615582, -0.1294188648, -0.8250491023, -0.0517890155, 0.1319655478, 0.4264459908, 0.1041415632, 0.0570227467, -0.3753731847, -0.4335963428, -0.2069931179, 0.2388263345, -0.3778446913, 0.0421822853, -0.0629177094, 0.1733497679, 0.4677535295, 0.46030882, -0.3025812209, 0.4757867157, -0.1583559513, 0.3667739332, -2.0504369736, 0.1882988811, 0.0182618946, -0.3281486332, 0.8391265273, 0.1826014072, 0.1111886874, -0.2568766773, 0.0725941882, -0.1435123682, -0.0751155615, 0.286762923, -0.4887376428, 0.0900786817, 0.2651269436, 0.4729520679, 0.2159009576, -0.0535720885, -0.2547558546, 0.0618954189, 0.038335748, -0.2312321514, 0.6065238118, 0.1614649594, 0.0785366893, 0.504627049, 0.3070814908, 0.0426609553, 0.5949465632, -0.1837699413, 0.4706873596, 0.2063554376, -0.1087655574, 0.1847458184, -0.010036502, -0.2983679771, 0.1220055223, -0.4577333629, -0.3015633225, -0.1162549332, -0.494130224, -0.3066190481, 0.2148017436, 0.0188383907, 0.3792521656, -0.8130715489, -0.1798872054, -0.4672985971, 0.1650904566, -0.3949325383, -0.0192587487, 0.1821415573, -0.0667862743, -0.6882886291, 0.219771415, 0.0542834438, 0.8225620985, 0.1127012372, -0.2661329806, 0.396173954, -0.1167109013, 0.0773602724, -0.1842371672, 0.0874487236, 0.5003326535, -0.4608216286, -0.2168773413, -0.2362791747, 0.2771558464, 0.149546966, -0.190461576, 0.1775102615, -0.1223850027, -0.2622747123, 0.0818929896, 0.1634190828, 0.2843185067, -0.3552679121, -0.2757597268, -0.0187404249, -0.2880458236, 0.7036882043, 0.0217038132, -0.1720914841]'}, hash='540ac216ba4eb72474e679ce9cb0a356e3a7cff7ff204961c6b63f14d9136fa9'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='9d98a92c-b5de-4dcd-a98b-64cb3c10f3ba', node_type=<ObjectType.TEXT: '1'>, metadata={'_id': 29997747, 'listing_url': 'https://www.airbnb.com/rooms/29997747', 'name': 'Cozy, New Ren. comfort for 2 pax, City hotspot', 'summary': 'If you are looking for a cozy and comfortable place for your visit to Hong Kong, somewhere close to the Mongkok MTR station and easy access to everything else, and with an affordable price, this could be your perfect choice. Our place is suitable for backpackers, students, tourists, and businessman.', 'space': 'The space The building is located in Shanghai Street at Mong Kok, 3 minutes walk from a big shopping mall, the Langham Place and a minute walk to Mongkok MTR Exit A2. Famous attractions such as “Ladies Market”, “Sneaker Street”, \"Goldfish Market\" and Temple Street are very close to our building. McDonald’s and 7-11 is next to our building! For staying in the most convenient location in Hong Kong, it costs you less than $200 per person per night.', 'description': 'If you are looking for a cozy and comfortable place for your visit to Hong Kong, somewhere close to the Mongkok MTR station and easy access to everything else, and with an affordable price, this could be your perfect choice. Our place is suitable for backpackers, students, tourists, and businessman. The space The building is located in Shanghai Street at Mong Kok, 3 minutes walk from a big shopping mall, the Langham Place and a minute walk to Mongkok MTR Exit A2. Famous attractions such as “Ladies Market”, “Sneaker Street”, \"Goldfish Market\" and Temple Street are very close to our building. McDonald’s and 7-11 is next to our building! For staying in the most convenient location in Hong Kong, it costs you less than $200 per person per night. ★ Free wireless wi-fi network, ★ Air conditioning (cooling only, non-heating) ★ Towels, bath towels ★ Electric kettle, cup ★ Hair dryer For the license requirement, no room is equipped with cooking utensils, cooking is not allowed inside any room.  ', 'neighborhood_overview': 'Mong Kok is simply the must-go place for your Hong Kong Trip; It is just so accessible to live here, there are a wide variety of shops, trendy and traditional, computer, sports, electronics and several new and huge malls built recently with worldwide famous brands. Many local food stalls nearby and countless restaurants, both east and west, are within 15 mins walk. There are various convenient stores and supermarkets around. They open every day (some 24 hours). You do not need to worry about your daily needs.', 'notes': \"This room is newly furnished and renovated. We offer good quality furniture and bed: Bunk bed for 3 adult or 2 adults and 2 small kids aged under 7 yrs old Note: This is a private room with private toilet and it has a big window. The upper bunk is 3' x 6' The lower bunk is 3' x 6'\", 'transit': 'Mong Kok is the travel hub in West Kowloon, there are two lines of MTR, and numerous bus routes passing through. Literally, you can go anywhere in Hong Kong within 1 hour. 10 minutes to Tsim Sha Tsui 15 minutes to Central, Exhibition Centre in Wan Chai, or Causeway Bay 40 minutes o Disneyland or Ocean Park Direct Airport Bus A21 for only 35 minutes from Airport', 'access': '★ Free wireless wi-fi network, ★ Air conditioning (cooling only, non-heating) ★ Towels, bath towels ★ Electric kettle, cup ★ Hair dryer For the license requirement, no room is equipped with cooking utensils, cooking is not allowed inside any room.  we do not offer any luggage storage service.', 'interaction': 'I may not be available on the spot all the time but I am responsive to (Hidden by Airbnb) , and you can always reach me when needed.', 'house_rules': '- Quiet hours after 11:00PM', 'property_type': 'Boutique hotel', 'room_type': 'Private room', 'bed_type': 'Real Bed', 'minimum_nights': 1, 'maximum_nights': 1125, 'cancellation_policy': 'strict_14_with_grace_period', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1547960400000, 'last_review': 1551416400000, 'accommodates': 2, 'bedrooms': 1.0, 'beds': 2.0, 'number_of_reviews': 7, 'bathrooms': 1.0, 'amenities': '[\"Wifi\", \"Air conditioning\", \"Washer\", \"Dryer\", \"Smoke detector\", \"Fire extinguisher\", \"Essentials\", \"Shampoo\", \"Lock on bedroom door\", \"Hangers\", \"Hair dryer\", \"Laptop friendly workspace\", \"Self check-in\", \"Smart lock\", \"Private living room\", \"Window guards\", \"Hot water\", \"Bed linens\", \"Refrigerator\", \"Long term stays allowed\"]', 'price': 345, 'security_deposit': 1000.0, 'cleaning_fee': 80.0, 'extra_people': 39, 'guests_included': 1, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/7e4b81fc-b186-4fda-b84e-d85567861e70.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"217504943\", \"host_url\": \"https://www.airbnb.com/users/show/217504943\", \"host_name\": \"Angel & Tommy\", \"host_location\": \"Hong Kong, Hong Kong\", \"host_about\": \"I am a very easy going type of guy and I like to meet new friends whether is from local or overseas. I like to offer help in any way I can. I am a busy worker but I also enjoy life, I like to eat seafood and hung out with friends when free.\", \"host_response_time\": \"within an hour\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/user/0fc90e84-4b93-4c31-81e7-db0599a0bd39.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/user/0fc90e84-4b93-4c31-81e7-db0599a0bd39.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"Mong Kok\", \"host_response_rate\": 99, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 12, \"host_total_listings_count\": 12, \"host_verifications\": [\"email\", \"phone\"]}', 'address': '{\"street\": \"Hong Kong, Kowloon, Hong Kong\", \"suburb\": \"Yau Tsim Mong\", \"government_area\": \"Yau Tsim Mong\", \"market\": \"Hong Kong\", \"country\": \"Hong Kong\", \"country_code\": \"HK\", \"location\": {\"type\": \"Point\", \"coordinates\": [114.16892, 22.32131], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 12, \"availability_60\": 38, \"availability_90\": 68, \"availability_365\": 68}', 'review_scores': '{\"review_scores_accuracy\": 10, \"review_scores_cleanliness\": 10, \"review_scores_checkin\": 10, \"review_scores_communication\": 10, \"review_scores_location\": 10, \"review_scores_value\": 9, \"review_scores_rating\": 94}', 'reviews': '[{\"_id\": \"402972909\", \"date\": 1547960400000, \"listing_id\": \"29997747\", \"reviewer_id\": \"234042013\", \"reviewer_name\": \"\\\\u709c\", \"comments\": \"\\\\u8fd8\\\\u4e0d\\\\u9519 \\\\u633a\\\\u68d2\\\\u7684\"}, {\"_id\": \"412586493\", \"date\": 1550293200000, \"listing_id\": \"29997747\", \"reviewer_id\": \"242080316\", \"reviewer_name\": \"\\\\u709c\\\\u8d85\", \"comments\": \"\\\\u5728\\\\u9999\\\\u6e2f\\\\u8fd9\\\\u4e2a\\\\u4ef7\\\\u4f4d\\\\u6027\\\\u4ef7\\\\u6bd4\\\\u8fd9\\\\u4e48\\\\u9ad8\\\\u7684\\\\u4f4f\\\\u5bbf\\\\u5df2\\\\u7ecf\\\\u5f88\\\\u5c11\\\\u4e86,\\\\u5730\\\\u7406\\\\u4f4d\\\\u7f6e\\\\u4e5f\\\\u4e0d\\\\u9519,\\\\u552f\\\\u4e00\\\\u4e0d\\\\u597d\\\\u7684\\\\u662f\\\\u70ed\\\\u6c34\\\\u5668\\\\u52a0\\\\u70ed\\\\u4f7f\\\\u7528\\\\u6709\\\\u70b9\\\\u9ebb\\\\u70e6,\\\\u5176\\\\u4ed6\\\\u90fd\\\\u4e0d\\\\u9519,\\\\u4ee5\\\\u540e\\\\u53bb\\\\u7684\\\\u8bdd\\\\u4f1a\\\\u4f18\\\\u5148\\\\u9009\\\\u62e9\"}, {\"_id\": \"413731890\", \"date\": 1550466000000, \"listing_id\": \"29997747\", \"reviewer_id\": \"200602007\", \"reviewer_name\": \"\\\\u738b\\\\u51ac\\\\u840d\", \"comments\": \"\\\\u8fd9\\\\u5bb6\\\\u5e97\\\\u4f4d\\\\u7f6e\\\\u5f88\\\\u597d,\\\\u771f\\\\u7684\\\\u5f88\\\\u65b9\\\\u4fbf\\\\uff61\\\\u9644\\\\u8fd1\\\\u597d\\\\u51e0\\\\u4e2a\\\\u65fa\\\\u89d2\\\\u5730\\\\u94c1\\\\u53e3,\\\\u597d\\\\u51e0\\\\u5bb6\\\\u5c48\\\\u81e3\\\\u6c0f,sasa\\\\uff61\\\\u623f\\\\u4e1c\\\\u56de\\\\u590d\\\\u5f88\\\\u5feb,\\\\u5f88\\\\u8010\\\\u5fc3\\\\uff61\\\\u5b89\\\\u5168\\\\u6307\\\\u6570\\\\u4e94\\\\u9897\\\\u661f\\\\uff61\"}, {\"_id\": \"415186745\", \"date\": 1550811600000, \"listing_id\": \"29997747\", \"reviewer_id\": \"142758809\", \"reviewer_name\": \"\\\\u5b9b\\\\u8389\", \"comments\": \"\\\\u633a\\\\u597d\\\\u7684,\\\\u5b89\\\\u5168\\\\u6027\\\\u80fd\\\\u4e5f\\\\u6bd4\\\\u8f83\\\\u53ef\\\\u9760,\\\\u697c\\\\u4e0b\\\\u9644\\\\u8fd1\\\\u4e0d\\\\u8fdc\\\\u5c31\\\\u6709\\\\u5403\\\\u65e9\\\\u9910\\\\u5403\\\\u996d\\\\u7684,\\\\u623f\\\\u4e1c\\\\u5927\\\\u5927\\\\u4e5f\\\\u4f1a\\\\u53ca\\\\u65f6\\\\u56de\\\\u590d\\\\u0295 \\\\u2022\\\\u1d25\\\\u2022\\\\u0294\"}, {\"_id\": \"416044531\", \"date\": 1550984400000, \"listing_id\": \"29997747\", \"reviewer_id\": \"243848539\", \"reviewer_name\": \"\\\\u79c0\\\\u683c\", \"comments\": \"\\\\u4e0d\\\\u9519\"}, {\"_id\": \"417360337\", \"date\": 1551243600000, \"listing_id\": \"29997747\", \"reviewer_id\": \"245213540\", \"reviewer_name\": \"Biyu\", \"comments\": \"\\\\u9ebb\\\\u96c0\\\\u867d\\\\u5c0f ,\\\\u4e94\\\\u810f\\\\u4ff1\\\\u5168\\\\uff61\"}, {\"_id\": \"417999045\", \"date\": 1551416400000, \"listing_id\": \"29997747\", \"reviewer_id\": \"242614411\", \"reviewer_name\": \"\\\\u52e4\", \"comments\": \"\\\\u5f88\\\\u597d \\\\u6bd4\\\\u9884\\\\u671f\\\\u8981\\\\u597d \\\\u5c31\\\\u662f\\\\u5395\\\\u6240\\\\u5c0f\\\\u4e86\\\\u70b9 \\\\u5176\\\\u4ed6\\\\u65b9\\\\u9762\\\\u90fd\\\\u53ef\\\\u4ee5\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[-0.3158158362, 0.3233007491, 0.2216365486, 0.4568406343, 0.3416933417, -0.2795549333, -0.1099459678, 0.0448980778, 0.0037740525, -0.152359277, 0.5868084431, -0.2440771312, 0.0037831515, -0.7279684544, 0.4068863988, 0.2430476546, -0.5859074593, -0.0582512841, 0.1788151264, -0.0025836132, 0.2864378095, 0.3891758919, 0.2327153385, 0.0978234857, -0.5447791815, 0.5689457059, -0.0167037696, -0.5191369057, 0.0288978796, -0.1649549901, -0.1675025821, 0.3599556088, -0.0141320936, 0.031959068, 0.5124676228, 0.3246706426, -0.1121435612, -0.6040832996, 0.22215949, 0.4833624065, 0.0603563078, -0.139917478, -0.1536374539, -0.5967227817, -0.1488204747, -1.3571993113, 0.2274641544, 0.125489071, -0.2232022882, 0.0197814014, 0.3413453996, -0.0896456391, -0.0184559338, 0.5025225282, -0.1189369187, 0.3874360621, -0.5735105276, -0.5507791638, -0.0819841623, 0.4128676355, -0.5223092437, -0.3372384906, -0.2564714253, 0.0418594666, -0.0921016335, -0.0758328438, 0.7420341969, 0.2376463711, 0.0205465127, 0.3373140693, 0.0406998545, 0.3197378218, -0.2518162727, 0.0711882487, 0.150803715, 0.0760058239, -0.181699127, -0.0299923159, -0.2798519731, -0.9799943566, 0.0945798978, -0.160748899, -0.3866907358, 1.0074830055, 0.4550831914, 0.3945181966, 1.2282233238, -0.3582764864, -0.7367947698, -0.2202841192, -0.1170626357, 0.0440538675, -5.8169875145, 0.4967951775, 0.0904858783, 0.4570301771, 0.3062467873, 0.252078712, 0.5386468172, -0.8302381635, 0.0803460777, 0.1944204569, -0.1015426144, 0.4299875796, 0.283449769, -0.0529141128, 0.1318406165, 0.1197266877, 0.3524529338, 0.1336794943, -0.1248922944, -0.4093644321, -0.2763195932, -0.3602978289, 0.0265902318, -0.6815069914, 0.1796984076, 0.1239786372, -0.1449513137, 0.1703713387, 0.1480079889, 0.5135015845, 0.4159770608, 0.1096940935, -0.1908965111, -0.463365078, 0.368861258, -0.3390793502, -0.4224042594, 0.1175627559, 0.0690737441, 0.0601352043, -0.0076588467, 0.7980198264, 0.1200659499, -0.2230406255, 0.1673384309, 0.4290993512, 0.0518382192, 0.2060372829, 0.201401487, -0.0505505539, 0.1377618909, 0.0166335888, -0.2082919329, 0.4145846069, 0.0522944368, 0.1106268838, -0.1455549002, 0.2613557279, -0.3644368351, -0.0781874433, 0.7518307567, -0.2000440508, 0.336795181, -0.173081696, -0.1168531924, -0.5672436953, 0.0173929911, 0.0766510591, 0.032159742, -0.3451489806, 0.0163095854, 0.0951428413, 0.2522565722, 0.1057723612, 0.8203007579, -0.4910795391, 0.4081795812, -0.6892234683, 0.1929036975, -0.1344458163, -0.1213796139, 0.2653743029, -0.4207774103, 0.0968617499, 0.8329356313, -0.3009214997, -0.2733801603, -0.1020936593, -0.1169422865, 0.0222777911, -0.0261608735, -0.2959120274, -0.2697953284, 0.3362632096, 0.1646107584, -0.0431573652, -0.5525557995, 0.2002973109, -0.0255784225, 0.0862284675, 0.0701532736, -0.5156066418, 0.2301077247, -0.1660320163, 0.3963571787, 0.0811444074, -0.9976867437, 0.1015348136, -0.2998350561, -0.3793753088, -0.1305107474, 0.0605858043, 0.5543877482, 0.0150531242, 0.208698526, 0.20681867, -0.2719561458, 0.6660065651, -0.2532458305, 0.3037358224, 0.0166223347, 0.1863358319, -0.5057032108, 0.0468070358, 0.724897325, 0.1408478767, 0.4795784652, -0.1804210842, -0.3185746074, 0.1890113652, -0.3632360101, -0.5094865561, -0.2705252767, 0.2039707154, 0.0485447049, 0.2111206353, -0.0285606906, 0.0533751324, 0.2774709463, 0.0065514911, 0.2547849417, 0.8297880888, -0.180975005, 0.2678935528, 0.3003636301, -0.039140068, -0.0337558948, -0.0814543515, -0.2025851905, -0.2841783762, -0.4995523691, -0.5667170882, 0.574719429, 1.1916328669, -0.0215722714, -0.3078604341, -0.3302677572, 0.2289632708, -0.0706707835, 0.0973176211, 0.2862367034, -0.1680462807, 0.2490326762, 0.0927664936, -0.7665193081, -0.1290781796, 0.3129737973, -0.4756698608, -0.0108228326, -0.147499904, 0.6841800809, 0.7661072016, 0.4328823686, -0.3794347048, -0.2022396624, -0.1222823709, 0.0524836183, 0.274723351, 0.0761178285, -0.3324353099, -0.2154753506, -0.6086939573, -0.2128244936, -0.1566265821, 0.2740844488, -0.4760979712, -0.0096536875, -0.3239465356, 0.2681340873, -0.3054705262, -0.5073376894, 0.6729696393, 0.5842432976, -0.2489464879, -0.1133007631, 0.3155860007, -0.0944885314, -0.0216638595, -0.0310180485, 0.1630259901, 0.2595257163, 0.1329768002, -0.453304112, 0.060856767, -0.027671285, 0.3351587653, -0.3868874311, -0.1290231645, -0.7222147584, 0.1541156769, -0.3209859431, 0.2574382424, 0.0383905284, -0.5450444818, 0.0726619512, 0.2742100358, 0.1716485023, -0.0849756673, 0.1092587113, 0.5674053431, 0.797642529, 0.1884080768, -0.0757954866, -0.2042325437, 0.5488975644, -0.0356030017, 0.4266503155, -0.1760681272, 0.3934499919, 0.5684318542, -0.3822975159, 0.3941679895, 0.1994796395, 0.4042281508, 0.1636261195, -0.4515720904, -0.1636054069, -0.238999024, 0.0555058941, 0.0076933634, 0.2800477743, -0.1155014336, 0.0358466804, 0.1212733313, -0.0154440477, 0.0308367796, 0.1598684788, 0.2355672419, -0.1678484976, -0.1799512655, -0.0378129929, 0.4063898325, 0.2607244849, -0.1568150967, 0.3747623563, 0.1467244625, 0.4203603864, -0.549369812, 0.6386401653, 0.1246835813, -0.0216439068, 0.2318382412, -0.3560654521, 0.2394337058, -0.5401765704, 1.1607348919, -0.5029149055, -0.2872592807, -0.959872365, -0.009711992, -0.1660494059, 0.073608622, 0.468634814, 0.1689570099, -0.2579350173, -0.6806343794, -0.4355932474, -0.467032373, 0.0527719259, -0.0378271788, 0.0490968153, -0.137890622, 0.3550528586, 0.3363477588, 0.3690587878, 0.1848447472, -0.1155796498, 0.1358575076, -0.1033499688, 0.1983329505, 0.1579177082, 0.0056263395, 0.3011241257, -0.0949804485, 0.2705532312, -0.3370699883, -0.2575276494, -0.8724218607, 0.386708051, 0.4216324389, -0.1485495269, 0.5168042183, -0.702986598, 0.367998153, -0.1198929399, -0.2260880023, 0.1235650331, 0.0640530288, -0.1273714602, 0.1117176786, 1.0714662075, -0.3699950278, 0.2054855227, -0.4005037546, 0.0895129368, 0.0180446897, -0.249431029, -0.1546498537, -0.0383591875, 0.3283779323, 0.0989254043, -0.022682, 0.1065071747, 0.4121985435, -0.4412537217, -0.1074997261, -0.4696683884, -0.4121329486, 0.1309817582, -0.1128660738, 0.3287155628, 0.3114117384, -0.4727011323, 0.0772248134, -0.1013083607, 0.0357327573, -1.158462882, -0.1128044724, -1.0201721191, 0.1225084141, 0.5786705017, 0.1879722029, 0.0711387992, -0.1814436615, 0.5695844293, 0.1520086974, 0.2901573479, 0.4612415731, -0.0251456648, -0.0109055229, 0.0799590126, 0.3913990855, 0.2022513449, -0.0807531998, 0.1056476533, -0.3482739925, 0.5230919123, -0.6975764036, -0.3645024598, 0.1002149731, -0.1703148931, 0.9040458202, 0.175940603, -0.0501305982, 0.4543680847, 0.0197642483, 0.5007976294, 0.4770755768, -0.5478366613, 0.2357888222, -0.0413305387, -0.3152391315, -0.3534832895, -0.0594666563, -0.098273471, -0.1386496127, -0.2269812822, 0.1452744752, 0.120427303, -0.230604887, 0.1408036351, -0.8252517581, -0.1027704179, -0.3412083089, 0.4772609472, 0.552297771, 0.1499296427, 0.0105411783, -0.014504713, -0.0845909864, -0.4389210939, -0.1483100653, 0.8401288986, 0.7083355188, 0.1559975445, 0.523685813, -0.096753791, 0.4348963499, -0.199838981, 0.0056611309, 0.6161967516, -0.8939759135, -0.2444265038, 0.0073470473, 0.4145923853, 0.0365709178, -0.1529447287, -0.2637856305, -0.3077575862, 0.0049692253, 0.3652532399, 0.2022253275, 0.511865437, 0.5282325745, -0.0350109898, 0.4184099138, -0.1483718455, 0.2758470774, -0.1358992159, -0.3023008108]'}, hash='7b66c5593f4e240ea5290c13073a9eed72e7de54205f3759b8677df667aee599'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='60750b22-d635-43de-be11-1b3094c0b124', node_type=<ObjectType.TEXT: '1'>, metadata={}, hash='b5d8ae50b4a79b0e93e7bc78928e29824e48f46f8be49108536bd350bf480ca8')}, text=\"You need to feel at home? Come to our home!! Cozy Room With Wifi In A Family Resident. Possibility of breakfast at $6 per day, per guest if desired. Home near restaurants, pubs, malls etc. Home away from home! Close to bus stops, Metro Angrignon, walking distance to nice restaurants e.g buffet, grocery stores etc. Close to Malls, downtown, movie theatres, recreation parks, Airport. Decent, peaceful, clean area in Lasalle. Parking available. 2 bedrooms in same house for hosting guests. I'm a down to earth peace-loving person with lovely family who will make your stay fun! Pick up from Trudeau Airport/bus stations is available for $50 fees. Please message host to discuss, thanks. Close to airport, restaurants, malls, parks etc. Easy access to transportations, downtown Montreal, historical sights, tourist centres, hospitals, etc. We love to make our guests very comfortable, treating them as family members. Depending on the guests, my children and I can interact or give space. Calm, safe,\", start_char_idx=0, end_char_idx=999, text_template='Metadata: {metadata_str}\\n-----\\nContent: {content}', metadata_template='{key}=>{value}', metadata_seperator='\\n'), score=0.7689720392227173),\n", " NodeWithScore(node=TextNode(id_='532ae40e-714c-49e5-beef-fdcbcfbc0268', embedding=None, metadata={'_id': 24871838, 'listing_url': 'https://www.airbnb.com/rooms/24871838', 'name': 'HOMESTAY NEAR MONTREAL AIRPORT', 'summary': 'Quiet home, near Airport, Good for 1-4 persons. Close to all.......5 mins walk to bus #208, 20 mins walk to Sunnybrook train station, 20 mins drive to Montreal Airport, Via-Rail & Downtown. Highway 13, 20 & 40 nearby. Park, Grocery, Pharmacy, Hindu & Sikh Temple walking distance. Nice & Quiet neighborhood ! Vegetarian food on request.', 'space': '', 'description': 'Quiet home, near Airport, Good for 1-4 persons. Close to all.......5 mins walk to bus #208, 20 mins walk to Sunnybrook train station, 20 mins drive to Montreal Airport, Via-Rail & Downtown. Highway 13, 20 & 40 nearby. Park, Grocery, Pharmacy, Hindu & Sikh Temple walking distance. Nice & Quiet neighborhood ! Vegetarian food on request.', 'neighborhood_overview': '', 'notes': '', 'transit': '', 'access': '', 'interaction': '', 'house_rules': \"- No shoes in the house. No smoking, no parties, no events, no noise pls. - Respect people in the house & neighborhood. No pets pls. - Only light cooking in the shared kitchen. - As a courtesy, pls wash your dishes, leave sink & kitchen counter clean after use. - No alcohol or drugs in or around the house. - Restrict your shower to maximum 10 mins so that there is enough hot water for other guests. - For Hygeine & sanitation reasons, pls don't keep your luggage on beds. Don't drag on the floors pls. DO NOT HESITATE to approach host, if any problem occurs. - Thank you for choosig to stay with us. - Pls don't consume food or beverage in the rooms. Enjoy your meals in dining area.\", 'property_type': 'Townhouse', 'room_type': 'Shared room', 'bed_type': 'Real Bed', 'minimum_nights': 1, 'maximum_nights': 1125, 'cancellation_policy': 'strict_14_with_grace_period', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1526788800000, 'last_review': 1535947200000, 'accommodates': 4, 'bedrooms': 1.0, 'beds': 2.0, 'number_of_reviews': 5, 'bathrooms': 2.0, 'amenities': '[\"Air conditioning\", \"Heating\", \"Family/kid friendly\", \"Washer\", \"Dryer\", \"Shampoo\", \"Hangers\", \"Hair dryer\", \"Iron\"]', 'price': 46, 'security_deposit': 200.0, 'cleaning_fee': 19.0, 'extra_people': 20, 'guests_included': 1, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/5daca350-4912-4f69-bf81-7ca301210136.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"71573275\", \"host_url\": \"https://www.airbnb.com/users/show/71573275\", \"host_name\": \"Neera\", \"host_location\": \"Montreal, Qu\\\\u00e9bec, Canada\", \"host_about\": \"I am sociable & a good host. I am an educated, professional & semi retired lady. I love my family and friends. I am well travelled and well informed. I consider myself a spiritual person. Love to cook. I am very sensitive to human needs. I would love to help people if I can.\", \"host_response_time\": \"within an hour\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/7cff0a21-5f93-4e85-9d48-78a3144933ea.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/7cff0a21-5f93-4e85-9d48-78a3144933ea.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"Dollard-des-Ormeaux\", \"host_response_rate\": 100, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 6, \"host_total_listings_count\": 6, \"host_verifications\": [\"email\", \"phone\", \"reviews\", \"offline_government_id\", \"selfie\", \"government_id\", \"identity_manual\"]}', 'address': '{\"street\": \"Dollard-des-Ormeaux, Qu\\\\u00e9bec, Canada\", \"suburb\": \"Dollard-des-Ormeaux\", \"government_area\": \"Dollard-des-Ormeaux\", \"market\": \"Montreal\", \"country\": \"Canada\", \"country_code\": \"CA\", \"location\": {\"type\": \"Point\", \"coordinates\": [-73.79282, 45.49385], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 30, \"availability_60\": 60, \"availability_90\": 90, \"availability_365\": 365}', 'review_scores': '{\"review_scores_accuracy\": 7, \"review_scores_cleanliness\": 6, \"review_scores_checkin\": 10, \"review_scores_communication\": 10, \"review_scores_location\": 7, \"review_scores_value\": 6, \"review_scores_rating\": 68}', 'reviews': '[{\"_id\": \"266743813\", \"date\": 1526788800000, \"listing_id\": \"24871838\", \"reviewer_id\": \"111653310\", \"reviewer_name\": \"Tathagat\", \"comments\": \"Clean and comfortable room\"}, {\"_id\": \"269592908\", \"date\": 1527393600000, \"listing_id\": \"24871838\", \"reviewer_id\": \"117253059\", \"reviewer_name\": \"Jeresteen\", \"comments\": \"The host was very accommodating and extremely sweet. She took great care of us. The room that we stayed in looked amazing and was clean until we looked behind the bed. There was a lot of dust or sawdust .. not sure which and unfortunately my nephew got allergies as he is allergic to dust. The home needs a bit of TLC. The bathroom was also not in a great shape, there was a hole where the toilet paper role was but it got fixed the very next day, which was great. The unfortunate part is that we didn\\'t realize it was a shared bathroom with other guests which was quite annoying as we are quite picky people . It is mentioned under house rules that it is a  shared bathroom. I wish this was written upfront on the summary page. Also whenever we wanted to go back to the house we had to call the host as we did not have the keys. The host was very accommodating but we felt bad going late. Once again the host was amazing very patient and nice but some things in the house were lacking.\"}, {\"_id\": \"285775322\", \"date\": 1530676800000, \"listing_id\": \"24871838\", \"reviewer_id\": \"196728188\", \"reviewer_name\": \"\\\\ucc44\\\\uc2e0\", \"comments\": \"\\\\uac00\\\\uaca9 \\\\ub300\\\\ube44 \\\\ubcc4\\\\ub85c~~~\"}, {\"_id\": \"310403747\", \"date\": 1534651200000, \"listing_id\": \"24871838\", \"reviewer_id\": \"209792519\", \"reviewer_name\": \"Amrith\", \"comments\": \"Feel like home\"}, {\"_id\": \"318186692\", \"date\": 1535947200000, \"listing_id\": \"24871838\", \"reviewer_id\": \"169600572\", \"reviewer_name\": \"Kiran\", \"comments\": \"Room was okay but too much chaos. host advised privacy but there was constant interference. Air conditioning in the room was leaking and host advised to keep checking so we had to keep an all night to see for water leak. I was conjusted and the value we paid was not even 1% worth what we got. I wont suggest if you are looking something clean and private o lying suitable for a ghetto place. Host was good and responsive but the place didnt do justice.\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[0.028289903, 0.5426813364, -0.1263493299, 0.0880639553, 0.1195924133, -0.3484530747, -0.2910634875, 0.5790982246, 0.1835098863, 0.3143885136, -0.0885362476, -0.2998264134, 0.1567870378, -0.5114456415, 0.2296565473, 0.1135994419, -0.350595057, -0.1110990345, 0.2008510083, -0.0699903965, -0.2177600563, 0.2117584497, 0.3657162189, 0.3446537554, -0.3597181737, 0.2043109983, 0.208236292, -0.4756190777, -0.0638155416, -0.1422024965, 0.189360708, 0.3805072904, 0.0849997178, 0.3461001813, -0.5480548739, 0.5954501629, -0.0452642627, -0.4723055959, 0.0436929278, 0.8003280163, -0.3365020454, -0.0836261958, -0.2246079445, -0.201527521, -0.1204060614, -2.3315246105, 0.1338311434, 0.2543126941, -0.1014514565, -0.0808105916, 0.4599454105, 0.1726036966, 0.4198836982, 0.4249495268, 0.1274706274, -0.1988445371, -0.1569736004, -0.075748533, -0.5405645967, 0.1542513072, -1.0775711536, -0.5395069122, 0.015805196, 0.1981425285, 0.094421722, 0.0276280511, 0.4348567128, 0.2626309991, -0.1738325655, 0.1230541617, -0.0702876672, 0.383765161, 0.1527442485, -0.23851493, 0.1178443655, 0.0280016307, -0.4287573695, 0.2286910266, -0.2804454565, -0.7532225847, -0.0914950818, -0.1909806877, 0.0794506818, 0.6901681423, 0.5214423537, 0.2294345051, 0.7680078149, -0.3327402771, -0.3528374434, -0.0666580945, 0.2052869201, -0.2260334343, -6.1491308212, -0.6932991743, -0.1862934381, 0.2446604967, 0.3239989281, -0.2762842774, 0.3406631052, 0.0331211835, 0.1882238239, 0.1271226555, 0.2411140949, 0.2437815666, 0.5258088708, 0.1317546964, 0.2406836599, -0.2573142946, 0.3292613924, -0.0990086794, -0.3411449194, 0.2984005511, -0.1651508212, -0.0593308024, -0.1912773848, -0.438274771, -0.0906344205, 0.1156560034, 0.2553135157, 0.2341465503, 0.1146052703, -0.2945663333, 0.3626779318, -0.0591760501, -0.0462215357, -0.1600264758, 0.0790427625, -0.3423352242, -0.118657507, 0.1727341861, 0.2024773806, 0.100341171, -0.3118989468, 0.7887333632, -0.0354722328, 0.1856890321, 0.1446823031, 0.8781445622, -0.2801594734, 0.0983140767, 0.1952615231, -0.0133991726, 0.5233959556, 0.1098594815, -0.4775821865, 0.2707497478, -0.0413427949, -0.2478369027, 0.145519644, 0.2029408216, 0.0758287534, 0.3051381707, -0.4521685839, -0.4098881185, 0.286075145, -0.7098713517, 0.0265494119, -0.0101394653, 0.256688714, 0.16969046, -0.1868201494, -0.4713023007, 0.1897083819, -0.0065655261, 0.0999124125, 0.1475352049, 0.3530426919, -0.2598636448, 0.1025258079, -0.5044997334, 0.231235683, 0.0187529251, 0.2514898181, 0.146372512, -0.131714806, -0.0280266143, 1.1765700579, -0.3130852282, -0.5862109661, 0.0229565334, -0.0527637079, -0.0053850189, 0.0131278671, -0.3453762531, -0.0441556983, 0.1188255996, 0.2976155579, 0.3626728952, -0.3556631804, 0.0579638667, -0.0282976739, -0.0714732111, 0.3615851402, -0.3986552358, 0.0215986557, -0.0303606093, 0.5349525213, -0.1762580425, -0.8364406228, -0.2489314824, 0.2986877561, -0.1610969454, -0.0573436581, 0.1166123003, 0.7104568481, 0.410687089, -0.2750972807, 0.3243075013, -0.0081026964, 0.4379435778, 0.0872915834, 0.2718119323, 0.1883122176, -0.0978781804, -0.2305068076, -0.1585019827, 0.4696531296, -0.0810396969, 1.7187594175, 0.077917479, 0.2881379724, 0.4117026329, -0.1115251258, -0.2318728268, -0.3111547232, 0.0711369216, 0.0561151393, 0.0708963573, -0.0073781461, 0.07729505, 0.3781637847, -0.225100413, 0.5094650984, 0.2830577493, -0.004775614, 0.2804560065, 0.1861454099, -0.2616858184, 0.0108465031, -0.0741706192, -0.255440861, -0.0605108105, 0.0850971714, -0.3650119901, 0.4144392312, 0.7378973961, 0.2186967283, -0.1151337773, -0.059429504, 0.0897397771, 0.2029229254, -0.1234460622, 0.5237460136, 0.2215650678, -0.0454240665, 0.2565318644, -0.1553573608, 0.1546132416, 1.563126564, 0.06815698, -0.0997933, -0.0781492591, 0.5405812263, -0.2718766332, 0.1561465859, -0.2457471788, 0.0241587386, -0.0316776633, -0.0747023821, 0.0479849055, 0.2148507833, 0.0995909721, -0.0115843313, 0.095813334, -0.1198509037, -0.2730686367, 0.2043595463, -0.2727225125, 0.1390503198, 0.3135280013, 0.3765451014, -0.0080140121, 0.1631822288, 0.9854659438, 0.5822296143, -0.2478367686, -0.0276659261, -0.2270969301, -0.0532878935, -0.0329822078, 0.3079518378, 0.0929483175, 0.5452842712, 0.3056309223, -0.1484960914, 0.1271563917, -0.6985507011, 0.0676160455, -0.1846180856, -0.2561847568, -0.1525128186, -0.0217330102, 0.060335651, 0.0294332299, 0.2544634938, -0.3957610726, 0.0620008595, -0.1176486909, 0.5863558054, -0.4218372107, -0.0291494504, 0.3851079643, 0.7876068354, 0.2506110072, -0.3599236608, 0.1765229404, 1.0986280441, -0.033419393, -0.0252086725, 0.1241148412, 0.1645638347, 0.665286541, -0.1602624059, -0.066517286, -0.0801196024, -0.0718235373, 0.2776556909, -0.1390406489, 0.0307008736, -0.0447937548, -0.2949569225, 0.1075274944, -0.0965340286, -0.3752008379, -0.1307932436, 0.2048219144, 0.2870424092, -0.2290546894, -0.4168808162, 0.1040272117, 0.2015807629, 0.0473072566, -0.105741933, 0.2785045803, 0.0082127452, -0.074578613, 0.3230136335, 0.2532664537, 0.4702716768, -0.306440562, 0.7499889731, -0.0368955582, -0.2877925634, -0.0195006263, -0.3370005488, 0.9595741034, -0.1999542564, 0.133206293, -0.0038944352, 0.2215911448, -0.1583319753, 0.5310643911, 0.0329700224, 0.3623477221, -0.8074452281, 0.3001114428, -0.2432784885, -0.6039684415, -0.4067654908, -0.3264508247, -0.1280963123, 0.2148363739, 0.021133896, 0.1535920501, 0.1499336362, 0.2566893399, 0.2929673791, 0.2469513416, 0.0530762672, -0.1169853657, 0.0999395698, 0.3284144402, 0.0050406456, 0.3364064395, 0.3483855724, -0.3329791129, 0.2665727437, -0.2109671533, -0.2796326876, -0.9920182228, -0.352373898, 0.5019666553, -0.4318062961, 0.0426778942, -0.5365985632, 0.1176537052, -0.0036811056, -0.1355537921, -0.2918403447, -0.3793652952, 0.3427999914, 0.0119904224, 1.2535467148, -0.4246667325, 0.2139688134, -0.3835571408, 0.0670988336, 0.0206326749, -0.3997405767, -0.3969603777, -0.0537345074, 0.3889828026, 0.2038506269, -0.0044693947, -0.0159486197, 0.3501478434, -0.3596816659, -0.0978938341, -0.6366390586, -0.2279251963, 0.1664911211, -0.2680296302, -0.0123324692, -0.1415036023, -0.3960226774, -0.0637492836, -0.2075771242, 0.107176438, -1.3275139332, 0.0118979309, -0.6989386678, -0.260148108, 0.6972681284, 0.0757236779, -0.0302112512, -0.1333198249, 0.4846360385, -0.1442804188, 0.2096456885, 0.2919196188, -0.2161897272, 0.0190013722, 0.4336061776, 0.2262809128, -0.057555072, -0.0092032924, -0.3067292869, -0.5397666693, 0.1881190538, -0.1577778757, -0.0732346699, 0.0791189298, 0.0502863042, 0.5835140944, -0.1311803907, 0.0522005484, 0.3408516645, -0.0680197179, 0.2375017554, 0.2997811437, -0.2217587531, -0.1210100278, 0.2032336742, -0.3868436515, 0.098322466, -0.0268243048, -0.2293084264, -0.1785353124, -0.2206578404, 0.0811902434, 0.1561337411, 0.3480139971, 0.3714961708, -0.8463890553, -0.040627297, -0.390545398, 0.2973010838, -0.1077024415, 0.1191631407, -0.1448717266, 0.0759288147, -0.1090726703, -0.2713242471, -0.1497026533, 0.1947856545, 0.5448101163, 0.1558864266, 0.1981367171, 0.1235750392, 0.2135001123, -0.2024725974, 0.0083686821, 0.7130544186, -0.1423343867, -0.0027624574, -0.1330531687, 0.4429421723, 0.3447732031, -0.097606793, 0.0914019942, -0.0731670111, 0.1402992755, 0.1355691403, 0.1770949811, 0.3643753529, 0.5940411687, -0.0489943698, 0.1494393349, -0.3058607578, 0.3836067319, -0.0836388022, -0.0172554888]'}, excluded_embed_metadata_keys=['_id', 'transit', 'minimum_nights', 'maximum_nights', 'cancellation_policy', 'last_scraped', 'calendar_last_scraped', 'first_review', 'last_review', 'security_deposit', 'cleaning_fee', 'guests_included', 'host', 'availability', 'reviews', 'image_embeddings'], excluded_llm_metadata_keys=['_id', 'transit', 'minimum_nights', 'maximum_nights', 'cancellation_policy', 'last_scraped', 'calendar_last_scraped', 'first_review', 'last_review', 'security_deposit', 'cleaning_fee', 'guests_included', 'host', 'availability', 'reviews', 'image_embeddings'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='2501f49a-7e24-4b97-a0e4-18c4bc2a34e0', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'_id': 24871838, 'listing_url': 'https://www.airbnb.com/rooms/24871838', 'name': 'HOMESTAY NEAR MONTREAL AIRPORT', 'summary': 'Quiet home, near Airport, Good for 1-4 persons. Close to all.......5 mins walk to bus #208, 20 mins walk to Sunnybrook train station, 20 mins drive to Montreal Airport, Via-Rail & Downtown. Highway 13, 20 & 40 nearby. Park, Grocery, Pharmacy, Hindu & Sikh Temple walking distance. Nice & Quiet neighborhood ! Vegetarian food on request.', 'space': '', 'description': 'Quiet home, near Airport, Good for 1-4 persons. Close to all.......5 mins walk to bus #208, 20 mins walk to Sunnybrook train station, 20 mins drive to Montreal Airport, Via-Rail & Downtown. Highway 13, 20 & 40 nearby. Park, Grocery, Pharmacy, Hindu & Sikh Temple walking distance. Nice & Quiet neighborhood ! Vegetarian food on request.', 'neighborhood_overview': '', 'notes': '', 'transit': '', 'access': '', 'interaction': '', 'house_rules': \"- No shoes in the house. No smoking, no parties, no events, no noise pls. - Respect people in the house & neighborhood. No pets pls. - Only light cooking in the shared kitchen. - As a courtesy, pls wash your dishes, leave sink & kitchen counter clean after use. - No alcohol or drugs in or around the house. - Restrict your shower to maximum 10 mins so that there is enough hot water for other guests. - For Hygeine & sanitation reasons, pls don't keep your luggage on beds. Don't drag on the floors pls. DO NOT HESITATE to approach host, if any problem occurs. - Thank you for choosig to stay with us. - Pls don't consume food or beverage in the rooms. Enjoy your meals in dining area.\", 'property_type': 'Townhouse', 'room_type': 'Shared room', 'bed_type': 'Real Bed', 'minimum_nights': 1, 'maximum_nights': 1125, 'cancellation_policy': 'strict_14_with_grace_period', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1526788800000, 'last_review': 1535947200000, 'accommodates': 4, 'bedrooms': 1.0, 'beds': 2.0, 'number_of_reviews': 5, 'bathrooms': 2.0, 'amenities': '[\"Air conditioning\", \"Heating\", \"Family/kid friendly\", \"Washer\", \"Dryer\", \"Shampoo\", \"Hangers\", \"Hair dryer\", \"Iron\"]', 'price': 46, 'security_deposit': 200.0, 'cleaning_fee': 19.0, 'extra_people': 20, 'guests_included': 1, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/5daca350-4912-4f69-bf81-7ca301210136.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"71573275\", \"host_url\": \"https://www.airbnb.com/users/show/71573275\", \"host_name\": \"Neera\", \"host_location\": \"Montreal, Qu\\\\u00e9bec, Canada\", \"host_about\": \"I am sociable & a good host. I am an educated, professional & semi retired lady. I love my family and friends. I am well travelled and well informed. I consider myself a spiritual person. Love to cook. I am very sensitive to human needs. I would love to help people if I can.\", \"host_response_time\": \"within an hour\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/7cff0a21-5f93-4e85-9d48-78a3144933ea.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/7cff0a21-5f93-4e85-9d48-78a3144933ea.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"Dollard-des-Ormeaux\", \"host_response_rate\": 100, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 6, \"host_total_listings_count\": 6, \"host_verifications\": [\"email\", \"phone\", \"reviews\", \"offline_government_id\", \"selfie\", \"government_id\", \"identity_manual\"]}', 'address': '{\"street\": \"Dollard-des-Ormeaux, Qu\\\\u00e9bec, Canada\", \"suburb\": \"Dollard-des-Ormeaux\", \"government_area\": \"Dollard-des-Ormeaux\", \"market\": \"Montreal\", \"country\": \"Canada\", \"country_code\": \"CA\", \"location\": {\"type\": \"Point\", \"coordinates\": [-73.79282, 45.49385], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 30, \"availability_60\": 60, \"availability_90\": 90, \"availability_365\": 365}', 'review_scores': '{\"review_scores_accuracy\": 7, \"review_scores_cleanliness\": 6, \"review_scores_checkin\": 10, \"review_scores_communication\": 10, \"review_scores_location\": 7, \"review_scores_value\": 6, \"review_scores_rating\": 68}', 'reviews': '[{\"_id\": \"266743813\", \"date\": 1526788800000, \"listing_id\": \"24871838\", \"reviewer_id\": \"111653310\", \"reviewer_name\": \"Tathagat\", \"comments\": \"Clean and comfortable room\"}, {\"_id\": \"269592908\", \"date\": 1527393600000, \"listing_id\": \"24871838\", \"reviewer_id\": \"117253059\", \"reviewer_name\": \"Jeresteen\", \"comments\": \"The host was very accommodating and extremely sweet. She took great care of us. The room that we stayed in looked amazing and was clean until we looked behind the bed. There was a lot of dust or sawdust .. not sure which and unfortunately my nephew got allergies as he is allergic to dust. The home needs a bit of TLC. The bathroom was also not in a great shape, there was a hole where the toilet paper role was but it got fixed the very next day, which was great. The unfortunate part is that we didn\\'t realize it was a shared bathroom with other guests which was quite annoying as we are quite picky people . It is mentioned under house rules that it is a  shared bathroom. I wish this was written upfront on the summary page. Also whenever we wanted to go back to the house we had to call the host as we did not have the keys. The host was very accommodating but we felt bad going late. Once again the host was amazing very patient and nice but some things in the house were lacking.\"}, {\"_id\": \"285775322\", \"date\": 1530676800000, \"listing_id\": \"24871838\", \"reviewer_id\": \"196728188\", \"reviewer_name\": \"\\\\ucc44\\\\uc2e0\", \"comments\": \"\\\\uac00\\\\uaca9 \\\\ub300\\\\ube44 \\\\ubcc4\\\\ub85c~~~\"}, {\"_id\": \"310403747\", \"date\": 1534651200000, \"listing_id\": \"24871838\", \"reviewer_id\": \"209792519\", \"reviewer_name\": \"Amrith\", \"comments\": \"Feel like home\"}, {\"_id\": \"318186692\", \"date\": 1535947200000, \"listing_id\": \"24871838\", \"reviewer_id\": \"169600572\", \"reviewer_name\": \"Kiran\", \"comments\": \"Room was okay but too much chaos. host advised privacy but there was constant interference. Air conditioning in the room was leaking and host advised to keep checking so we had to keep an all night to see for water leak. I was conjusted and the value we paid was not even 1% worth what we got. I wont suggest if you are looking something clean and private o lying suitable for a ghetto place. Host was good and responsive but the place didnt do justice.\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[0.028289903, 0.5426813364, -0.1263493299, 0.0880639553, 0.1195924133, -0.3484530747, -0.2910634875, 0.5790982246, 0.1835098863, 0.3143885136, -0.0885362476, -0.2998264134, 0.1567870378, -0.5114456415, 0.2296565473, 0.1135994419, -0.350595057, -0.1110990345, 0.2008510083, -0.0699903965, -0.2177600563, 0.2117584497, 0.3657162189, 0.3446537554, -0.3597181737, 0.2043109983, 0.208236292, -0.4756190777, -0.0638155416, -0.1422024965, 0.189360708, 0.3805072904, 0.0849997178, 0.3461001813, -0.5480548739, 0.5954501629, -0.0452642627, -0.4723055959, 0.0436929278, 0.8003280163, -0.3365020454, -0.0836261958, -0.2246079445, -0.201527521, -0.1204060614, -2.3315246105, 0.1338311434, 0.2543126941, -0.1014514565, -0.0808105916, 0.4599454105, 0.1726036966, 0.4198836982, 0.4249495268, 0.1274706274, -0.1988445371, -0.1569736004, -0.075748533, -0.5405645967, 0.1542513072, -1.0775711536, -0.5395069122, 0.015805196, 0.1981425285, 0.094421722, 0.0276280511, 0.4348567128, 0.2626309991, -0.1738325655, 0.1230541617, -0.0702876672, 0.383765161, 0.1527442485, -0.23851493, 0.1178443655, 0.0280016307, -0.4287573695, 0.2286910266, -0.2804454565, -0.7532225847, -0.0914950818, -0.1909806877, 0.0794506818, 0.6901681423, 0.5214423537, 0.2294345051, 0.7680078149, -0.3327402771, -0.3528374434, -0.0666580945, 0.2052869201, -0.2260334343, -6.1491308212, -0.6932991743, -0.1862934381, 0.2446604967, 0.3239989281, -0.2762842774, 0.3406631052, 0.0331211835, 0.1882238239, 0.1271226555, 0.2411140949, 0.2437815666, 0.5258088708, 0.1317546964, 0.2406836599, -0.2573142946, 0.3292613924, -0.0990086794, -0.3411449194, 0.2984005511, -0.1651508212, -0.0593308024, -0.1912773848, -0.438274771, -0.0906344205, 0.1156560034, 0.2553135157, 0.2341465503, 0.1146052703, -0.2945663333, 0.3626779318, -0.0591760501, -0.0462215357, -0.1600264758, 0.0790427625, -0.3423352242, -0.118657507, 0.1727341861, 0.2024773806, 0.100341171, -0.3118989468, 0.7887333632, -0.0354722328, 0.1856890321, 0.1446823031, 0.8781445622, -0.2801594734, 0.0983140767, 0.1952615231, -0.0133991726, 0.5233959556, 0.1098594815, -0.4775821865, 0.2707497478, -0.0413427949, -0.2478369027, 0.145519644, 0.2029408216, 0.0758287534, 0.3051381707, -0.4521685839, -0.4098881185, 0.286075145, -0.7098713517, 0.0265494119, -0.0101394653, 0.256688714, 0.16969046, -0.1868201494, -0.4713023007, 0.1897083819, -0.0065655261, 0.0999124125, 0.1475352049, 0.3530426919, -0.2598636448, 0.1025258079, -0.5044997334, 0.231235683, 0.0187529251, 0.2514898181, 0.146372512, -0.131714806, -0.0280266143, 1.1765700579, -0.3130852282, -0.5862109661, 0.0229565334, -0.0527637079, -0.0053850189, 0.0131278671, -0.3453762531, -0.0441556983, 0.1188255996, 0.2976155579, 0.3626728952, -0.3556631804, 0.0579638667, -0.0282976739, -0.0714732111, 0.3615851402, -0.3986552358, 0.0215986557, -0.0303606093, 0.5349525213, -0.1762580425, -0.8364406228, -0.2489314824, 0.2986877561, -0.1610969454, -0.0573436581, 0.1166123003, 0.7104568481, 0.410687089, -0.2750972807, 0.3243075013, -0.0081026964, 0.4379435778, 0.0872915834, 0.2718119323, 0.1883122176, -0.0978781804, -0.2305068076, -0.1585019827, 0.4696531296, -0.0810396969, 1.7187594175, 0.077917479, 0.2881379724, 0.4117026329, -0.1115251258, -0.2318728268, -0.3111547232, 0.0711369216, 0.0561151393, 0.0708963573, -0.0073781461, 0.07729505, 0.3781637847, -0.225100413, 0.5094650984, 0.2830577493, -0.004775614, 0.2804560065, 0.1861454099, -0.2616858184, 0.0108465031, -0.0741706192, -0.255440861, -0.0605108105, 0.0850971714, -0.3650119901, 0.4144392312, 0.7378973961, 0.2186967283, -0.1151337773, -0.059429504, 0.0897397771, 0.2029229254, -0.1234460622, 0.5237460136, 0.2215650678, -0.0454240665, 0.2565318644, -0.1553573608, 0.1546132416, 1.563126564, 0.06815698, -0.0997933, -0.0781492591, 0.5405812263, -0.2718766332, 0.1561465859, -0.2457471788, 0.0241587386, -0.0316776633, -0.0747023821, 0.0479849055, 0.2148507833, 0.0995909721, -0.0115843313, 0.095813334, -0.1198509037, -0.2730686367, 0.2043595463, -0.2727225125, 0.1390503198, 0.3135280013, 0.3765451014, -0.0080140121, 0.1631822288, 0.9854659438, 0.5822296143, -0.2478367686, -0.0276659261, -0.2270969301, -0.0532878935, -0.0329822078, 0.3079518378, 0.0929483175, 0.5452842712, 0.3056309223, -0.1484960914, 0.1271563917, -0.6985507011, 0.0676160455, -0.1846180856, -0.2561847568, -0.1525128186, -0.0217330102, 0.060335651, 0.0294332299, 0.2544634938, -0.3957610726, 0.0620008595, -0.1176486909, 0.5863558054, -0.4218372107, -0.0291494504, 0.3851079643, 0.7876068354, 0.2506110072, -0.3599236608, 0.1765229404, 1.0986280441, -0.033419393, -0.0252086725, 0.1241148412, 0.1645638347, 0.665286541, -0.1602624059, -0.066517286, -0.0801196024, -0.0718235373, 0.2776556909, -0.1390406489, 0.0307008736, -0.0447937548, -0.2949569225, 0.1075274944, -0.0965340286, -0.3752008379, -0.1307932436, 0.2048219144, 0.2870424092, -0.2290546894, -0.4168808162, 0.1040272117, 0.2015807629, 0.0473072566, -0.105741933, 0.2785045803, 0.0082127452, -0.074578613, 0.3230136335, 0.2532664537, 0.4702716768, -0.306440562, 0.7499889731, -0.0368955582, -0.2877925634, -0.0195006263, -0.3370005488, 0.9595741034, -0.1999542564, 0.133206293, -0.0038944352, 0.2215911448, -0.1583319753, 0.5310643911, 0.0329700224, 0.3623477221, -0.8074452281, 0.3001114428, -0.2432784885, -0.6039684415, -0.4067654908, -0.3264508247, -0.1280963123, 0.2148363739, 0.021133896, 0.1535920501, 0.1499336362, 0.2566893399, 0.2929673791, 0.2469513416, 0.0530762672, -0.1169853657, 0.0999395698, 0.3284144402, 0.0050406456, 0.3364064395, 0.3483855724, -0.3329791129, 0.2665727437, -0.2109671533, -0.2796326876, -0.9920182228, -0.352373898, 0.5019666553, -0.4318062961, 0.0426778942, -0.5365985632, 0.1176537052, -0.0036811056, -0.1355537921, -0.2918403447, -0.3793652952, 0.3427999914, 0.0119904224, 1.2535467148, -0.4246667325, 0.2139688134, -0.3835571408, 0.0670988336, 0.0206326749, -0.3997405767, -0.3969603777, -0.0537345074, 0.3889828026, 0.2038506269, -0.0044693947, -0.0159486197, 0.3501478434, -0.3596816659, -0.0978938341, -0.6366390586, -0.2279251963, 0.1664911211, -0.2680296302, -0.0123324692, -0.1415036023, -0.3960226774, -0.0637492836, -0.2075771242, 0.107176438, -1.3275139332, 0.0118979309, -0.6989386678, -0.260148108, 0.6972681284, 0.0757236779, -0.0302112512, -0.1333198249, 0.4846360385, -0.1442804188, 0.2096456885, 0.2919196188, -0.2161897272, 0.0190013722, 0.4336061776, 0.2262809128, -0.057555072, -0.0092032924, -0.3067292869, -0.5397666693, 0.1881190538, -0.1577778757, -0.0732346699, 0.0791189298, 0.0502863042, 0.5835140944, -0.1311803907, 0.0522005484, 0.3408516645, -0.0680197179, 0.2375017554, 0.2997811437, -0.2217587531, -0.1210100278, 0.2032336742, -0.3868436515, 0.098322466, -0.0268243048, -0.2293084264, -0.1785353124, -0.2206578404, 0.0811902434, 0.1561337411, 0.3480139971, 0.3714961708, -0.8463890553, -0.040627297, -0.390545398, 0.2973010838, -0.1077024415, 0.1191631407, -0.1448717266, 0.0759288147, -0.1090726703, -0.2713242471, -0.1497026533, 0.1947856545, 0.5448101163, 0.1558864266, 0.1981367171, 0.1235750392, 0.2135001123, -0.2024725974, 0.0083686821, 0.7130544186, -0.1423343867, -0.0027624574, -0.1330531687, 0.4429421723, 0.3447732031, -0.097606793, 0.0914019942, -0.0731670111, 0.1402992755, 0.1355691403, 0.1770949811, 0.3643753529, 0.5940411687, -0.0489943698, 0.1494393349, -0.3058607578, 0.3836067319, -0.0836388022, -0.0172554888]'}, hash='c36d02cc98dd0b0baed8c8d1f3b0647dccc9e0d8a492203d957b3cc6b92c549c'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='f4625c6a-7752-4b06-99d0-75da36f82799', node_type=<ObjectType.TEXT: '1'>, metadata={'_id': 24818739, 'listing_url': 'https://www.airbnb.com/rooms/24818739', 'name': 'Cosy Studio in the Heart of Hong Kong', 'summary': 'Lovely studio right in Sai Ying Pun. 5 minute walk to MTR and 2 stops away from Central. There are plenty trolleys and busses he stop right on the main road outside the building. It’s about 15 minute walk to Central where you can find lots of attractions along the way.', 'space': '', 'description': 'Lovely studio right in Sai Ying Pun. 5 minute walk to MTR and 2 stops away from Central. There are plenty trolleys and busses he stop right on the main road outside the building. It’s about 15 minute walk to Central where you can find lots of attractions along the way.', 'neighborhood_overview': '', 'notes': '', 'transit': '', 'access': '', 'interaction': '', 'house_rules': '', 'property_type': 'Apartment', 'room_type': 'Entire home/apt', 'bed_type': 'Real Bed', 'minimum_nights': 1, 'maximum_nights': 1125, 'cancellation_policy': 'flexible', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1529208000000, 'last_review': 1551589200000, 'accommodates': 2, 'bedrooms': 0.0, 'beds': 1.0, 'number_of_reviews': 8, 'bathrooms': 1.0, 'amenities': '[\"TV\", \"Wifi\", \"Air conditioning\", \"Wheelchair accessible\", \"Kitchen\", \"Doorman\", \"Elevator\", \"Washer\", \"Essentials\", \"Shampoo\", \"Hangers\", \"Hair dryer\", \"Laptop friendly workspace\"]', 'price': 699, 'security_deposit': None, 'cleaning_fee': 157.0, 'extra_people': 542, 'guests_included': 2, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/3cd51cc3-971d-4325-ae2c-59e004431346.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"7517718\", \"host_url\": \"https://www.airbnb.com/users/show/7517718\", \"host_name\": \"Nancy\", \"host_location\": \"Hong Kong, Hong Kong\", \"host_about\": \"Hi! I\\\\u2019m originally from upstate New York and moved to Hong Kong last year for work.\", \"host_response_time\": \"within a day\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/user/f2cb2236-4d7c-43ff-86c9-1ffd881c1e71.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/user/f2cb2236-4d7c-43ff-86c9-1ffd881c1e71.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"Sheung Wan\", \"host_response_rate\": 92, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": true, \"host_listings_count\": 1, \"host_total_listings_count\": 1, \"host_verifications\": [\"email\", \"phone\", \"facebook\", \"reviews\", \"jumio\", \"government_id\"]}', 'address': '{\"street\": \"Hong Kong, Hong Kong Island, Hong Kong\", \"suburb\": \"Central & Western District\", \"government_area\": \"Central & Western\", \"market\": \"Hong Kong\", \"country\": \"Hong Kong\", \"country_code\": \"HK\", \"location\": {\"type\": \"Point\", \"coordinates\": [114.14509, 22.28684], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 2, \"availability_60\": 23, \"availability_90\": 23, \"availability_365\": 242}', 'review_scores': '{\"review_scores_accuracy\": 9, \"review_scores_cleanliness\": 8, \"review_scores_checkin\": 9, \"review_scores_communication\": 10, \"review_scores_location\": 10, \"review_scores_value\": 9, \"review_scores_rating\": 90}', 'reviews': '[{\"_id\": \"277647935\", \"date\": 1529208000000, \"listing_id\": \"24818739\", \"reviewer_id\": \"13202943\", \"reviewer_name\": \"Seifu\", \"comments\": \"Nancy\\'s place is in a great location right near Sai Ying Pun MTR station. The flat has all the essentials with a shower, kitchen and bed. It\\'s a great base from which to explore Hong Kong.\"}, {\"_id\": \"291515961\", \"date\": 1531713600000, \"listing_id\": \"24818739\", \"reviewer_id\": \"48764189\", \"reviewer_name\": \"Stefan\", \"comments\": \"Nancys place is a great location from which to explore HK!\"}, {\"_id\": \"294273903\", \"date\": 1532232000000, \"listing_id\": \"24818739\", \"reviewer_id\": \"141484392\", \"reviewer_name\": \"Chris\", \"comments\": \"Great place! Room was super tidy and it\\'s a very short walk to the Sai Ying Pun MTR. Pleasant communication all around.\"}, {\"_id\": \"*********\", \"date\": 1533441600000, \"listing_id\": \"24818739\", \"reviewer_id\": \"*********\", \"reviewer_name\": \"Marc-Antoine\", \"comments\": \"Nice place, convenient, and really good location, close to Sun Yat Sen Park and next to a subway entrance! The neighborhood is lively but the room quite quiet. Check-in was easy, check-out even more . The place is small But not that much for Hong Kong and well-equipped, perfect for a solo traveller.\"}, {\"_id\": \"*********\", \"date\": 1534046400000, \"listing_id\": \"24818739\", \"reviewer_id\": \"26765073\", \"reviewer_name\": \"Yina\", \"comments\": \"\\\\u623f\\\\u9593\\\\u6253\\\\u958b\\\\u884c\\\\u674e\\\\u7bb1\\\\u5c31\\\\u5361\\\\u4f4f\\\\u8d70\\\\u9053\\\\u4e86,\\\\u883b\\\\u5c0f\\\\u7684,\\\\u9069\\\\u5408\\\\u55ae\\\\u8eab\\\\u4f4f\\\\u5bbf,\\\\u4f4d\\\\u7f6e\\\\u5f88\\\\u65b9\\\\u4fbf,\\\\u6d74\\\\u5ba4\\\\u6bdb\\\\u9aee\\\\u6c92\\\\u6709\\\\u6e05...\"}, {\"_id\": \"*********\", \"date\": 1546318800000, \"listing_id\": \"24818739\", \"reviewer_id\": \"39293487\", \"reviewer_name\": \"Stephen\", \"comments\": \"Location of place is very convenient. Like many places in Hong Kong, space is at a premium, but there is more than sufficient space for a single traveler or a couple. Place is an excellent value for those looking to spend more of their time enjoying Hong Kong and just need a place to sleep late at night. \\\\n\\\\nNancy was very responsive and the place was all set up when we arrived. Would definitely stay again in future.\"}, {\"_id\": \"*********\", \"date\": 1550898000000, \"listing_id\": \"24818739\", \"reviewer_id\": \"86561095\", \"reviewer_name\": \"Ryan\", \"comments\": \"The location of this place was perfect! You are very central and in a cool up and coming area. It\\'s easy to get to from the airport. It is quite small but perfectly fine for 1-2 people. The only downside is the bed, which was extremely hard and difficult to sleep on. I would still recommend this place as it was very convenient!\"}, {\"_id\": \"*********\", \"date\": 1551589200000, \"listing_id\": \"24818739\", \"reviewer_id\": \"44928822\", \"reviewer_name\": \"Sarah\", \"comments\": \"We didn\\'t enjoy our stay at Nancy\\'s place due to the extremely hard bed. Whilst the host was helpful in answering any questions we had unfortunately the apartment had not been cleaned. Whilst this may be a one-off issue the unwashed bedding and lack of working air conditioning left us feeling unrested and unable to sleep comfortably. \\\\n\\\\nThe apartment is a private lockable room in a shared flat in an apartment building - about 2 minutes from the A2 MTR exit and 1 minute from the tram stop. We got the impression that other residents/the landlord did not want non-residential guests staying in the building - this made us feel uncomfortable several times throughout our stay.\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[-0.4249857068, 0.2868934274, 0.1347595751, 0.4580504298, 0.086326614, -0.5570053458, -0.4133133888, 0.7044804096, -0.469765991, -0.0253965463, 0.3771396875, -0.0389472954, -0.2070026696, -0.498660028, 0.3811903298, -0.246388495, -0.2631154954, 0.0373689681, 0.3274929821, -0.0969198421, 0.3657887578, -0.0906344876, 0.4447936416, 0.2015483975, -0.2658789754, 0.1914606243, 0.3502138555, -0.6528893709, 0.0486677848, -0.1814704388, -0.0231769532, 0.2835228443, 0.0367835462, 0.0466011465, -0.7098959088, 0.338075459, -0.1841160953, -0.6343468428, 0.3329365849, 0.2721553445, -0.6380196214, -0.4130164385, -0.2199569941, -0.257571131, -0.0598069876, -1.9742379189, 0.3801576197, 0.0342598446, -0.0048746988, -0.0157407522, 0.1061400324, 0.3853500187, 0.2165354341, 0.3005985916, 0.0206130929, -0.0790824145, -0.3878996074, -0.5452831984, -0.1659913361, 0.188696593, 0.1288179159, -0.5029723644, -0.2144998908, -0.3930740356, -0.1815857589, -0.1334615201, 0.6448399425, 0.1852291822, 0.6845952272, 0.0420771688, 0.086695008, 0.1138276309, 0.0132194795, -0.1157420278, -0.0672175288, 0.1131174862, -0.1948806643, 0.2519989014, 0.0283822343, -0.4632290304, -0.3272307217, -0.0571534857, 0.0878153294, 0.6219537854, 0.5347490311, -0.2979043126, 0.5080491304, -0.4239952862, -0.7347147465, -0.1568066776, -0.1456358433, -0.0875635147, -6.5954151154, 0.3915340006, -0.1928173304, 0.1695390791, 0.2997232378, 0.299085319, 0.5573402047, -0.3885336518, 0.0642787144, 0.2608981133, 0.3707594573, -0.0052823927, 0.0197662264, -0.0976275206, -0.4933166206, 0.0221532844, 0.4718209505, 0.2588553131, -0.6668253541, 0.598772347, -0.2034018487, -0.6578175426, 0.3049017191, -0.1745646298, -5.48884e-05, 0.0036029592, -0.5399010181, 0.5873813629, 0.5019544959, -0.2948977351, 0.152570188, 0.2755687833, -0.0456347726, -0.5952959061, 0.5404180288, -0.4793410003, -0.2582863867, 0.4176558554, -0.4013422728, -0.041523058, -0.321398437, 0.8247510791, 0.0558130033, 0.0103015322, 0.1576526463, 0.1889743358, -0.1494760513, 0.2475715578, -0.0429684967, -0.0056064669, 0.092658937, 0.2621244192, -0.3066220582, 0.4712063968, 0.2521444559, 0.1549856216, -0.0584453195, 0.2447771877, -0.3616022766, -0.165654853, 0.0426046625, 0.3286617696, 0.1081266478, -0.2833153903, 0.1925040931, -0.6589725018, 0.2719596624, 0.5995569229, -0.0767051131, 0.0380606316, 0.4928856194, 0.4609612525, 0.4944107234, 0.1962920129, 0.8813393116, -0.4360135496, 0.2597152889, -0.2145456672, 0.1287243515, -0.5023820996, -0.0157496184, 0.3681961894, -0.3047198951, 0.126786381, 1.646776557, -0.1372043788, 0.2952940166, -0.018566817, 0.1389690042, 0.0542892516, -0.1803266704, 0.1726937592, -0.1797333956, 0.1834639609, -0.1725710183, 0.100221172, -0.3112410605, -0.0228844825, 0.1221267879, 0.2141662091, -0.0375041366, -0.0908674002, 0.5576756597, 0.1208534688, 0.3805292249, 0.0736277178, -1.0039994717, 0.3058624566, 0.1203827187, -0.3435912132, 0.1326855868, 0.149600029, 0.6071168184, -0.2964916229, 0.2414059788, 0.3623900414, 0.022030171, 0.2379507869, -0.3537982404, 0.229836911, -0.1195619032, 0.1796314418, -0.1482289135, -0.3365411162, 0.3352710307, -0.066858016, 0.9954123497, 0.0612790696, -0.4435375929, 0.2906423509, -0.1152891889, -0.624550581, -0.1043927073, 0.0429602116, 0.1021535546, 0.0484501496, 0.2395381033, 0.0852625668, 0.4605731964, 0.2810772657, 0.1008199304, 0.6431871653, -0.1567980945, 0.7363876104, 0.6445298791, -0.1242995262, -0.0562503487, 0.1806692332, -0.0232830551, 0.0845302492, -0.2734347582, -0.4684785604, 0.4106335044, 0.7786636353, -0.0458915085, -0.5155130625, -0.0865951702, -0.3192517757, -0.0375628136, -0.0891491175, -0.1178928912, 0.105165571, 0.1375459284, 0.1733171493, -0.2364248782, 0.3971491456, 1.047770381, -0.3923878074, 0.1280434132, -0.1317535192, 0.3783975244, 1.4797105789, 0.3340144753, 0.0121281035, 0.0932352915, -0.3710186183, -0.0675557107, 0.1457364708, -0.3588316441, -0.3915560544, 0.1828556061, -0.3515296578, -0.1520602405, -0.126170665, 0.4129315317, -0.4852103293, -0.1638411582, -0.1789916605, 0.5038180351, -0.2791780829, -0.2270836383, 0.7437161207, 0.6590687037, -0.3539847732, -0.5308571458, 0.1472592652, 0.0591892898, 0.051617492, 0.2351301014, 0.1228523999, 0.2709792256, -0.1063612103, -0.2987050414, -0.0556642935, -0.0305884629, 0.2123399377, -0.5191867352, -0.1645119041, -0.3766528666, -0.0879928693, -0.1868224889, -0.1498186588, -0.0325727202, -0.2681253254, -0.1465667337, -0.1742292643, 0.5437396765, 0.1070270389, 0.0382592976, 0.5032322407, 0.8235766888, -0.4767813683, 0.0206355192, 0.3399460614, 0.5334485173, -0.152735129, 0.2947852314, -0.1203648522, 0.3202168643, 0.7100434303, -0.1737895608, 0.1072025597, 0.0282791108, 0.6051469445, 0.354143858, -0.1838518083, 0.0697837174, -0.0787729174, -0.007019069, 0.2596561313, 0.1425700188, -0.3016323745, 0.0996029377, 0.2821411788, 0.2308156937, -0.093228884, -0.1585410535, -0.1158863306, -0.1417016089, -0.1275564283, -0.007396441, 0.4069790244, 0.1786732078, -0.2720657289, 0.3688606024, 0.1932512373, 0.1157492176, -0.311894536, 0.7426621318, 0.031804923, -0.1980550885, 0.1596983373, -0.4725829661, -0.370957315, 0.0336848795, 0.3008775413, -0.5468822122, -0.4059664905, -1.1186188459, -0.3854837418, -0.1390925795, -0.2717506289, 0.4490094781, -0.0489704311, -0.4471151829, -0.9850897193, -0.2791576684, -0.2064676434, 0.2712975144, -0.2592327595, 0.0195201784, -0.0430730544, 0.2182071954, 0.4338933825, 0.4441533685, 0.2735003829, -0.7562411427, 0.0683490038, 0.2261841893, 0.3279668689, 0.0531939603, -0.1703151762, 0.3762774169, 0.3130448461, 0.1757576168, -0.2731378078, -0.3980905116, -0.3394751549, -0.3455021977, 0.4096892178, -0.2475276291, 0.5125181079, -0.2179437727, 0.1689870656, -0.0489026532, -0.1199449822, -0.3176470399, 0.5354034305, 0.0650269017, -0.1315177381, 1.1821225882, -0.4970289469, 0.3658720553, -0.2416169494, 0.2220795155, -0.0313100889, -0.7288525701, 0.0071334392, -0.3417907357, 0.0431505293, -0.0488679111, 0.2252672762, 0.2466342151, 0.3116643727, -0.4013240933, 0.1500560641, -0.426643014, 0.1551049501, 0.2222323716, -0.2351940721, 0.033396244, -0.0463437177, -0.0967845619, 0.1031716615, -0.2894729376, 0.3988717794, -0.8749447465, 0.1021548882, -0.4293667376, -0.2278559506, 0.123265624, 0.4469744265, 0.1110199243, 0.0831768289, 0.2957620621, 0.1485922039, -0.0644113794, 0.4906956851, -0.0338338017, 0.0481871106, -0.0626150668, 0.2649432123, -0.1504374295, -0.135217011, -0.0416873991, -0.4865778983, 0.3370192051, -0.2861433327, 0.1080518961, 0.0295226369, 0.111711666, 0.7827356458, 0.1340029389, 0.2561218739, 0.3316004276, 0.031516213, 0.5838077664, -0.0316448435, -0.48511374, 0.3460516036, -0.2158347964, 0.2043406516, 0.0346233323, 0.3476728201, -0.1142945811, -0.4607749581, 0.1476607323, 0.0216729604, -0.0728876963, 0.2706070244, 0.474869132, -0.469435364, -0.1729541123, -0.0645233616, 0.2648380697, 0.0360745601, 0.2847351134, 0.3681031168, 0.1403540075, -0.1315924823, -0.0425316505, 0.2014541626, 0.4883278012, 0.7885029316, 0.2084901631, 0.865018487, -0.0172005408, 0.520958364, 0.0315133668, 0.3405387402, 0.5310319066, -0.6695555449, -0.3199343383, 0.138635993, 0.6479771733, -0.1319323927, -0.2299695313, -0.4434045255, -0.1729574502, -0.1406909525, 0.354916364, -0.0693956167, 0.1499086916, 0.1117649153, 0.2366907001, 0.1561951637, -0.4023478031, -0.4567997158, 0.132241413, -0.2555404007]'}, hash='b8573759d49c5bb367e172cf362ce332196e90e17ff383c6f3a1338feddfc935'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='7224655a-31bd-419c-9358-b307e79a18bf', node_type=<ObjectType.TEXT: '1'>, metadata={}, hash='9d7e44b98e34e0301610a907dcc735f0fe3f3671ad830802fae5077b38a113cb')}, text='Quiet home, near Airport, Good for 1-4 persons. Close to all.......5 mins walk to bus #208, 20 mins walk to Sunnybrook train station, 20 mins drive to Montreal Airport, Via-Rail & Downtown. Highway 13, 20 & 40 nearby. Park, Grocery, Pharmacy, Hindu & Sikh Temple walking distance. Nice & Quiet neighborhood ! Vegetarian food on request.', start_char_idx=0, end_char_idx=336, text_template='Metadata: {metadata_str}\\n-----\\nContent: {content}', metadata_template='{key}=>{value}', metadata_seperator='\\n'), score=0.7619873285293579),\n", " NodeWithScore(node=TextNode(id_='7270e16c-69a3-49e3-b025-64fd95428cda', embedding=None, metadata={'_id': 30507309, 'listing_url': 'https://www.airbnb.com/rooms/30507309', 'name': 'luxury apartment near Downtown', 'summary': 'the apartment is located 6 minute away from downtown, very cozy place to spend time with friends and family. the apartment put you at the center of it all; restaurent nearby, groceries store etc. everything is at a walking distance, the subway, bus stop. you will be able to enjoy the city like a real Canadian', 'space': 'the apartment has many accessories: . 2 Tv  .bell cable .Apple TV .Netflix .xbox one .ps4 .printer  .sound system and many more accessories', 'description': \"the apartment is located 6 minute away from downtown, very cozy place to spend time with friends and family. the apartment put you at the center of it all; restaurent nearby, groceries store etc. everything is at a walking distance, the subway, bus stop. you will be able to enjoy the city like a real Canadian the apartment has many accessories: . 2 Tv  .bell cable .Apple TV .Netflix .xbox one .ps4 .printer  .sound system and many more accessories the neighborhood I very cozy, not a lot of noise. you will find everything you. need at walking distance if you don't have a car its really not a problem, you have the bus   stop in front of the apartment. you can take the 165 to get to downtown. 166 to go and visit Oratoire St Joseph\", 'neighborhood_overview': 'the neighborhood I very cozy, not a lot of noise. you will find everything you. need at walking distance', 'notes': '', 'transit': \"if you don't have a car its really not a problem, you have the bus   stop in front of the apartment. you can take the 165 to get to downtown. 166 to go and visit Oratoire St Joseph\", 'access': '', 'interaction': '', 'house_rules': '', 'property_type': 'Apartment', 'room_type': 'Entire home/apt', 'bed_type': 'Real Bed', 'minimum_nights': 2, 'maximum_nights': 16, 'cancellation_policy': 'flexible', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1545627600000, 'last_review': 1545627600000, 'accommodates': 5, 'bedrooms': 2.0, 'beds': 2.0, 'number_of_reviews': 1, 'bathrooms': 1.5, 'amenities': '[\"TV\", \"Wifi\", \"Air conditioning\", \"Pool\", \"Kitchen\", \"Elevator\", \"Free street parking\", \"Heating\", \"Washer\", \"Dryer\", \"Smoke detector\", \"Carbon monoxide detector\", \"Essentials\", \"Shampoo\", \"Lock on bedroom door\", \"Iron\", \"Laptop friendly workspace\", \"Private living room\", \"Outlet covers\", \"Game console\", \"Hot water\", \"Microwave\", \"Coffee maker\", \"Refrigerator\", \"Dishwasher\", \"Dishes and silverware\", \"Cooking basics\", \"Oven\", \"Stove\", \"Single level home\", \"Patio or balcony\", \"Cleaning before checkout\", \"Step-free access\", \"Accessible-height bed\", \"Host greets you\"]', 'price': 200, 'security_deposit': None, 'cleaning_fee': None, 'extra_people': 0, 'guests_included': 1, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/d75fb052-9a30-43ea-a17d-dac8db34761e.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"172923728\", \"host_url\": \"https://www.airbnb.com/users/show/172923728\", \"host_name\": \"Alec\", \"host_location\": \"CA\", \"host_about\": \"\", \"host_response_time\": \"within an hour\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/user/df0cea70-d9d7-46ae-9faf-f82624c8dbb1.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/user/df0cea70-d9d7-46ae-9faf-f82624c8dbb1.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"Cote-des-Neiges\", \"host_response_rate\": 100, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 1, \"host_total_listings_count\": 1, \"host_verifications\": [\"phone\", \"selfie\"]}', 'address': '{\"street\": \"Montr\\\\u00e9al, Qu\\\\u00e9bec, Canada\", \"suburb\": \"Cote-des-Neiges-Notre-Dame-de-Grace\", \"government_area\": \"C\\\\u00f4te-des-Neiges-Notre-Dame-de-Gr\\\\u00e2ce\", \"market\": \"Montreal\", \"country\": \"Canada\", \"country_code\": \"CA\", \"location\": {\"type\": \"Point\", \"coordinates\": [-73.60754, 45.49448], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 30, \"availability_60\": 60, \"availability_90\": 90, \"availability_365\": 90}', 'review_scores': '{\"review_scores_accuracy\": null, \"review_scores_cleanliness\": null, \"review_scores_checkin\": null, \"review_scores_communication\": null, \"review_scores_location\": null, \"review_scores_value\": null, \"review_scores_rating\": null}', 'reviews': '[{\"_id\": \"361861495\", \"date\": 1545627600000, \"listing_id\": \"30507309\", \"reviewer_id\": \"177268073\", \"reviewer_name\": \"Stephh\", \"comments\": \"The host canceled this reservation 5 days before arrival. This is an automated posting.\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[0.0936508328, 0.2934384048, -0.1971703321, -0.036242418, -0.1308076829, -0.5545901656, 0.0703001693, 0.7066923976, 0.0367466025, 0.3122252524, 0.0575124398, -0.0176410545, 0.23645854, -0.3418642282, 0.2401382327, -0.2683159709, -0.4907199442, -0.0838668272, 0.2047564238, 0.1241259426, 0.6298086047, -0.0594352894, 0.17609635, 0.3586091697, -0.1152842492, 0.3271628618, 0.3562264442, -0.401230365, -0.0324097872, 0.0257515199, 0.2367218584, 0.4149738252, 0.0623220019, 0.1230640188, -0.6417615414, 0.4289738834, -0.2390958965, -0.3937125504, 0.0343358144, 1.3376479149, -0.2820866406, 0.2867327332, 0.3220860958, 0.0718876123, 0.0310478248, -2.4133234024, -0.1746396571, 0.6687079072, -0.191295594, -0.3058785796, 0.2382543534, 0.0172601566, 0.4137854576, 0.3177392483, 0.173821032, -0.6530903578, -0.1661632955, 0.0808606222, -0.1998483241, 0.2719935477, -1.8883548975, -0.5251950026, 0.3081382811, 0.0225291643, -0.11023397, -0.3362475634, 0.4266829789, -0.4477331936, -0.1139914319, 0.0808719099, -0.0515247695, -0.2638714015, 0.0207354464, 0.1511707604, -0.2066265345, 0.0632327646, -0.3621925414, -0.0039675459, -0.3030139208, -0.7638857961, -0.0083576255, 0.0057815015, 0.4586983323, 0.1093195006, 0.2378947735, -0.2319231629, 0.4890350699, -0.4177411795, -0.3584872782, 0.0620955341, 0.4642188549, 0.0553483516, -6.165699482, -0.3408660591, -0.1619065106, 0.2709009349, 0.2610371709, -0.0420166478, 0.5491482019, -0.2053907365, 0.1081285775, 0.2007240951, 0.2593016326, -0.3949846923, 0.44777897, -0.3637988269, 0.1364564151, -0.0422127172, -0.0504126772, 0.2430855334, -0.6666798592, 0.0356527865, -0.2137417495, 0.4018095434, -0.0614395477, -0.4291506112, 0.1719515622, 0.2576130033, 0.2563770711, 0.0531287193, -0.1573293358, -0.3043266833, 0.3812526464, 0.074896656, -0.0942599475, -0.3126021028, -0.1351081133, -0.2288512886, -0.3413164318, -0.2898048759, 0.1039129123, 0.3044900596, -0.1357839704, 0.7858344316, 0.0563879162, -0.2873725593, -0.129681021, 0.9556832314, -0.0980376452, 0.387187928, 0.4048145115, 0.2455666959, 0.7763134837, 0.1500092596, 0.0431036055, 0.1755233705, -0.1536914408, 0.4151951373, -0.0907365829, 0.3759293556, -0.1575253308, 0.0810023174, -0.152135551, -0.3514503539, 0.150087893, -0.3329392374, -0.3484278917, -0.0894963592, 0.28538993, -0.5667915344, 0.2878194749, -0.3309826255, 0.0950611308, 0.1416505426, 0.059813112, 0.326878041, 0.3416688144, -0.1071731746, 0.0039769039, 0.0809016377, 0.1889898032, 0.1132745668, -0.073839657, -0.090536125, 0.1542532742, 0.086439155, 0.2940548658, -0.0658153221, -0.6451060772, 0.136744976, -0.5218996406, -0.6433677077, 0.1812835634, -0.0225592032, -0.218790859, 0.030007707, -0.3147239387, 0.0395843387, 0.1397807896, -0.2037294805, 0.1303156614, 0.2028984129, 0.1701811403, -0.3757658601, 0.1266909242, -0.0675171539, 0.3045350909, 0.2398714572, -0.7187101245, 0.0730339587, 0.552485168, -0.3183240891, 0.2240779996, 0.1693822145, 0.2618731558, 0.0646095127, 0.3206186295, 0.1912883669, 0.0532983877, 0.3020588458, -0.6477378011, 0.5574151874, -0.2308274209, 0.1858152002, -0.2367836386, -0.3235510588, 0.2729896903, -0.2735707462, 0.6218007207, 0.0988899767, 0.2976068556, -0.0693745911, 0.4176607132, -0.1187660471, 0.1006759778, 0.0822289139, -0.1496759653, 0.1714290977, -0.652474463, 0.1693969071, 0.0710318983, 0.250695616, 0.2473854572, 0.5122967362, 0.4487681091, -0.1047468409, 0.032669045, 0.1507998705, -0.4412857294, -0.2955643535, 0.0418169908, -0.2061197162, -0.0490035713, -0.4358876348, 0.5679101944, 1.085180521, 0.1863981932, 0.3763158023, -0.1436301619, -0.4404369295, 0.0640965849, 0.0279755071, 0.2039414346, -0.148682192, -0.1436197758, 0.5396843553, -0.292086035, 0.1002515703, 0.7562980652, -0.240588218, 0.0274934433, 0.003957279, 0.2560417056, 0.302500844, 0.0691101998, 0.0979272649, 0.2681129575, -0.2618680596, -0.0495767072, 0.2786444724, 0.271064043, 0.3185680807, 0.3334520459, 0.0356190838, 0.2208519131, -0.5344493389, 0.3520115316, -0.3276404142, 0.0165878683, 0.0263864659, 0.3580197096, -0.3373585045, 1.0286982059, 0.5971220136, 0.0683153495, -0.348387301, 0.2484560609, -0.0706661716, -0.2046740651, -0.2614763975, 0.5054394603, -0.2704466879, 0.2676596642, -0.0996818915, -0.0902254581, 0.2712615728, -0.4692640305, 0.2040676475, -0.3840078712, -0.3669686317, -0.13712354, 0.413377434, 0.1280419528, 0.1807151586, 0.2464874387, -0.8473085761, 0.276778549, 0.1056393012, -0.1459106207, 0.1808351874, 0.1001494825, 0.1080775186, 0.7838820815, 0.3605751097, 0.0201069191, 0.2927002609, 0.3358390629, -0.2365003824, 0.1157880574, 0.3784732223, 0.0292614903, -0.2722353935, 0.0790782869, 0.155871138, -0.4979914725, 0.1811427772, 0.0011449233, 0.1367296576, 0.2438020259, -0.0180753842, -0.1500319839, -0.3702911437, 0.0850403085, -0.0034136735, -0.1555725336, -0.2515790761, -0.0526188239, 0.0709152371, -0.3321228325, -0.0679291338, -0.0751454383, 0.2116616964, -0.0416442938, -0.037087474, -0.1784698218, -0.1398064494, -0.0429410189, 0.2556550205, -0.0298438892, -0.3578060865, 1.2530721426, 0.3115640879, -0.1433243901, -0.1238279641, 0.3007592261, 0.6440600157, 0.0640157908, 0.8837527633, -0.3647267818, -0.0852599517, -0.0553782359, 0.1646501124, -0.3031951785, 0.084736295, -0.4819566905, 0.249663651, -0.0458876938, -0.2939544916, -0.3059579432, -0.422523886, -0.1878985763, -0.0600410849, 0.0710275173, -0.0179526266, 0.1731146723, 0.4978395402, -0.4139511585, 0.2558306754, 0.5786162615, -0.126276955, -0.0576158129, -0.2009733468, -0.012724054, 0.5874778628, 0.4462074339, -0.131814152, -0.501883924, -0.0402033925, -0.0243354961, -0.6816082597, 0.0382400118, 0.3575153351, -0.0178200305, 0.3436185122, -0.5882027149, -0.0872937888, 0.0163958576, -0.3335332274, 0.6096326113, -0.1430576146, -0.2621991932, 0.2161331028, 1.3086111546, -0.0525269359, 0.1280938089, -0.3650610447, 0.0732507706, -0.2491031736, -0.0652113557, -0.1270372868, 0.248532936, 0.2272706032, 0.1333750486, 0.3045102954, 0.1271125376, 0.4659550786, -0.4250519872, 0.1534260958, -0.3051880598, -0.0225665495, 0.5961506963, 0.1010922864, -0.2201724946, -0.3010082543, -0.3826218247, -0.1574632376, -0.1640392095, 0.5102485418, -1.0383553505, -0.06934122, -0.702436626, -0.6363373399, -0.3490189314, -0.0756868199, -0.0090990774, -0.1148631796, -0.1267162412, 0.1318321973, 0.1028501391, -0.188444078, -0.6364668012, 0.1462859958, 0.4813495874, 0.1454141587, -0.0163059961, -0.1456854939, -0.2061395198, 0.1601860225, -0.0493925884, -0.3427640498, 0.103279829, 0.1533657163, 0.0049643144, 1.1080428362, -0.1956967115, 0.1710280031, 0.3573034108, -0.3422089815, 0.5455297828, 0.4575739801, -0.1050536335, -0.2143058181, 0.5122144818, 0.3044996858, -0.3476823568, -0.2974934578, 0.1295005232, -0.156553477, 0.0655864552, -0.120824039, 0.5027754307, -0.0027679, 0.2299740911, -0.7161695957, -0.0190214328, -0.0780350342, 0.1363912672, 0.3357873559, 0.1799354851, -0.4840032458, 0.0554325432, -0.6231790781, 0.2310371697, -0.1487981975, 0.2327839732, 0.2179002613, -0.1323736459, -0.2544661164, -0.1587672085, 0.0788268, -0.0108902436, 0.0705949366, 0.2518509328, -0.5487610698, 0.0848161578, -0.0806653798, 0.5452415347, 0.2350126356, -0.5260224938, 0.1158930808, -0.3511397243, -0.176871419, -0.158860907, 0.1966926754, 0.9798125625, 0.1160485819, 0.2057992816, 0.2014113069, -0.206781745, 0.2640373111, 0.0135713108, 0.0519998521]'}, excluded_embed_metadata_keys=['_id', 'transit', 'minimum_nights', 'maximum_nights', 'cancellation_policy', 'last_scraped', 'calendar_last_scraped', 'first_review', 'last_review', 'security_deposit', 'cleaning_fee', 'guests_included', 'host', 'availability', 'reviews', 'image_embeddings'], excluded_llm_metadata_keys=['_id', 'transit', 'minimum_nights', 'maximum_nights', 'cancellation_policy', 'last_scraped', 'calendar_last_scraped', 'first_review', 'last_review', 'security_deposit', 'cleaning_fee', 'guests_included', 'host', 'availability', 'reviews', 'image_embeddings'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='af6b2fdb-b36d-4901-8f72-f2da91fd582d', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'_id': 30507309, 'listing_url': 'https://www.airbnb.com/rooms/30507309', 'name': 'luxury apartment near Downtown', 'summary': 'the apartment is located 6 minute away from downtown, very cozy place to spend time with friends and family. the apartment put you at the center of it all; restaurent nearby, groceries store etc. everything is at a walking distance, the subway, bus stop. you will be able to enjoy the city like a real Canadian', 'space': 'the apartment has many accessories: . 2 Tv  .bell cable .Apple TV .Netflix .xbox one .ps4 .printer  .sound system and many more accessories', 'description': \"the apartment is located 6 minute away from downtown, very cozy place to spend time with friends and family. the apartment put you at the center of it all; restaurent nearby, groceries store etc. everything is at a walking distance, the subway, bus stop. you will be able to enjoy the city like a real Canadian the apartment has many accessories: . 2 Tv  .bell cable .Apple TV .Netflix .xbox one .ps4 .printer  .sound system and many more accessories the neighborhood I very cozy, not a lot of noise. you will find everything you. need at walking distance if you don't have a car its really not a problem, you have the bus   stop in front of the apartment. you can take the 165 to get to downtown. 166 to go and visit Oratoire St Joseph\", 'neighborhood_overview': 'the neighborhood I very cozy, not a lot of noise. you will find everything you. need at walking distance', 'notes': '', 'transit': \"if you don't have a car its really not a problem, you have the bus   stop in front of the apartment. you can take the 165 to get to downtown. 166 to go and visit Oratoire St Joseph\", 'access': '', 'interaction': '', 'house_rules': '', 'property_type': 'Apartment', 'room_type': 'Entire home/apt', 'bed_type': 'Real Bed', 'minimum_nights': 2, 'maximum_nights': 16, 'cancellation_policy': 'flexible', 'last_scraped': 1552276800000, 'calendar_last_scraped': 1552276800000, 'first_review': 1545627600000, 'last_review': 1545627600000, 'accommodates': 5, 'bedrooms': 2.0, 'beds': 2.0, 'number_of_reviews': 1, 'bathrooms': 1.5, 'amenities': '[\"TV\", \"Wifi\", \"Air conditioning\", \"Pool\", \"Kitchen\", \"Elevator\", \"Free street parking\", \"Heating\", \"Washer\", \"Dryer\", \"Smoke detector\", \"Carbon monoxide detector\", \"Essentials\", \"Shampoo\", \"Lock on bedroom door\", \"Iron\", \"Laptop friendly workspace\", \"Private living room\", \"Outlet covers\", \"Game console\", \"Hot water\", \"Microwave\", \"Coffee maker\", \"Refrigerator\", \"Dishwasher\", \"Dishes and silverware\", \"Cooking basics\", \"Oven\", \"Stove\", \"Single level home\", \"Patio or balcony\", \"Cleaning before checkout\", \"Step-free access\", \"Accessible-height bed\", \"Host greets you\"]', 'price': 200, 'security_deposit': None, 'cleaning_fee': None, 'extra_people': 0, 'guests_included': 1, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/d75fb052-9a30-43ea-a17d-dac8db34761e.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"172923728\", \"host_url\": \"https://www.airbnb.com/users/show/172923728\", \"host_name\": \"Alec\", \"host_location\": \"CA\", \"host_about\": \"\", \"host_response_time\": \"within an hour\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/user/df0cea70-d9d7-46ae-9faf-f82624c8dbb1.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/user/df0cea70-d9d7-46ae-9faf-f82624c8dbb1.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"Cote-des-Neiges\", \"host_response_rate\": 100, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 1, \"host_total_listings_count\": 1, \"host_verifications\": [\"phone\", \"selfie\"]}', 'address': '{\"street\": \"Montr\\\\u00e9al, Qu\\\\u00e9bec, Canada\", \"suburb\": \"Cote-des-Neiges-Notre-Dame-de-Grace\", \"government_area\": \"C\\\\u00f4te-des-Neiges-Notre-Dame-de-Gr\\\\u00e2ce\", \"market\": \"Montreal\", \"country\": \"Canada\", \"country_code\": \"CA\", \"location\": {\"type\": \"Point\", \"coordinates\": [-73.60754, 45.49448], \"is_location_exact\": true}}', 'availability': '{\"availability_30\": 30, \"availability_60\": 60, \"availability_90\": 90, \"availability_365\": 90}', 'review_scores': '{\"review_scores_accuracy\": null, \"review_scores_cleanliness\": null, \"review_scores_checkin\": null, \"review_scores_communication\": null, \"review_scores_location\": null, \"review_scores_value\": null, \"review_scores_rating\": null}', 'reviews': '[{\"_id\": \"361861495\", \"date\": 1545627600000, \"listing_id\": \"30507309\", \"reviewer_id\": \"177268073\", \"reviewer_name\": \"Stephh\", \"comments\": \"The host canceled this reservation 5 days before arrival. This is an automated posting.\"}]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': '[0.0936508328, 0.2934384048, -0.1971703321, -0.036242418, -0.1308076829, -0.5545901656, 0.0703001693, 0.7066923976, 0.0367466025, 0.3122252524, 0.0575124398, -0.0176410545, 0.23645854, -0.3418642282, 0.2401382327, -0.2683159709, -0.4907199442, -0.0838668272, 0.2047564238, 0.1241259426, 0.6298086047, -0.0594352894, 0.17609635, 0.3586091697, -0.1152842492, 0.3271628618, 0.3562264442, -0.401230365, -0.0324097872, 0.0257515199, 0.2367218584, 0.4149738252, 0.0623220019, 0.1230640188, -0.6417615414, 0.4289738834, -0.2390958965, -0.3937125504, 0.0343358144, 1.3376479149, -0.2820866406, 0.2867327332, 0.3220860958, 0.0718876123, 0.0310478248, -2.4133234024, -0.1746396571, 0.6687079072, -0.191295594, -0.3058785796, 0.2382543534, 0.0172601566, 0.4137854576, 0.3177392483, 0.173821032, -0.6530903578, -0.1661632955, 0.0808606222, -0.1998483241, 0.2719935477, -1.8883548975, -0.5251950026, 0.3081382811, 0.0225291643, -0.11023397, -0.3362475634, 0.4266829789, -0.4477331936, -0.1139914319, 0.0808719099, -0.0515247695, -0.2638714015, 0.0207354464, 0.1511707604, -0.2066265345, 0.0632327646, -0.3621925414, -0.0039675459, -0.3030139208, -0.7638857961, -0.0083576255, 0.0057815015, 0.4586983323, 0.1093195006, 0.2378947735, -0.2319231629, 0.4890350699, -0.4177411795, -0.3584872782, 0.0620955341, 0.4642188549, 0.0553483516, -6.165699482, -0.3408660591, -0.1619065106, 0.2709009349, 0.2610371709, -0.0420166478, 0.5491482019, -0.2053907365, 0.1081285775, 0.2007240951, 0.2593016326, -0.3949846923, 0.44777897, -0.3637988269, 0.1364564151, -0.0422127172, -0.0504126772, 0.2430855334, -0.6666798592, 0.0356527865, -0.2137417495, 0.4018095434, -0.0614395477, -0.4291506112, 0.1719515622, 0.2576130033, 0.2563770711, 0.0531287193, -0.1573293358, -0.3043266833, 0.3812526464, 0.074896656, -0.0942599475, -0.3126021028, -0.1351081133, -0.2288512886, -0.3413164318, -0.2898048759, 0.1039129123, 0.3044900596, -0.1357839704, 0.7858344316, 0.0563879162, -0.2873725593, -0.129681021, 0.9556832314, -0.0980376452, 0.387187928, 0.4048145115, 0.2455666959, 0.7763134837, 0.1500092596, 0.0431036055, 0.1755233705, -0.1536914408, 0.4151951373, -0.0907365829, 0.3759293556, -0.1575253308, 0.0810023174, -0.152135551, -0.3514503539, 0.150087893, -0.3329392374, -0.3484278917, -0.0894963592, 0.28538993, -0.5667915344, 0.2878194749, -0.3309826255, 0.0950611308, 0.1416505426, 0.059813112, 0.326878041, 0.3416688144, -0.1071731746, 0.0039769039, 0.0809016377, 0.1889898032, 0.1132745668, -0.073839657, -0.090536125, 0.1542532742, 0.086439155, 0.2940548658, -0.0658153221, -0.6451060772, 0.136744976, -0.5218996406, -0.6433677077, 0.1812835634, -0.0225592032, -0.218790859, 0.030007707, -0.3147239387, 0.0395843387, 0.1397807896, -0.2037294805, 0.1303156614, 0.2028984129, 0.1701811403, -0.3757658601, 0.1266909242, -0.0675171539, 0.3045350909, 0.2398714572, -0.7187101245, 0.0730339587, 0.552485168, -0.3183240891, 0.2240779996, 0.1693822145, 0.2618731558, 0.0646095127, 0.3206186295, 0.1912883669, 0.0532983877, 0.3020588458, -0.6477378011, 0.5574151874, -0.2308274209, 0.1858152002, -0.2367836386, -0.3235510588, 0.2729896903, -0.2735707462, 0.6218007207, 0.0988899767, 0.2976068556, -0.0693745911, 0.4176607132, -0.1187660471, 0.1006759778, 0.0822289139, -0.1496759653, 0.1714290977, -0.652474463, 0.1693969071, 0.0710318983, 0.250695616, 0.2473854572, 0.5122967362, 0.4487681091, -0.1047468409, 0.032669045, 0.1507998705, -0.4412857294, -0.2955643535, 0.0418169908, -0.2061197162, -0.0490035713, -0.4358876348, 0.5679101944, 1.085180521, 0.1863981932, 0.3763158023, -0.1436301619, -0.4404369295, 0.0640965849, 0.0279755071, 0.2039414346, -0.148682192, -0.1436197758, 0.5396843553, -0.292086035, 0.1002515703, 0.7562980652, -0.240588218, 0.0274934433, 0.003957279, 0.2560417056, 0.302500844, 0.0691101998, 0.0979272649, 0.2681129575, -0.2618680596, -0.0495767072, 0.2786444724, 0.271064043, 0.3185680807, 0.3334520459, 0.0356190838, 0.2208519131, -0.5344493389, 0.3520115316, -0.3276404142, 0.0165878683, 0.0263864659, 0.3580197096, -0.3373585045, 1.0286982059, 0.5971220136, 0.0683153495, -0.348387301, 0.2484560609, -0.0706661716, -0.2046740651, -0.2614763975, 0.5054394603, -0.2704466879, 0.2676596642, -0.0996818915, -0.0902254581, 0.2712615728, -0.4692640305, 0.2040676475, -0.3840078712, -0.3669686317, -0.13712354, 0.413377434, 0.1280419528, 0.1807151586, 0.2464874387, -0.8473085761, 0.276778549, 0.1056393012, -0.1459106207, 0.1808351874, 0.1001494825, 0.1080775186, 0.7838820815, 0.3605751097, 0.0201069191, 0.2927002609, 0.3358390629, -0.2365003824, 0.1157880574, 0.3784732223, 0.0292614903, -0.2722353935, 0.0790782869, 0.155871138, -0.4979914725, 0.1811427772, 0.0011449233, 0.1367296576, 0.2438020259, -0.0180753842, -0.1500319839, -0.3702911437, 0.0850403085, -0.0034136735, -0.1555725336, -0.2515790761, -0.0526188239, 0.0709152371, -0.3321228325, -0.0679291338, -0.0751454383, 0.2116616964, -0.0416442938, -0.037087474, -0.1784698218, -0.1398064494, -0.0429410189, 0.2556550205, -0.0298438892, -0.3578060865, 1.2530721426, 0.3115640879, -0.1433243901, -0.1238279641, 0.3007592261, 0.6440600157, 0.0640157908, 0.8837527633, -0.3647267818, -0.0852599517, -0.0553782359, 0.1646501124, -0.3031951785, 0.084736295, -0.4819566905, 0.249663651, -0.0458876938, -0.2939544916, -0.3059579432, -0.422523886, -0.1878985763, -0.0600410849, 0.0710275173, -0.0179526266, 0.1731146723, 0.4978395402, -0.4139511585, 0.2558306754, 0.5786162615, -0.126276955, -0.0576158129, -0.2009733468, -0.012724054, 0.5874778628, 0.4462074339, -0.131814152, -0.501883924, -0.0402033925, -0.0243354961, -0.6816082597, 0.0382400118, 0.3575153351, -0.0178200305, 0.3436185122, -0.5882027149, -0.0872937888, 0.0163958576, -0.3335332274, 0.6096326113, -0.1430576146, -0.2621991932, 0.2161331028, 1.3086111546, -0.0525269359, 0.1280938089, -0.3650610447, 0.0732507706, -0.2491031736, -0.0652113557, -0.1270372868, 0.248532936, 0.2272706032, 0.1333750486, 0.3045102954, 0.1271125376, 0.4659550786, -0.4250519872, 0.1534260958, -0.3051880598, -0.0225665495, 0.5961506963, 0.1010922864, -0.2201724946, -0.3010082543, -0.3826218247, -0.1574632376, -0.1640392095, 0.5102485418, -1.0383553505, -0.06934122, -0.702436626, -0.6363373399, -0.3490189314, -0.0756868199, -0.0090990774, -0.1148631796, -0.1267162412, 0.1318321973, 0.1028501391, -0.188444078, -0.6364668012, 0.1462859958, 0.4813495874, 0.1454141587, -0.0163059961, -0.1456854939, -0.2061395198, 0.1601860225, -0.0493925884, -0.3427640498, 0.103279829, 0.1533657163, 0.0049643144, 1.1080428362, -0.1956967115, 0.1710280031, 0.3573034108, -0.3422089815, 0.5455297828, 0.4575739801, -0.1050536335, -0.2143058181, 0.5122144818, 0.3044996858, -0.3476823568, -0.2974934578, 0.1295005232, -0.156553477, 0.0655864552, -0.120824039, 0.5027754307, -0.0027679, 0.2299740911, -0.7161695957, -0.0190214328, -0.0780350342, 0.1363912672, 0.3357873559, 0.1799354851, -0.4840032458, 0.0554325432, -0.6231790781, 0.2310371697, -0.1487981975, 0.2327839732, 0.2179002613, -0.1323736459, -0.2544661164, -0.1587672085, 0.0788268, -0.0108902436, 0.0705949366, 0.2518509328, -0.5487610698, 0.0848161578, -0.0806653798, 0.5452415347, 0.2350126356, -0.5260224938, 0.1158930808, -0.3511397243, -0.176871419, -0.158860907, 0.1966926754, 0.9798125625, 0.1160485819, 0.2057992816, 0.2014113069, -0.206781745, 0.2640373111, 0.0135713108, 0.0519998521]'}, hash='924205d48d3d161a929f1da821571431b86ca63fc0e918d7e5ffeca18116d680'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='c260aa57-fb8f-4575-9344-29953c1f2ea6', node_type=<ObjectType.TEXT: '1'>, metadata={'_id': 30507198, 'listing_url': 'https://www.airbnb.com/rooms/30507198', 'name': 'Sands Of Kahana 234 - 2 Bedroom, 2 Bathroom | Sleeps 6', 'summary': 'SPRING SAVINGS IN APRIL! 20% OFF! Beautiful remodeled corner unit with Central AC and panoramic sunsets!', 'space': 'Enjoy views of Molokai and Lanai in one of nicest corner units in the Sands of Kahana.  This air conditioned 2 bedroom, 2 bath unit with two lanais is one of the most spacious waterfront units on Maui. From the front balcony you are so close to the ocean that you can toss out a flower and watch it gently float back to the beach. Off the second bedroom balcony, you can watch the surfers at S Turn or maybe a turtle sunbathing on the beach. And don’t forget enjoy the nightly sunsets and the whales when they visit during the winter. When you enter the unit you are welcomed by the spectacular ocean view. The large kitchen has beautiful granite counters and modern appliances. The cabinets are a rich cherry wood. Draw the ice for your drinks directly from the refrigerator.  You can create Mai Tais or blend a Pina Colada. Although most visitors enjoy the Maui cuisine of the West Side restaurants, you have the choice of using the gourmet kitchen or simply going down to the beach to use one of t', 'description': 'SPRING SAVINGS IN APRIL! 20% OFF! Beautiful remodeled corner unit with Central AC and panoramic sunsets! Enjoy views of Molokai and Lanai in one of nicest corner units in the Sands of Kahana.  This air conditioned 2 bedroom, 2 bath unit with two lanais is one of the most spacious waterfront units on Maui. From the front balcony you are so close to the ocean that you can toss out a flower and watch it gently float back to the beach. Off the second bedroom balcony, you can watch the surfers at S Turn or maybe a turtle sunbathing on the beach. And don’t forget enjoy the nightly sunsets and the whales when they visit during the winter. When you enter the unit you are welcomed by the spectacular ocean view. The large kitchen has beautiful granite counters and modern appliances. The cabinets are a rich cherry wood. Draw the ice for your drinks directly from the refrigerator.  You can create Mai Tais or blend a Pina Colada. Although most visitors enjoy the Maui cuisine of the West Side restau', 'neighborhood_overview': 'The Sands of Kahana does have a great beach out front for swimming and other water activities.  There is also a great restaurant on site as well as a few within walking distance such as Miso Phat Sushi and Maui Brewing Company.', 'notes': '', 'transit': 'We recommend renting a vehicle while on Maui as there are so many amazing things to see on this island and having a car makes it easy!  Parking is included in your fees; the front desk will issue you a parking pass.  This property does not have assigned parking but there should be plenty of stalls available.  If you do not wish to rent a car, public transportation is nearby.', 'access': 'The Sands of Kahana has a 24-hour front desk, please check in with them and they will issue you a key for the condo!  All property facilities (pools, bbq’s, gym’s, tennis courts, beach volley ball) are available for your use.  Beach towels are provided in the condo!', 'interaction': 'The Sullivan Properties office is located in Kahana in the business building across from the gas station and McDonald’s.  We are open 7 days a week and have an emergency line for after hours if needed!', 'house_rules': 'We have\\xa0no long distance phone service, so please bring your cellular phone or phone card! RATES, TAXES, CHECK-OUT CLEAN CHARGE & REGISTER OR MISC. FEES\\xa0Descriptions and rates are subject to change without notice. State of Hawaii Transient Accommodations Tax\\xa0(10.25%) and General Excise Tax (4.167%) are charged on each reservation. Rates do not include daily maid service. Daily maid service is available at additional cost. Homeowner Association check-in fees, local telephone charges, parking fees or Resort fees may apply. Rates are subject to change without notice. Rates will vary based on the condo location, the condo size and the time of year. CHECK-IN PROCEDURES: SORRY, NO LATE CHECK OUTS!\\xa0\\xa0Please phone us if you do not understand your entry condo access or keying instructions! Check-in time is after 3:00 PM. Check-out time is at 11:00 AM. You may need to rent another day for Departing Red Eye Flights! Sullivan Properties will provide a condominiums lock box code or other arrival ent', 'property_type': 'Condominium', 'room_type': 'Entire home/apt', 'bed_type': 'Real Bed', 'minimum_nights': 4, 'maximum_nights': 180, 'cancellation_policy': 'super_strict_60', 'last_scraped': 1551848400000, 'calendar_last_scraped': 1551848400000, 'first_review': None, 'last_review': None, 'accommodates': 6, 'bedrooms': 2.0, 'beds': 2.0, 'number_of_reviews': 0, 'bathrooms': 2.0, 'amenities': '[\"TV\", \"Cable TV\", \"Wifi\", \"Air conditioning\", \"Pool\", \"Kitchen\", \"Gym\", \"Elevator\", \"Hot tub\", \"Washer\", \"Dryer\", \"Smoke detector\", \"Essentials\", \"Shampoo\", \"Hangers\", \"Hair dryer\", \"Iron\", \"Laptop friendly workspace\", \"Private living room\", \"Private entrance\", \"Bed linens\", \"Microwave\", \"Coffee maker\", \"Refrigerator\", \"Dishwasher\", \"Dishes and silverware\", \"Cooking basics\", \"Oven\", \"Stove\", \"BBQ grill\"]', 'price': 515, 'security_deposit': 0.0, 'cleaning_fee': 339.0, 'extra_people': 0, 'guests_included': 6, 'images': '{\"thumbnail_url\": \"\", \"medium_url\": \"\", \"picture_url\": \"https://a0.muscache.com/im/pictures/e29196b7-cb6a-4fa8-a045-f15eb18a190f.jpg?aki_policy=large\", \"xl_picture_url\": \"\"}', 'host': '{\"host_id\": \"146182295\", \"host_url\": \"https://www.airbnb.com/users/show/146182295\", \"host_name\": \"Kelly\", \"host_location\": \"Lahaina, Hawaii, United States\", \"host_about\": \"Aloha & Welcome to Maui Resorts at Sullivan Properties, offering affordable Vacation Condo Rentals on the tropical paradise island getaway of West Maui, Hawaii. We have been providing visitors and residents with the best Maui Vacation Condo Rentals, Real Estate Sales, Property Management Services, Activities and Car Rentals for over 20 years. We can provide you with the best quality, selection, value and service on the \\\\\"West Side\\\\\" of Maui. We specialize in properties located in the: Kapalua Resort, Napili, Kahana, Mahinahina, Honokowai, Kaanapali, Lahaina, and Olowalu areas of West Maui.\\\\r\\\\n\\\\r\\\\nWe offer tastefully furnished and spacious accommodations at all of Sullivan\\'s managed resort properties. Enjoy complete kitchens that offer home-like conveniences. Golden sand beaches, championship golf courses, gorgeous sunsets and pristine views of both the West Maui Mountains and the Pacific Ocean are all part of the vacation experience.\", \"host_response_time\": \"within a few hours\", \"host_thumbnail_url\": \"https://a0.muscache.com/im/pictures/300a6c8b-bbc5-48c8-b7ee-4ba1811b8aa8.jpg?aki_policy=profile_small\", \"host_picture_url\": \"https://a0.muscache.com/im/pictures/300a6c8b-bbc5-48c8-b7ee-4ba1811b8aa8.jpg?aki_policy=profile_x_medium\", \"host_neighbourhood\": \"\", \"host_response_rate\": 100, \"host_is_superhost\": false, \"host_has_profile_pic\": true, \"host_identity_verified\": false, \"host_listings_count\": 68, \"host_total_listings_count\": 68, \"host_verifications\": [\"email\", \"phone\", \"reviews\"]}', 'address': '{\"street\": \"Lahaina, HI, United States\", \"suburb\": \"Westside\", \"government_area\": \"Lahaina\", \"market\": \"Maui\", \"country\": \"United States\", \"country_code\": \"US\", \"location\": {\"type\": \"Point\", \"coordinates\": [-156.68012, 20.96898], \"is_location_exact\": false}}', 'availability': '{\"availability_30\": 1, \"availability_60\": 23, \"availability_90\": 43, \"availability_365\": 215}', 'review_scores': '{\"review_scores_accuracy\": null, \"review_scores_cleanliness\": null, \"review_scores_checkin\": null, \"review_scores_communication\": null, \"review_scores_location\": null, \"review_scores_value\": null, \"review_scores_rating\": null}', 'reviews': '[]', 'weekly_price': None, 'monthly_price': None, 'image_embeddings': 'null'}, hash='d9f9b127e5f8e57ca131c436e815d67d9a7c0c75e9e05ca4692c4e19fd65d089'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='11e25560-9ac5-42c2-95f2-1f54c95035c2', node_type=<ObjectType.TEXT: '1'>, metadata={}, hash='effb83f20da0b83391b9d4270ac5995edcc8cd87dbb232d41e7d1db7155a351b')}, text=\"the apartment is located 6 minute away from downtown, very cozy place to spend time with friends and family. the apartment put you at the center of it all; restaurent nearby, groceries store etc. everything is at a walking distance, the subway, bus stop. you will be able to enjoy the city like a real Canadian the apartment has many accessories: . 2 Tv  .bell cable .Apple TV .Netflix .xbox one .ps4 .printer  .sound system and many more accessories the neighborhood I very cozy, not a lot of noise. you will find everything you. need at walking distance if you don't have a car its really not a problem, you have the bus   stop in front of the apartment. you can take the 165 to get to downtown. 166 to go and visit Oratoire St Joseph\", start_char_idx=0, end_char_idx=736, text_template='Metadata: {metadata_str}\\n-----\\nContent: {content}', metadata_template='{key}=>{value}', metadata_seperator='\\n'), score=0.7604679465293884)]\n"]}], "source": ["import pprint\n", "\n", "from llama_index.core.response.notebook_utils import display_response\n", "\n", "query_engine = index.as_query_engine(similarity_top_k=3)\n", "\n", "query = \"I want to stay in a place that's warm and friendly, and not too far from resturants, can you recommend a place? Include a reason as to why you've chosen your selection\"\n", "\n", "response = query_engine.query(query)\n", "display_response(response)\n", "pprint.pprint(response.source_nodes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iLvSnEysqdbP"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 0}