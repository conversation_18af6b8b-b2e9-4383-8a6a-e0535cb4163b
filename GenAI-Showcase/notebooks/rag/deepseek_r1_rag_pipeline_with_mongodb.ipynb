{"cells": [{"cell_type": "markdown", "metadata": {"id": "ynFM43qnrEJP"}, "source": ["# DeepSeek and MongoDB For Movie Recommendation System\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/deepseek_r1_rag_pipeline_with_mongodb.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)]()"]}, {"cell_type": "markdown", "metadata": {"id": "pw-5v8H_rXOV"}, "source": ["## Install Libaries and Set Environment Variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "z2cY51m4Wc6K", "outputId": "2608b3d8-7e39-48d8-fd57-eb84f3a709bf"}, "outputs": [], "source": ["!pip install --quiet -U pymongo sentence-transformers datasets accelerate"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "reglHoYMrZ3Z"}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "# Function to securely get and set environment variables\n", "def set_env_securely(var_name, prompt):\n", "    value = getpass.getpass(prompt)\n", "    os.environ[var_name] = value"]}, {"cell_type": "markdown", "metadata": {"id": "4apT5j46rsfO"}, "source": ["## Step 1: Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 113, "referenced_widgets": ["32a6161ea2ea4957829729afa1720584", "599f54303eb74b05aa806c3228a956a7", "2a996a97b6a74fc3b462e2373c901692", "6878311b22b94ae79c4d1700a0c1410e", "a3b2a5214d4d47dcace266a15ee6568d", "cea0e1aa862e4fd7b2c790cef07d175f", "11c88d53c1d4498dafd3b255cb8ee653", "471bd7b9f0cc4a41aad91bd2a0fa0b74", "f4c8b7e7c4eb499f94de9c611e90b663", "dc965db378fe4995bc92d4606541ecc5", "ad9e4414b262401ba1cc088be291de8b", "7137f86efff146cea416da5c637b55ec", "a93bbc80b5f24583b72a710c7e946094", "3769dbbf521843aea1ce4f367484c080", "430b3c93637a4aeb908f82c4812d2897", "bd30390bc99d42bbac96c3c9f8a2858a", "c94a1b82bcb946378b0c8d7c5fd1388f", "8ca9c3dd6b2c42b4abee90b9a3a7b9b2", "d9089aeb40654caeb517ad2f1dc0e733", "5bb0697d0ffb4a0aba9b3bd9b08d3146", "6918f348708943b6b4220c7a2fdd178a", "c76b90bab1f242e8b3402425aeefc0d3", "7e975d28657041058355c18716c5fa48", "d65b9c8d3cbe48f49b764d4cc85f852f", "9bf0a927a8dd4c82bfef900f29e69ce6", "c45f7981bfc64061a113bffddbddce9e", "c13377e46cf146e6a62102cd51710d9f", "c4144e1058e04ee5a87e40cfb7795441", "123a170f4f514bc58ed44b4a21015538", "6c1bd92a1aad4ef6aa47c94c6e3f0898", "708de4722f1b41cda3d321888a3cbfb7", "caec76179d6b467f80ae27ad7ae541e2", "1a299210afc24c06ba25dfee47e60f3d"]}, "id": "hfQERNtFr9T7", "outputId": "fcfef985-2d05-45bd-8c9c-5d6af8e8f2ff"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "32a6161ea2ea4957829729afa1720584", "version_major": 2, "version_minor": 0}, "source": ["## Install Libraries and Set Environment Variables"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7137f86efff146cea416da5c637b55ec", "version_major": 2, "version_minor": 0}, "text/plain": ["sample_mflix.embedded_movies.json:   0%|          | 0.00/42.3M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7e975d28657041058355c18716c5fa48", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/1500 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load Dataset\n", "import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# https://huggingface.co/datasets/MongoDB/embedded_movies\n", "dataset = load_dataset(\"MongoDB/embedded_movies\")\n", "\n", "# Convert the dataset to a pandas DataFrame\n", "dataset_df = pd.DataFrame(dataset[\"train\"])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MLMQYeqLsBBL", "outputId": "7aafe645-6c2f-4236-ee88-30f61b4a6af2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Number of missing values in each column after removal:\n", "plot                    0\n", "runtime                14\n", "genres                  0\n", "fullplot                0\n", "directors              12\n", "writers                13\n", "countries               0\n", "poster                 78\n", "languages               1\n", "cast                    1\n", "title                   0\n", "num_mflix_comments      0\n", "rated                 279\n", "imdb                    0\n", "awards                  0\n", "type                    0\n", "metacritic            893\n", "plot_embedding          1\n", "dtype: int64\n"]}], "source": ["# Remove data point where plot column is missing\n", "dataset_df = dataset_df.dropna(subset=[\"fullplot\"])\n", "print(\"\\nNumber of missing values in each column after removal:\")\n", "print(dataset_df.isnull().sum())\n", "\n", "# Remove the plot_embedding from each data point in the dataset as we are going to create new embeddings with an open-source embedding model from Hugging Face: gte-large\n", "dataset_df = dataset_df.drop(columns=[\"plot_embedding\"])"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 747}, "id": "UqUj9A7i4Ed_", "outputId": "453c495b-7c31-43bc-d34d-b8d4d66f01fd"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1452,\n  \"fields\": [\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> saves Tokyo from a flying saucer that transforms into the beast <PERSON><PERSON>.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.569335235764626,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 137,\n        \"samples\": [\n          60.0,\n          151.0,\n          110.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"God<PERSON> returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\",\n          \"Relationships become entangled in an emotional web.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1332,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BMTQ2NTMxODEyNV5BMl5BanBnXkFtZTcwMDgxMjA0MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTY5OTg1ODk0MV5BMl5BanBnXkFtZTcwMTEwMjU1MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTMyMjgyMDIyM15BMl5BanBnXkFtZTcwNjg3MjAyMg@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1391,\n        \"samples\": [\n          \"Superhero Movie\",\n          \"Hooper\",\n          \"Sivaji\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134,\n          124\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\",\n          \"TV-G\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.855402595666064,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "dataset_df"}, "text/html": ["\n", "  <div id=\"df-89194d2b-f72e-4715-abac-982cfc678a25\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>plot</th>\n", "      <th>runtime</th>\n", "      <th>genres</th>\n", "      <th>fullplot</th>\n", "      <th>directors</th>\n", "      <th>writers</th>\n", "      <th>countries</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>cast</th>\n", "      <th>title</th>\n", "      <th>num_mflix_comments</th>\n", "      <th>rated</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>type</th>\n", "      <th>metacritic</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>199.0</td>\n", "      <td>[Action]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.06366512924432755, 0.05893123149871826, -0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>22.0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>0</td>\n", "      <td>TV-G</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.04760091006755829, -0.008872468955814838, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>101.0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>[USA]</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[0.022996366024017334, 0.10801853239536285, -0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>88.0</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>The Black Pirate</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.07819894701242447, 0.11125769466161728, -0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>58.0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>For Heaven's Sake</td>\n", "      <td>0</td>\n", "      <td>PASSED</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.014855025336146355, 0.09593196213245392, -...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-89194d2b-f72e-4715-abac-982cfc678a25')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-89194d2b-f72e-4715-abac-982cfc678a25 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-89194d2b-f72e-4715-abac-982cfc678a25');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-a7e9e1b3-87db-4511-9a26-39d2d90b349e\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-a7e9e1b3-87db-4511-9a26-39d2d90b349e')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-a7e9e1b3-87db-4511-9a26-39d2d90b349e button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["                                                plot  runtime  \\\n", "0  Young <PERSON> is left a lot of money when her ...    199.0   \n", "1  A penniless young man tries to save an heiress...     22.0   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...    101.0   \n", "3  Seeking revenge, an athletic young man joins t...     88.0   \n", "4  An irresponsible young millionaire changes his...     58.0   \n", "\n", "                       genres  \\\n", "0                    [Action]   \n", "1     [Comedy, Short, Action]   \n", "2  [Action, Adventure, Drama]   \n", "3         [Adventure, Action]   \n", "4   [Action, Comedy, Romance]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                             writers countries  \\\n", "0  [<PERSON> (screenplay), <PERSON>...     [USA]   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]     [USA]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...     [USA]   \n", "3  [<PERSON> (story), <PERSON> (a...     [USA]   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.     [USA]   \n", "\n", "                                              poster  languages  \\\n", "0  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2                                               None  [English]   \n", "3  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                                cast                  title  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...  The Perils of Pauline   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     From Hand to Mouth   \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>             <PERSON>   \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>..       The Black Pirate   \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.      For Heaven's Sake   \n", "\n", "   num_mflix_comments   rated                                         imdb  \\\n", "0                   0    None    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1                   0    TV-G   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2                   0    None   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3                   1    None  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4                   0  PASSED   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                              awards   type  metacritic  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "\n", "                                           embedding  \n", "0  [-0.06366512924432755, 0.05893123149871826, -0...  \n", "1  [-0.04760091006755829, -0.008872468955814838, ...  \n", "2  [0.022996366024017334, 0.10801853239536285, -0...  \n", "3  [-0.07819894701242447, 0.11125769466161728, -0...  \n", "4  [-0.014855025336146355, 0.09593196213245392, -...  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "0qXXgVE6sE-v"}, "source": ["## Step 2: Generating Embeddings"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"id": "xsrBO2QIs5aE"}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer\n", "\n", "# Load the embedding model\n", "embedding_model = SentenceTransformer(\"sentence-transformers/all-MiniLM-L6-v2\")\n", "\n", "\n", "# Function to generate embeddings\n", "def generate_embedding(text):\n", "    return embedding_model.encode([text])[0].tolist()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "rCeBaEJwtPGN"}, "outputs": [], "source": ["dataset_df[\"embedding\"] = dataset_df[\"fullplot\"].apply(generate_embedding)"]}, {"cell_type": "markdown", "metadata": {"id": "20L_L9Q5tXPu"}, "source": ["## Step 3: MongoDB (Operational and Vector Database)\n", "\n", "MongoDB acts as both an operational and a vector database for the RAG system.\n", "MongoDB Atlas specifically provides a database solution that efficiently stores, queries and retrieves vector embeddings.\n", "\n", "Creating a database and collection within MongoDB is made simple with MongoDB Atlas.\n", "\n", "1. First, register for a [MongoDB Atlas account](https://www.mongodb.com/cloud/atlas/register). For existing users, sign into MongoDB Atlas.\n", "2. [Follow the instructions](https://www.mongodb.com/docs/atlas/tutorial/deploy-free-tier-cluster/). Select Atlas UI as the procedure to deploy your first cluster.\n", "\n", "Follow MongoDB’s [steps to get the connection](https://www.mongodb.com/docs/manual/reference/connection-string/) string from the Atlas UI. After setting up the database and obtaining the Atlas cluster connection URI, securely store the URI within your development environment.\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eEKqeQwBtWa5", "outputId": "3d4aa7da-2e7d-4762-afbb-51727807d82b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your MONGO URI: ··········\n"]}], "source": ["# Set MongoDB URI\n", "set_env_securely(\"MONGO_URI\", \"Enter your MONGO URI: \")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "5wU4XxAste1f"}, "outputs": [], "source": ["import pymongo\n", "\n", "\n", "def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish and validate connection to the MongoDB.\"\"\"\n", "\n", "    client = pymongo.MongoClient(\n", "        mongo_uri, appname=\"devrel.showcase.rag.deepseek_rag_movies.python\"\n", "    )\n", "\n", "    # Validate the connection\n", "    ping_result = client.admin.command(\"ping\")\n", "    if ping_result.get(\"ok\") == 1.0:\n", "        # Connection successful\n", "        print(\"Connection to MongoDB successful\")\n", "        return client\n", "    else:\n", "        print(\"Connection to MongoDB failed\")\n", "    return None\n", "\n", "\n", "MONGO_URI = os.environ[\"MONGO_URI\"]\n", "if not MONGO_URI:\n", "    print(\"MONGO_URI not set in environment variables\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pvsNoe4MtkWp", "outputId": "1ffaf9a1-8f7f-471e-f6e5-05f9c3590bd5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["mongo_client = get_mongo_client(MONGO_URI)\n", "\n", "DB_NAME = \"movies_database\"\n", "COLLECTION_NAME = \"movies_collection\"\n", "\n", "# Create or get the database\n", "db = mongo_client[DB_NAME]\n", "\n", "# Create or get the collections\n", "collection = db[COLLECTION_NAME]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0XY0lLK9tt_A", "outputId": "51cda56e-74e7-48c5-f6ba-1fa5f8c5a7f2"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 0, 'electionId': ObjectId('7fffffff000000000000003c'), 'opTime': {'ts': Timestamp(1738352202, 1), 't': 60}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1738352202, 1), 'signature': {'hash': b'\\xe4\\xa5\\xe1\\x04\\xcd\\xc6\\xcf\\x8aI\\xe2\\xbd:\\xc5\\xf6\\xa1\\xa1Jk\\xf6\\xea', 'keyId': 7421923411288391683}}, 'operationTime': Timestamp(1738352202, 1)}, acknowledged=True)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["collection.delete_many({})"]}, {"cell_type": "markdown", "metadata": {"id": "KU0KJC5etwhN"}, "source": ["## Step 4: Data Ingestion"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tppOAg0ltwAz", "outputId": "844e4cb8-75e3-4033-99ed-11984c4915c8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data ingestion into MongoDB completed\n"]}], "source": ["documents = dataset_df.to_dict(\"records\")\n", "collection.insert_many(documents)\n", "\n", "print(\"Data ingestion into MongoDB completed\")"]}, {"cell_type": "markdown", "metadata": {"id": "nwMXXMZtt_CJ"}, "source": ["## Step 5: Vector Index Creation"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "Jk1I6OqpPx85"}, "outputs": [], "source": ["# The field containing the text embeddings on each document within the shipping_data collection\n", "embedding_field_name = \"embedding\"\n", "# MongoDB Atlas Vector Search index name\n", "vector_search_index_name = \"vector_index\""]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "v2NGctXs-g5y"}, "outputs": [], "source": ["import time\n", "\n", "from pymongo.operations import SearchIndexModel\n", "\n", "\n", "def setup_vector_search_index(collection, index_definition, index_name=\"vector_index\"):\n", "    \"\"\"\n", "    Setup a vector search index for a MongoDB collection and wait for 30 seconds.\n", "\n", "    Args:\n", "    collection: MongoDB collection object\n", "    index_definition: Dictionary containing the index definition\n", "    index_name: Name of the index (default: \"vector_index\")\n", "    \"\"\"\n", "    new_vector_search_index_model = SearchIndexModel(\n", "        definition=index_definition, name=index_name, type=\"vectorSearch\"\n", "    )\n", "\n", "    # Create the new index\n", "    try:\n", "        result = collection.create_search_index(model=new_vector_search_index_model)\n", "        print(f\"Creating index '{index_name}'...\")\n", "\n", "        # Sleep for 30 seconds\n", "        print(f\"Waiting for 30 seconds to allow index '{index_name}' to be created...\")\n", "        time.sleep(30)\n", "\n", "        print(f\"30-second wait completed for index '{index_name}'.\")\n", "        return result\n", "\n", "    except Exception as e:\n", "        print(f\"Error creating new vector search index '{index_name}': {e!s}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "lSmEYB61-jma"}, "outputs": [], "source": ["def create_vector_index_definition(dimensions):\n", "    return {\n", "        \"fields\": [\n", "            {\n", "                \"type\": \"vector\",\n", "                \"path\": embedding_field_name,\n", "                \"numDimensions\": dimensions,\n", "                \"similarity\": \"cosine\",\n", "            }\n", "        ]\n", "    }"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "bsdaxKp1-ync"}, "outputs": [], "source": ["DIMENSIONS = 384\n", "vector_index_definition = create_vector_index_definition(dimensions=DIMENSIONS)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 0}, "id": "qH7cyEat-0EG", "outputId": "5337f4fd-893d-46aa-add7-f04d6be3fb00"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating index 'vector_index'...\n", "Waiting for 30 seconds to allow index 'vector_index' to be created...\n", "30-second wait completed for index 'vector_index'.\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'vector_index'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["setup_vector_search_index(collection, vector_index_definition, \"vector_index\")"]}, {"cell_type": "markdown", "metadata": {"id": "wwT2yKe1vbEH"}, "source": ["## Step 6: Vector Search Function"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"id": "y4Oo-hzMvZlU"}, "outputs": [], "source": ["def vector_search(user_query, top_k=150):\n", "    \"\"\"\n", "    Perform a vector search in the MongoDB collection based on the user query.\n", "\n", "    Args:\n", "    user_query (str): The user's query string.\n", "    collection (MongoCollection): The MongoDB collection to search.\n", "\n", "    Returns:\n", "    list: A list of matching documents.\n", "    \"\"\"\n", "\n", "    # Generate embedding for the user query\n", "    query_embedding = generate_embedding(user_query)\n", "\n", "    if query_embedding is None:\n", "        return \"Invalid query or embedding generation failed.\"\n", "\n", "    # Define the vector search pipeline\n", "    vector_search_stage = {\n", "        \"$vectorSearch\": {\n", "            \"index\": \"vector_index\",\n", "            \"queryVector\": query_embedding,\n", "            \"path\": \"embedding\",\n", "            \"numCandidates\": top_k,  # Number of candidate matches to consider\n", "            \"limit\": 5,  # Return top 4 matches\n", "        }\n", "    }\n", "\n", "    project_stage = {\n", "        \"$project\": {\n", "            \"_id\": 0,  # Exclude the _id field\n", "            \"fullplot\": 1,  # Include the plot field\n", "            \"title\": 1,  # Include the title field\n", "            \"genres\": 1,  # Include the genres field\n", "            \"score\": {\"$meta\": \"vectorSearchScore\"},  # Include the search score\n", "        }\n", "    }\n", "\n", "    pipeline = [vector_search_stage, project_stage]\n", "\n", "    # Execute the search\n", "    results = collection.aggregate(pipeline)\n", "    return list(results)"]}, {"cell_type": "markdown", "metadata": {"id": "RRPBlajEwW4j"}, "source": ["## Step 7: <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XoOFWgvcwawY", "outputId": "f751ddd4-fe99-4eae-eea1-8e44bc65c2a9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 5 results for query 'What are the some interesting action movies to watch that include business?':\n", "Title: Shanghai Express, Score: 0.7532\n", "Title: <PERSON><PERSON><PERSON><PERSON>, Score: 0.7137\n", "Title: Crime Story, Score: 0.7058\n", "Title: The Accidental Spy, Score: 0.6996\n", "Title: Hand Gun, Score: 0.6962\n"]}], "source": ["query = \"What are the some interesting action movies to watch that include business?\"\n", "\n", "get_knowledge = vector_search(query)\n", "\n", "pd.DataFrame(get_knowledge).head()\n", "\n", "print(f\"\\nTop 5 results for query '{query}':\")\n", "\n", "for result in get_knowledge:\n", "    print(f\"Title: {result['title']}, Score: {result['score']:.4f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "wqlDbbbHw8LJ"}, "source": ["## Step 8: Retrieval Augmented Generation(RA)"]}, {"cell_type": "markdown", "metadata": {"id": "ntQVOSTyw_Z0"}, "source": ["Load DeepSeek model from Hugging Face"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 646, "referenced_widgets": ["2c3a0dd6a1eb438dbcd46297053c1617", "0d0f70ea26984ad0b341292a89ca1ae9", "957f5f1c63c3466e8731a2ef1a8b0964", "50bac768ece44a02aaaf1e1df81cd340", "685344b8731f4614b104daeecb506349", "f77f228befc04fd286740c8e4008b0e3", "a648eaa1a4f64a6997f085c7834eeaf7", "26a980725fdb487f8e3d0415cbf607d7", "0d991641826e4451a5ebdb4329f3cbe3", "0f8c1a06cb9743bc8ac77616cd82fa9c", "c5c7ee3f1f2340c380e7050c41926f5d", "bf912b2aad0d4b0e9fbd15b82e7577a2", "1822d15a82044cc690b9ac8e27901c26", "7bff5ad84946419c9ba73cf860534d60", "b6456756effd4047b1fedfdbd4bbf539", "6b973cb0594a432cba4ea6f6adea5a19", "756df8b5de2c443e8e5db0b16fbd4a1f", "69894e27dab8404dbdc8d9ed2d82ac94", "0d8a7b4909114d269acf1df8c14c7000", "eb9515b9e492476e97f1ac02bc5ded8c", "a02309a17f604f17a23521bc0f5cdd35", "ebc9c48a0c054ed780ef5384c63544e4", "8c43cc01031b4da88ab2a12f16c9aff0", "93aad82624944342a250a980bd39e64d", "e578633949794b72b497fa20ad3e1ec4", "1c447ae3914e4ef38c4a1d368595eb60", "f62f85e997be42a09e9fcb1dfd6084b5", "529faa51648a4bdc967049ab75c67955", "2e135d23dfda40b5b1a0b09ec9e63b31", "e533b9b0a52447a1921c2c6a2a831da2", "5c0bac7a6fef40189972fc6cea95fa29", "52178876a7864fd4beb9bee548766c24", "dbcc51dcc3064c8d8269d3b4031d5a3c", "33a065062f134c0d975262bc6c7e5727", "491c140d231842b79a854a0311311990", "2778883ff60e43798fcf7515007dd890", "222e9dc70dea4916adca90fbdb86ae00", "68e099a39f4e4abcb2f9e8fdc0884f11", "2fab42c58ad845c5b66f0262e22fa8d4", "ed113bf3772c41e0a72891e524d7bc6f", "b2d4a4d6af984fc28c7a4e9a67b7a5d0", "b895f5fc35c3446e96e2a2637fb89127", "5eccc99196c94c52af0757d28c09090e", "34d42ac742a2473bb7c52a7557a36e39", "5fed7972422e49d992b564afa6bd4e44", "8e543fd471c74ba2a386c25d79f530d0", "36a6233658a54bec98670da58c6a1695", "c85e9694cdee437892bdfbdffdbf3788", "409308c8506e45b2a00ee2dcc74152f1", "0c77052a694f45a49a88e6ac190ce45e", "404bdac883a948a89b40520a27ca487e", "f0c1200a87f74ea88d3ce36ecfc99014", "214fc2ffd406429a9103d1e27b3985cb", "1d8f25114ad741ca85745d066b7d2495", "d77491778334421684b67d1db2ea29f9"]}, "id": "PUL9xWXqXGtP", "outputId": "b99f5da7-9c0d-48f7-eb22-ef001b5553fb"}, "outputs": [], "source": ["# Load model directly\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\n", "    \"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\", device_map=\"cuda\"\n", ")\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    \"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\"\n", ")\n", "\n", "model.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": 66, "metadata": {"id": "7esYaWvXXVF1"}, "outputs": [], "source": ["def rag_query(query):\n", "    query = (\n", "        \"What are the some interesting action movies to watch that include business?\"\n", "    )\n", "\n", "    get_knowledge = vector_search(query)\n", "\n", "    combined_information = f\"Query: {query}\\nContinue to answer the query by using the Search Results:\\n{get_knowledge}.\"\n", "\n", "    # Moving tensors to GPU\n", "    input_ids = tokenizer(combined_information, return_tensors=\"pt\").to(\"cuda\")\n", "    response = model.generate(**input_ids, max_new_tokens=1000)\n", "\n", "    return tokenizer.decode(response[0], skip_special_tokens=False)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TxqotBLf2zVj", "outputId": "d63d018a-3846-4262-c38d-0948c21b9cec"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting `pad_token_id` to `eos_token_id`:151643 for open-end generation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<｜begin▁of▁sentence｜>Query: What are the some interesting action movies to watch that include business?\n", "Continue to answer the query by using the Search Results:\n", "[{'genres': ['Action', 'Comedy', 'Western'], 'fullplot': \"Multi-genre flick (western, martial arts, comedy, adventure, etc.) with an all-star cast about a man who returns to his home town, buys everything in sight, and tries to improve its municipal (and his personal) profits by sabotaging a train so the passengers all have to stop in his town and spend lots o' money! Throw in various subplots involving some Japanese swordsmen, some bungling bankrobbers (one of whom is the head of security), and a gang of no-goods who try to mess up the town.\", 'title': 'Shanghai Express', 'score': 0.****************}, {'genres': ['Action', 'Horror', 'Thriller'], 'fullplot': 'A double-bill of thrillers that recall both filmmakers\\' favorite exploitation films. \"Grindhouse\" (a downtown movie theater in disrepair since its glory days as a movie palace known for \"grinding out\" non-stop double-bill programs of B-movies) is presented as one full-length feature comprised of two individual films helmed separately by each director. \"Death Proof,\" is a rip-roaring slasher flick where the killer pursues his victims with a car rather than a knife, while \"Planet Terror\" shows us a view of the world in the midst of a zombie outbreak. The films are joined together by clever faux trailers that recall the \\'50s exploitation drive-in classics.', 'title': 'Grindhouse', 'score': 0.***************}, {'genres': ['Action', 'Crime', 'Drama'], 'fullplot': \"A dark and handsome true-crime thriller about kidnapping and police corruption in Hong Kong. Once of Jackie Chan's most serious roles, but still overflowing with spectacular acrobatic sequences.\", 'title': 'Crime Story', 'score': 0.7057880163192749}, {'genres': ['Action', 'Comedy', 'Thriller'], 'fullplot': 'This action movie unfolds with the story of Bei, a salesman at a workout equipment store, who harbors dreams of adventures. It all starts when on one normal dull day, Bei follows his instincts to trail two suspicious looking men into an alley. When he realizes that these men are robbing a jewelry store, he jumps into action to foil their plans. Soon after Bei meets Liu, a private investigator who convinces Bei that he may be the long-lost son of a rich Korean businessman. In no time, Bei is on his way to fulfill his dreams of adventure and fortune travelling to Korea and even exotic Turkey. As Bei is drawn deeper into the game of cat and mouse, he realizes he has become the key to locating a lung cancer virus. With an assortment of characters fighting him along the way, will Bei succeed in finding the virus himself?', 'title': 'The Accidental Spy', 'score': 0.6996449828147888}, {'genres': ['Action', 'Crime', 'Thriller'], 'fullplot': \"It's _The Good, the Bad and the Ugly (1966)_ meets Simple Men (1992). George is a small-time crook. His brother Michael makes a living scamming the elderly by selling them nonexistent burial plots. When their father steals half a million dollars, the brothers find themselves on the run from the police and a local crime boss. But when Dad is killed, George and Michael have to team up to find the hidden loot. fix it to look like this: It's _Buono, il brutto, il cattivo, Il (1966)_ meets Simple Men (1992). George is a small-time crook. His brother Michael makes a living scamming the elderly by selling them nonexistent burial plots. When their father steals half a million dollars, the brothers find themselves on the run from the police and a local crime boss. But when Dad is killed, George and Michael have to team up to find the hidden loot.\", 'title': 'Hand Gun', 'score': 0.6962246894836426}]. The Search Results: 10 results. First result: 10 results. First result has 10 entries. The first result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The second result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The third result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The fourth result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The fifth result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The sixth result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The seventh result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The eighth result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The ninth result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "The tenth result is a list of 10 search results, each being a dictionary with 'genres', 'fullplot', 'title', and 'score' keys.\n", "\n", "So, the user is looking for some interesting action movies that include business. They probably want to watch something that's both thrilling and has a business aspect, maybe something that combines both elements.\n", "\n", "Looking at the genres, the user wants to include business. So, the genres that involve business would be Action, Business, Crime, etc. Looking at the genres in the results, Action is present in several, and Business is also present in a few.\n", "\n", "So, the user probably wants to watch an action movie that also deals with business-related topics, maybe business-related plot points, or business-related characters.\n", "\n", "Looking at the titles, some of them are \"Shanghai Express\", \"Grindhouse\", \"Crime Story\", \"The Accidental Spy\", \"Hand Gun\". None of these are business-related, so perhaps the user wants to watch an action movie with a business twist.\n", "\n", "Wait, maybe the user is looking for an action movie that also has a business component, like a business-related plot or character. So, perhaps the genres should include Action, Business, and another category.\n", "\n", "Looking at the results, \"The Good, the Bad and the Ugly\" is a crime thriller, and \"Fix it\" is a crime thriller as well. So, perhaps the user is looking for a crime thriller that has a business twist.\n", "\n", "Looking at \"The Good, the Bad and the Ugly\" and \"Fix it\", both are crime thrillers. \"The Good, the Bad and the Ugly\" is 1966, \"Fix it\" is 1992. Both are crime thrillers, but \"Fix it\" is a bit more of a crime thriller with a business element. The title is \"Fix it, il brutto, il cattivo, Il (1966)\", which seems to be a crime thriller with a mix of business and crime elements.\n", "\n", "Alternatively, looking at \"Hand Gun\", which is a crime thriller, but it's a bit of a different story.\n", "\n", "Looking back at the movies:\n", "\n", "- \"The Good, the Bad and the Ugly\": A crime thriller where the main character is a crook, and the brothers are trying to fix something, maybe a business issue. The title is \"Fix it, il brutto, il cattivo, Il (1966)\".\n", "\n", "- \"Hand Gun\": Another crime thriller, but it's more of a psychological thriller. The title is \"Hand Gun\", which is 1966.\n", "\n", "So, \"The Good, the Bad and the Ugly\" seems like a better fit because it's a crime thriller with a business element, perhaps in the sense that the main character is involved in a business-related plot.\n", "\n", "Alternatively, maybe \"The Accidental Spy\" is an action movie with a business twist. Let me check its genre. \"The Accidental Spy\" is Action, Comedy, Thriller. So, it's more of an action-comedy-thriller.\n", "\n", "But the user is looking for an action movie that includes business. So, perhaps \"The Good, the Bad and the Ugly\" is a better fit\n"]}], "source": ["print(\n", "    rag_query(\n", "        \"What's a romantic movie that I can watch with my wife? Make your response concise\"\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KBopOAHKA2uf"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": ["pw-5v8H_rXOV", "4apT5j46rsfO", "20L_L9Q5tXPu", "KU0KJC5etwhN", "nwMXXMZtt_CJ"], "gpuType": "T4", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0c77052a694f45a49a88e6ac190ce45e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d0f70ea26984ad0b341292a89ca1ae9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f77f228befc04fd286740c8e4008b0e3", "placeholder": "​", "style": "IPY_MODEL_a648eaa1a4f64a6997f085c7834eeaf7", "value": "tokenizer_config.json: 100%"}}, "0d8a7b4909114d269acf1df8c14c7000": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d991641826e4451a5ebdb4329f3cbe3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0f8c1a06cb9743bc8ac77616cd82fa9c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "11c88d53c1d4498dafd3b255cb8ee653": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "123a170f4f514bc58ed44b4a21015538": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1822d15a82044cc690b9ac8e27901c26": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_756df8b5de2c443e8e5db0b16fbd4a1f", "placeholder": "​", "style": "IPY_MODEL_69894e27dab8404dbdc8d9ed2d82ac94", "value": "tokenizer.json: 100%"}}, "1a299210afc24c06ba25dfee47e60f3d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1c447ae3914e4ef38c4a1d368595eb60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_52178876a7864fd4beb9bee548766c24", "placeholder": "​", "style": "IPY_MODEL_dbcc51dcc3064c8d8269d3b4031d5a3c", "value": " 679/679 [00:00&lt;00:00, 59.9kB/s]"}}, "1d8f25114ad741ca85745d066b7d2495": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "214fc2ffd406429a9103d1e27b3985cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "222e9dc70dea4916adca90fbdb86ae00": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5eccc99196c94c52af0757d28c09090e", "placeholder": "​", "style": "IPY_MODEL_34d42ac742a2473bb7c52a7557a36e39", "value": " 3.55G/3.55G [01:24&lt;00:00, 42.7MB/s]"}}, "26a980725fdb487f8e3d0415cbf607d7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2778883ff60e43798fcf7515007dd890": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b2d4a4d6af984fc28c7a4e9a67b7a5d0", "max": 3554214621, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b895f5fc35c3446e96e2a2637fb89127", "value": 3554214621}}, "2a996a97b6a74fc3b462e2373c901692": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_471bd7b9f0cc4a41aad91bd2a0fa0b74", "max": 6172, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f4c8b7e7c4eb499f94de9c611e90b663", "value": 6172}}, "2c3a0dd6a1eb438dbcd46297053c1617": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0d0f70ea26984ad0b341292a89ca1ae9", "IPY_MODEL_957f5f1c63c3466e8731a2ef1a8b0964", "IPY_MODEL_50bac768ece44a02aaaf1e1df81cd340"], "layout": "IPY_MODEL_685344b8731f4614b104daeecb506349"}}, "2e135d23dfda40b5b1a0b09ec9e63b31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2fab42c58ad845c5b66f0262e22fa8d4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "32a6161ea2ea4957829729afa1720584": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_599f54303eb74b05aa806c3228a956a7", "IPY_MODEL_2a996a97b6a74fc3b462e2373c901692", "IPY_MODEL_6878311b22b94ae79c4d1700a0c1410e"], "layout": "IPY_MODEL_a3b2a5214d4d47dcace266a15ee6568d"}}, "33a065062f134c0d975262bc6c7e5727": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_491c140d231842b79a854a0311311990", "IPY_MODEL_2778883ff60e43798fcf7515007dd890", "IPY_MODEL_222e9dc70dea4916adca90fbdb86ae00"], "layout": "IPY_MODEL_68e099a39f4e4abcb2f9e8fdc0884f11"}}, "34d42ac742a2473bb7c52a7557a36e39": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "36a6233658a54bec98670da58c6a1695": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f0c1200a87f74ea88d3ce36ecfc99014", "max": 181, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_214fc2ffd406429a9103d1e27b3985cb", "value": 181}}, "3769dbbf521843aea1ce4f367484c080": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d9089aeb40654caeb517ad2f1dc0e733", "max": 42271667, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5bb0697d0ffb4a0aba9b3bd9b08d3146", "value": 42271667}}, "404bdac883a948a89b40520a27ca487e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "409308c8506e45b2a00ee2dcc74152f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "430b3c93637a4aeb908f82c4812d2897": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6918f348708943b6b4220c7a2fdd178a", "placeholder": "​", "style": "IPY_MODEL_c76b90bab1f242e8b3402425aeefc0d3", "value": " 42.3M/42.3M [00:01&lt;00:00, 42.7MB/s]"}}, "471bd7b9f0cc4a41aad91bd2a0fa0b74": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "491c140d231842b79a854a0311311990": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2fab42c58ad845c5b66f0262e22fa8d4", "placeholder": "​", "style": "IPY_MODEL_ed113bf3772c41e0a72891e524d7bc6f", "value": "model.safetensors: 100%"}}, "50bac768ece44a02aaaf1e1df81cd340": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0f8c1a06cb9743bc8ac77616cd82fa9c", "placeholder": "​", "style": "IPY_MODEL_c5c7ee3f1f2340c380e7050c41926f5d", "value": " 3.06k/3.06k [00:00&lt;00:00, 291kB/s]"}}, "52178876a7864fd4beb9bee548766c24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "529faa51648a4bdc967049ab75c67955": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "599f54303eb74b05aa806c3228a956a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cea0e1aa862e4fd7b2c790cef07d175f", "placeholder": "​", "style": "IPY_MODEL_11c88d53c1d4498dafd3b255cb8ee653", "value": "README.md: 100%"}}, "5bb0697d0ffb4a0aba9b3bd9b08d3146": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5c0bac7a6fef40189972fc6cea95fa29": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5eccc99196c94c52af0757d28c09090e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5fed7972422e49d992b564afa6bd4e44": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8e543fd471c74ba2a386c25d79f530d0", "IPY_MODEL_36a6233658a54bec98670da58c6a1695", "IPY_MODEL_c85e9694cdee437892bdfbdffdbf3788"], "layout": "IPY_MODEL_409308c8506e45b2a00ee2dcc74152f1"}}, "685344b8731f4614b104daeecb506349": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6878311b22b94ae79c4d1700a0c1410e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dc965db378fe4995bc92d4606541ecc5", "placeholder": "​", "style": "IPY_MODEL_ad9e4414b262401ba1cc088be291de8b", "value": " 6.17k/6.17k [00:00&lt;00:00, 486kB/s]"}}, "68e099a39f4e4abcb2f9e8fdc0884f11": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6918f348708943b6b4220c7a2fdd178a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "69894e27dab8404dbdc8d9ed2d82ac94": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6b973cb0594a432cba4ea6f6adea5a19": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6c1bd92a1aad4ef6aa47c94c6e3f0898": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "708de4722f1b41cda3d321888a3cbfb7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7137f86efff146cea416da5c637b55ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a93bbc80b5f24583b72a710c7e946094", "IPY_MODEL_3769dbbf521843aea1ce4f367484c080", "IPY_MODEL_430b3c93637a4aeb908f82c4812d2897"], "layout": "IPY_MODEL_bd30390bc99d42bbac96c3c9f8a2858a"}}, "756df8b5de2c443e8e5db0b16fbd4a1f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7bff5ad84946419c9ba73cf860534d60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0d8a7b4909114d269acf1df8c14c7000", "max": 7031660, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_eb9515b9e492476e97f1ac02bc5ded8c", "value": 7031660}}, "7e975d28657041058355c18716c5fa48": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d65b9c8d3cbe48f49b764d4cc85f852f", "IPY_MODEL_9bf0a927a8dd4c82bfef900f29e69ce6", "IPY_MODEL_c45f7981bfc64061a113bffddbddce9e"], "layout": "IPY_MODEL_c13377e46cf146e6a62102cd51710d9f"}}, "8c43cc01031b4da88ab2a12f16c9aff0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_93aad82624944342a250a980bd39e64d", "IPY_MODEL_e578633949794b72b497fa20ad3e1ec4", "IPY_MODEL_1c447ae3914e4ef38c4a1d368595eb60"], "layout": "IPY_MODEL_f62f85e997be42a09e9fcb1dfd6084b5"}}, "8ca9c3dd6b2c42b4abee90b9a3a7b9b2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8e543fd471c74ba2a386c25d79f530d0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0c77052a694f45a49a88e6ac190ce45e", "placeholder": "​", "style": "IPY_MODEL_404bdac883a948a89b40520a27ca487e", "value": "generation_config.json: 100%"}}, "93aad82624944342a250a980bd39e64d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_529faa51648a4bdc967049ab75c67955", "placeholder": "​", "style": "IPY_MODEL_2e135d23dfda40b5b1a0b09ec9e63b31", "value": "config.json: 100%"}}, "957f5f1c63c3466e8731a2ef1a8b0964": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26a980725fdb487f8e3d0415cbf607d7", "max": 3061, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0d991641826e4451a5ebdb4329f3cbe3", "value": 3061}}, "9bf0a927a8dd4c82bfef900f29e69ce6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6c1bd92a1aad4ef6aa47c94c6e3f0898", "max": 1500, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_708de4722f1b41cda3d321888a3cbfb7", "value": 1500}}, "a02309a17f604f17a23521bc0f5cdd35": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3b2a5214d4d47dcace266a15ee6568d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a648eaa1a4f64a6997f085c7834eeaf7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a93bbc80b5f24583b72a710c7e946094": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c94a1b82bcb946378b0c8d7c5fd1388f", "placeholder": "​", "style": "IPY_MODEL_8ca9c3dd6b2c42b4abee90b9a3a7b9b2", "value": "sample_mflix.embedded_movies.json: 100%"}}, "ad9e4414b262401ba1cc088be291de8b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b2d4a4d6af984fc28c7a4e9a67b7a5d0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b6456756effd4047b1fedfdbd4bbf539": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a02309a17f604f17a23521bc0f5cdd35", "placeholder": "​", "style": "IPY_MODEL_ebc9c48a0c054ed780ef5384c63544e4", "value": " 7.03M/7.03M [00:01&lt;00:00, 6.71MB/s]"}}, "b895f5fc35c3446e96e2a2637fb89127": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bd30390bc99d42bbac96c3c9f8a2858a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf912b2aad0d4b0e9fbd15b82e7577a2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1822d15a82044cc690b9ac8e27901c26", "IPY_MODEL_7bff5ad84946419c9ba73cf860534d60", "IPY_MODEL_b6456756effd4047b1fedfdbd4bbf539"], "layout": "IPY_MODEL_6b973cb0594a432cba4ea6f6adea5a19"}}, "c13377e46cf146e6a62102cd51710d9f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c4144e1058e04ee5a87e40cfb7795441": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c45f7981bfc64061a113bffddbddce9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_caec76179d6b467f80ae27ad7ae541e2", "placeholder": "​", "style": "IPY_MODEL_1a299210afc24c06ba25dfee47e60f3d", "value": " 1500/1500 [00:00&lt;00:00, 2441.11 examples/s]"}}, "c5c7ee3f1f2340c380e7050c41926f5d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c76b90bab1f242e8b3402425aeefc0d3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c85e9694cdee437892bdfbdffdbf3788": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1d8f25114ad741ca85745d066b7d2495", "placeholder": "​", "style": "IPY_MODEL_d77491778334421684b67d1db2ea29f9", "value": " 181/181 [00:00&lt;00:00, 16.5kB/s]"}}, "c94a1b82bcb946378b0c8d7c5fd1388f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "caec76179d6b467f80ae27ad7ae541e2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cea0e1aa862e4fd7b2c790cef07d175f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d65b9c8d3cbe48f49b764d4cc85f852f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c4144e1058e04ee5a87e40cfb7795441", "placeholder": "​", "style": "IPY_MODEL_123a170f4f514bc58ed44b4a21015538", "value": "Generating train split: 100%"}}, "d77491778334421684b67d1db2ea29f9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d9089aeb40654caeb517ad2f1dc0e733": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dbcc51dcc3064c8d8269d3b4031d5a3c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dc965db378fe4995bc92d4606541ecc5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e533b9b0a52447a1921c2c6a2a831da2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e578633949794b72b497fa20ad3e1ec4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e533b9b0a52447a1921c2c6a2a831da2", "max": 679, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5c0bac7a6fef40189972fc6cea95fa29", "value": 679}}, "eb9515b9e492476e97f1ac02bc5ded8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ebc9c48a0c054ed780ef5384c63544e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ed113bf3772c41e0a72891e524d7bc6f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f0c1200a87f74ea88d3ce36ecfc99014": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f4c8b7e7c4eb499f94de9c611e90b663": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f62f85e997be42a09e9fcb1dfd6084b5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f77f228befc04fd286740c8e4008b0e3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}