{"cells": [{"cell_type": "markdown", "metadata": {"id": "OIiUjWlTSva2"}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/ml/tensorflow_mongodbcharts_horoscopes.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/mongodb/tensorflow-mongodb-charts/)\n", "\n", "# Sentiment Analysis on my scraped horoscopes"]}, {"cell_type": "markdown", "metadata": {"id": "BQDHC--ZL5jN"}, "source": ["Our first step is to do sentiment analysis on our .csv file of scraped horoscopes. Luckily for us, at this point in the tutorial we don't need to build a model (yet!), we can use a pre-trained model to figure out whether or not our horoscopes from the past six months are positive or negative."]}, {"cell_type": "markdown", "metadata": {"id": "TzJTpO5Pe0Jc"}, "source": ["I am using this tutorial here from [Medium](https://medium.com/@sharma.tanish096/sentiment-analysis-using-pre-trained-models-and-transformer-28e9b9486641), please feel free to take a look at it to better understand the code used below."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2k-bMW3kL5VV"}, "outputs": [], "source": ["import numpy as np\n", "import tensorflow as tf\n", "from scipy.special import softmax\n", "from transformers import AutoConfig, AutoTokenizer, TFAutoModelForSequenceClassification"]}, {"cell_type": "markdown", "metadata": {"id": "mLxUwBGINH_4"}, "source": ["Once everything is imported in, let's choose which pre-trained model we want to use. Since this is a TensorFlow tutorial, we can go ahead and use the [\"distilbert-base-uncased-finetuned-sst-2-english\"](https://huggingface.co/distilbert/distilbert-base-uncased-finetuned-sst-2-english) model since it's compatible with TensorFlow, but there are a ton of options out there to choose from if you would like to switch it up."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 356, "referenced_widgets": ["a5dcdc712f674ba9aa3ea6d7bdb3d97d", "b1a4590564b34786b15f6e3aedf076a0", "8cfb979551d74482a06eb63f6d5c7a29", "3ac89cbe689f4699a0aa9dc9edab85de", "84f42f0541df44b48bf1546086e74c0a", "b23e522db72344cfa4a2ebdda2e2e2ef", "ebcb407f178642c79b8f0c805ee7dabb", "6f755b7843274dfc9209efcc5ed7d29d", "f8e90c44c62c40b4b09b51c5fa34a59b", "3b33ad4c130444ffbe7a5b23d76384f5", "bbc815ce1bb34c569d24be6734a64891", "cc4ab91bf4494416b7b1f4da183e62f7", "106986cf19c340c28e07636ab69d4dc4", "b79ed4c354704e508ccdf7c3c9ccb418", "4f624dbdf918425c9545d1e1f3b9fbd3", "27f842a5d3a34174a960ef7a7b025665", "6e2a096fe51a49dda08e2ad6d7456c0b", "dfbf5321bb4a49fbb046cc0011ebd508", "a036e88a5e2c4960ba5df9ccd6726556", "60080f8c348c4813a3d1c0b8d3599361", "fc6c8a098ac54feb98c9b24a95ab88a1", "07df2dd843db4a5eb14cbf55eee02d65", "5bb06a85c9684aa8aea37bee508d4aa4", "7953c87952fc46c4a67660e9343972a2", "2b4e633979de49368460b09486847eb5", "ae052cb87ce84cf486accafdf0e9d701", "5b6727464c6640a08bb4fcb0efd53571", "546387dcdbdc408dae7e728d5c123608", "f43e3aaf188a45aaa67d07ff55fd1d42", "bce274686e084ab79f75679e9574729a", "2faa230a9412434d8be564fcac05e9a4", "0a262fc5c2304b299f5a8ae14b8e7ace", "8dfe9277291e4941b078b642b5fefc22", "fd752c9c46864fa8ac691e98f2ef4a94", "457cfed772634defbdb74af748e77f9b", "1fb6b73395194edf80e0c32478068dc4", "9e9f251b8fdb45aab48df63b77539adb", "4b0e77d8ce3b4be987c52fed07c765a0", "59a0115b0cbf49b0bd43936292d29678", "9b9344aa7dbd4a02ba2cf757cdbc963f", "066597d2e3b545c4ad832adea9a1ea70", "5474edeea875477e84c87febb0e2c98e", "8321d09f32d649f5b117289fdfe36b06", "07403199e5e5427f8243f7d38bce962c"]}, "id": "-d8xiD_dR3zS", "outputId": "f79e3739-0a7a-4e85-e400-dcc905bb6c43"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:89: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a5dcdc712f674ba9aa3ea6d7bdb3d97d", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/48.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cc4ab91bf4494416b7b1f4da183e62f7", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/629 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5bb06a85c9684aa8aea37bee508d4aa4", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fd752c9c46864fa8ac691e98f2ef4a94", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/268M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["All PyTorch model weights were used when initializing TFDistilBertForSequenceClassification.\n", "\n", "All the weights of TFDistilBertForSequenceClassification were initialized from the PyTorch model.\n", "If your task is similar to the task the model of the checkpoint was trained on, you can already use TFDistilBertForSequenceClassification for predictions without further training.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Horoscope is NEGATIVE\n"]}], "source": ["# distilbert model we are using\n", "distilbert = \"distilbert-base-uncased-finetuned-sst-2-english\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(distilbert)\n", "config = AutoConfig.from_pretrained(distilbert)\n", "model = TFAutoModelForSequenceClassification.from_pretrained(distilbert)\n", "\n", "\n", "def sentiment_finder(horoscope):\n", "    input = tokenizer(\n", "        horoscope, padding=True, truncation=True, max_length=512, return_tensors=\"tf\"\n", "    )\n", "    output = model(input)\n", "    scores = output.logits[0].numpy()\n", "    scores = softmax(scores)\n", "    ranking = np.a<PERSON><PERSON>t(scores)\n", "    ranking = ranking[::-1]\n", "    return config.id2label[ranking[0]]\n", "\n", "\n", "# test and see if works before we try on our csv file\n", "horoscope = \"Things might get a bit confusing for you today, <PERSON><PERSON><PERSON>. Don't feel like you need to make sense of it all. In fact, this task may be impossible. Just be yourself. Let your creative nature shine through. Other people are quite malleable, and you should feel free to take the lead in just about any situation. Make sure, however, that you consider other people's needs.\"\n", "sentiment = sentiment_finder(horoscope)\n", "print(f\"Horoscope is {sentiment}\")"]}, {"cell_type": "markdown", "metadata": {"id": "zfgzA07bUNU6"}, "source": ["This is great! As we can see, that Capricorn horoscope is in fact negative, and we were able to use our pre-trained model to classify it. But, now we need to make some changes because we don't want to put in everything manually, we want to use this pre-trained model and put in our .csv file of all our horoscopes and figure out the sentiment analysis of everything in our file, while also incorporating in a new \"sentiment\" column that will include 1's if the horoscope is positive and 0 if the horoscope is negative."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JFgV74F-Uh9A"}, "outputs": [], "source": ["# function to apply sentiment against each horoscope\n", "def apply_sentiment(horoscope):\n", "    sentiment = sentiment_finder(horoscope)\n", "    return 1 if sentiment == \"POSITIVE\" else 0"]}, {"cell_type": "markdown", "metadata": {"id": "OIerwYG4W7o5"}, "source": ["Now load our \"anaiya-six-months-horoscopes.csv\" file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EVatE2YjXGrc", "outputId": "451393ed-6e40-430c-d415-861697acd47d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (2.1.4)\n", "Requirement already satisfied: numpy<2,>=1.22.4 in /usr/local/lib/python3.10/dist-packages (from pandas) (1.26.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas) (2024.1)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas) (2024.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas) (1.16.0)\n"]}], "source": ["# import pandas\n", "!pip install pandas\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "uTDPCXmjW-9G"}, "outputs": [], "source": ["df = pd.read_csv(\"anaiya-six-months-horoscopes.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PIZKPCpsXSQm"}, "outputs": [], "source": ["# we want to apply each sentiment to our horoscopes with a new column to hold them\n", "df[\"sentiment\"] = df[\"horoscope\"].apply(apply_sentiment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BWTSiiRAXw-r", "outputId": "f9325a96-cafb-4c65-e9fb-361ab42af2a0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["saved to new file called anaiya-six-months-horoscopes-sentiment.csv\n"]}], "source": ["# save our new dataframe to new csv file\n", "df.to_csv(\"anaiya-six-months-horoscopes-sentiment.csv\")\n", "\n", "print(\"saved to new file called anaiya-six-months-horoscopes-sentiment.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "tOQsIjl9a94V", "outputId": "1fd575e4-1cf1-44f6-ff59-ddcc93a6d05d"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df\",\n  \"rows\": 2196,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 173,\n        \"min\": 20240128,\n        \"max\": 20240728,\n        \"num_unique_values\": 183,\n        \"samples\": [\n          20240216,\n          20240310,\n          20240701\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"zodiac\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"Aquarius\",\n          \"Capricorn\",\n          \"Aries\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"horoscope\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 2196,\n        \"samples\": [\n          \"Mar 3, 2024 - Unexpected communications from people far away could open up new opportunities regarding career, education, travel, or other broadening experiences. As a result, <PERSON><PERSON><PERSON>, new friendships could develop, as well as new ideas and possibly exciting new goals. Modern technology could play a major role in this development. A trip by air could be on the horizon, probably to a place you've never been before. Don't be too overwhelmed by it all!\",\n          \"Jun 23, 2024 - Business and financial successes have you feeling happy and satisfied, <PERSON><PERSON><PERSON>. You're also looking forward to moving ahead. The downside of this period is that people who aren't particularly trustworthy could decide to grab your coattails for their own purposes. Some might even ask for loans. Be careful about the ones you choose to assist. They might not be honest with you. Don't fall for any sob stories.\",\n          \"Feb 27, 2024 - Today the energy is on partnerships, Scorpio, probably those begun on the spur of the moment. You and a colleague might have a conversation about the possibility of going into business together. There is also the possibility that you could fall in love at first sight, maybe with someone from far away. Any sort of partnership formed today will have its ups and downs, but with work, it could succeed. If it feels right, go for it!\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"sentiment\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df"}, "text/html": ["\n", "  <div id=\"df-ece3d8a6-1a4a-4d3b-88c3-f1288d535607\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>zodiac</th>\n", "      <th>horoscope</th>\n", "      <th>sentiment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240128</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Jan 28, 2024 - Drastic shifts in your emotions...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20240128</td>\n", "      <td>Taurus</td>\n", "      <td>Jan 28, 2024 - Today is one of those days in w...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20240128</td>\n", "      <td>Gemini</td>\n", "      <td>Jan 28, 2024 - You're most likely going to be ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20240128</td>\n", "      <td>Cancer</td>\n", "      <td>Jan 28, 2024 - You may find yourself staring a...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20240128</td>\n", "      <td>Leo</td>\n", "      <td>Jan 28, 2024 - If you find yourself needing to...</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ece3d8a6-1a4a-4d3b-88c3-f1288d535607')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-ece3d8a6-1a4a-4d3b-88c3-f1288d535607 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-ece3d8a6-1a4a-4d3b-88c3-f1288d535607');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-16d1da61-1a8d-469d-99f7-1f140886faa4\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-16d1da61-1a8d-469d-99f7-1f140886faa4')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-16d1da61-1a8d-469d-99f7-1f140886faa4 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["       date  zodiac                                          horoscope  \\\n", "0  20240128   <PERSON><PERSON>  Jan 28, 2024 - Drastic shifts in your emotions...   \n", "1  20240128  Taurus  Jan 28, 2024 - Today is one of those days in w...   \n", "2  20240128  Gemini  Jan 28, 2024 - You're most likely going to be ...   \n", "3  20240128  Cancer  Jan 28, 2024 - You may find yourself staring a...   \n", "4  20240128     <PERSON> 28, 2024 - If you find yourself needing to...   \n", "\n", "   sentiment  \n", "0          1  \n", "1          1  \n", "2          1  \n", "3          0  \n", "4          1  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "awYnnrlat4Au"}, "source": ["## Save our new `.csv` file into MongoDB Atlas so we can visualize our data in MongoDB Charts\n"]}, {"cell_type": "markdown", "metadata": {"id": "pWQSkA5Cvff7"}, "source": ["This part is done using MongoDB Compass and MongoDB Charts."]}, {"cell_type": "markdown", "metadata": {"id": "kS52h0tGkwdS"}, "source": ["# TRAIN AND TEST M<PERSON><PERSON> WITH TENSORFLOW"]}, {"cell_type": "markdown", "metadata": {"id": "sdCxBNz1kzww"}, "source": ["Now that we have our dataset ready with our sentiment analysis done using our pre-trained model, we can go ahead and set up a way to train and test our data so that if we wanted to incorporate new horoscopes, we can see if they will be negative or positive.\n", "\n", "In order to help me learn how to do this, I watched this video from freeCodeCamp.org: https://www.youtube.com/watch?v=VtRLrQ3Ev-U, and I used the skeleton code from this TensorFlow docs: https://www.tensorflow.org/tutorials/structured_data/preprocessing_layers\n", "\n", "Feel free to watch it to get a better understanding of the code used below."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "weLzUFaPlEYV"}, "outputs": [], "source": ["# let's first download tensorflow_hub since we need it\n", "import tensorflow_hub as hub"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6ypXuEaPnxfa"}, "outputs": [], "source": ["# now load in our new .csv file with our sentiment analysis\n", "# but only keep the columns we want, which are \"horoscope\" and \"sentiment\"\n", "\n", "df = pd.read_csv(\"anaiya-six-months-horoscopes-sentiment.csv\")\n", "df = df[[\"horoscope\", \"sentiment\"]]"]}, {"cell_type": "markdown", "metadata": {"id": "hwphbaVEoDJ3"}, "source": ["We want to split up our dataset into three sets. We need a training set, a validation set, and a test set."]}, {"cell_type": "markdown", "metadata": {"id": "dBacubNQCLNU"}, "source": ["# BALANCE DATASET\n", "We need to balance our dataset since we need to make sure our model is trained on the same exact amount of negative and positive horoscopes, otherwise things will be swayed in one direction or the other. Check out this article for help on how to balance your dataset: https://medium.com/@daniele.santiago/balancing-imbalanced-data-undersampling-and-oversampling-techniques-in-python-7c5378282290 and https://semaphoreci.com/blog/imbalanced-data-machine-learning-python"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "8rCAwJJ6oKxi"}, "outputs": [], "source": ["# shuffle\n", "df = df.sample(frac=1, random_state=42)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "YViEqQyqEYBN"}, "outputs": [], "source": ["# first split variables into x and y\n", "X = df[\"horoscope\"]\n", "y = df[\"sentiment\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gVWtJy-AEeRa"}, "outputs": [], "source": ["from imblearn.under_sampling import RandomUnderSampler\n", "from sklearn.model_selection import train_test_split\n", "\n", "# now do for training and testing\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, stratify=y, shuffle=True, test_size=0.15, random_state=42\n", ")\n", "\n", "# create our RandomUnderSampler object\n", "rus = RandomUnderSampler(random_state=42, sampling_strategy=\"majority\")\n", "\n", "# apply our RUS technique\n", "X_resampled, y_resampled = rus.fit_resample(X_train.to_frame(), y_train)\n", "\n", "# now convert it back to our dataframe\n", "balanced_trained = pd.DataFrame(\n", "    {\"horoscope\": X_resampled[\"horoscope\"], \"sentiment\": y_resampled}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fe2ZAv2QGbyG", "outputId": "f20a32a7-978c-44b5-ab06-41354b6e358b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sentiment\n", "0    495\n", "1    495\n", "Name: count, dtype: int64\n"]}], "source": ["# print it out and make sure you have the exact same number of negative and positive\n", "# horoscopes for our data.\n", "sentiment_amount_training = balanced_trained[\"sentiment\"].value_counts()\n", "print(sentiment_amount_training)"]}, {"cell_type": "markdown", "metadata": {"id": "V1zXG7gclUbe"}, "source": ["# SPLIT UP OUR DATASET"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_Oy7itfBpVrZ"}, "outputs": [], "source": ["# split our balanced dataset into our training and validation\n", "train, val = train_test_split(\n", "    balanced_trained,\n", "    test_size=0.2,\n", "    stratify=balanced_trained[\"sentiment\"],\n", "    random_state=42,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LmnmjcWNpoLb", "outputId": "c62c87b6-b03c-4e4f-9068-1a7dc61715f6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training set: 792\n", "Validation set: 198\n", "Test set: 330\n"]}], "source": ["# view the sizes of each set\n", "print(\"Training set:\", len(train))\n", "print(\"Validation set:\", len(val))\n", "print(\"Test set:\", len(X_test))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cmWiIERGbj9c"}, "outputs": [], "source": ["# combine back our X_test and y_test to a df\n", "test = pd.DataFrame({\"horoscope\": X_test, \"sentiment\": y_test})"]}, {"cell_type": "markdown", "metadata": {"id": "PSnwn6jjlX1x"}, "source": ["# CONVERT TO TENSORFLOW DATASET"]}, {"cell_type": "markdown", "metadata": {"id": "OsEB7a6cquto"}, "source": ["Now, let's convert our dataframes to TensorFlow datasets. Use this code from the documentation: https://www.tensorflow.org/tutorials/structured_data/preprocessing_layers\n", "\n", "it converts each train, validation, and test dataset into a tensorflow dataset and will shuffle again and batch the data for you."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "USIrbdtAq-Pa"}, "outputs": [], "source": ["# this is the code from the tutorial\n", "def df_to_dataset(dataframe, shuffle=True, batch_size=32):\n", "    df = dataframe.copy()\n", "    labels = df.pop(\"target\")\n", "    df = {key: value.values[:, tf.newaxis] for key, value in dataframe.items()}\n", "    ds = tf.data.Dataset.from_tensor_slices((dict(df), labels))\n", "    if shuffle:\n", "        ds = ds.shuffle(buffer_size=len(dataframe))\n", "    ds = ds.batch(batch_size)\n", "    ds = ds.prefetch(batch_size)\n", "    return ds"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AU6SCf-irDAQ"}, "outputs": [], "source": ["# code changed to meet my specific needs\n", "def df_to_dataset(dataframe, shuffle=True, batch_size=32):\n", "    df = dataframe.copy()\n", "    labels = df.pop(\"sentiment\")\n", "    df = df[\"horoscope\"]\n", "    ds = tf.data.Dataset.from_tensor_slices((df, labels))\n", "    if shuffle:\n", "        ds = ds.shuffle(buffer_size=len(dataframe))\n", "    ds = ds.batch(batch_size)\n", "    ds = ds.prefetch(tf.data.AUTOTUNE)\n", "    return ds"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "f9dWMbNVr2Vg"}, "outputs": [], "source": ["train_data = df_to_dataset(train)\n", "val_data = df_to_dataset(val)\n", "test_data = df_to_dataset(test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "_0mlrtcBsDnv", "outputId": "ef3a9e6b-9709-4f9d-e49b-c26cf438cd8d"}, "outputs": [{"data": {"text/plain": ["(<tf.Tensor: shape=(32,), dtype=string, numpy=\n", " array([b'Feb 13, 2024 - Machines involved in financial transactions, such as ATMs, phone systems, or banking websites could malfunction today, <PERSON>, so you might have to resort to dealing with money in the old fashioned way: by going into the bank or writing checks. Electrical storms or solar flares could be interfering with satellite signals, so there isn\\xe2\\x80\\x99t much you can do. Needless to say, this isn\\xe2\\x80\\x99t a good day to make any major financial transactions.',\n", "        b\"Feb 26, 2024 - There could be a missing person very much on your mind these days. Is it possible that the relationship is over and you're the last one to know? Don't let your insecurities get the better of you, <PERSON><PERSON><PERSON><PERSON>. It's likely that your friend merely needs some time alone to sort out some big life issues. He or she will seek out your warmth and friendship again soon.\",\n", "        b'Jun 5, 2024 - It\\xe2\\x80\\x99s time to get in touch with the people in your neighborhood, <PERSON><PERSON>. By reaching out to the people you live near in a jovial and congenial way you build community spirit. You can expect a lot of stimulating conversation once you take the initiative. Valuable business contacts could be made today, although you might not be aware of it at the time.',\n", "        b\"Feb 1, 2024 - Today is a great day for transformation, <PERSON><PERSON>, and your actions have deeper than usual inner meaning. Be careful where you aim your arrow, because the tip of it has extra sting today. People might be rubbing you the wrong way, making you feel like you\\xe2\\x80\\x99re a pot of water on a hot plate. The heat is turned up and the cover is about to blow. Make sure you don't hit anyone when the lid flies off.\",\n", "        b\"Jul 19, 2024 - Your insight, creativity, and inventiveness are in full swing, Cancer, and new ideas come thick and fast. However, you might find yourself too caught up in boring, mundane tasks to do very much about it. Yet physical energy is plentiful, so you have it in you to take care of chores and then have time to do what you enjoy. You'll produce results if you remember to pace yourself. Work too hard and you'll be too tired!\",\n", "        b\"May 31, 2024 - Today you're likely focused on sex, romance, and committed relationships. The planetary energies surrounding love are promising, <PERSON><PERSON>. If you're in a relationship, you and your partner could make plans to embark on a new enterprise together, which is likely to succeed. If you're involved but not committed, expect the relationship to move forward now. If you aren't with someone, who knows? By the end of the day you might be.\",\n", "        b'Feb 25, 2024 - Certainly there are advantages and disadvantages to the high level of intuition you have. Today you may feel weighed down by some strange thoughts that enter your head. Only time will tell if they are true premonitions or simply weird daydreams. In the meantime, you\\xe2\\x80\\x99d probably be best served by ignoring them completely. Life is too short to play the \"what if\" game.',\n", "        b\"Apr 8, 2024 - A colleague could be in a very bad mood, <PERSON><PERSON>, and therefore not the easiest person in the world to deal with. In fact, today he or she could resist working at all, and you might feel obligated to take up the slack. Only do it if the tasks are urgent. It isn't fair to you to have to do someone else's job. Don't feel guilty if you leave it unfinished.\",\n", "        b\"Apr 28, 2024 - A friend might be in such a dour mood that you wonder if this is the same person you know and love, <PERSON>. During the day you might be tempted to try to analyze what's going on, but this is apt to raise more questions than answers. Probably this is a case of too many problems hitting your friend at once. Don't pry, but make sure your friend knows you're there if needed.\",\n", "        b\"Jun 1, 2024 - You're normally intuitive, <PERSON>, but today you might feel more psychic than you ever dreamed you could. Information received from the media could have you picking up psychically on the thoughts and feelings of people you've never met. This can be emotionally overpowering, so protect yourself by keeping the white light around you. Channel your thoughts and feelings into art of some kind. It helps.\",\n", "        b\"Mar 2, 2024 - Today you might feel stifled by your current situation. Perhaps you're thinking of changing jobs or professions, or maybe you're considering moving to a more exotic place. These all might be good ideas, <PERSON>, but today isn't the day to make any definite decisions or even consider your options. You aren't in an objective frame of mind. Wait a few days and then think about this some more. You might change your mind.\",\n", "        b\"Mar 3, 2024 - Today should find you continuing to work toward cherished career and other goals. Love matters may come to the forefront, <PERSON><PERSON><PERSON>. If you're currently involved, unexpected events could bring you that much closer to your partner. If you aren't involved, you could meet someone exciting, perhaps through some kind of group activities. Also, a longtime friend could suddenly appear to you in a new light. Whichever it is, expect some interesting developments.\",\n", "        b'Jan 31, 2024 - You may be anxious to fit things in your life into pigeonholes, <PERSON>. When it comes to matters of love and romance, this may be hard to do, especially on a day like today. Call upon your pioneering spirit to seek something new and not limit yourself to what you originally had in mind. Perhaps there is something bigger and better waiting for you. You\\xe2\\x80\\x99ll only find it if you dare to accept something outside the norm.',\n", "        b\"Jul 18, 2024 - You could be feeling the pressure of time now, <PERSON><PERSON>. At this point in your life you may be more aware than ever of your limited time left on this planet. Perhaps you're thinking more about love and romance and how much they mean to you. Consider the limitations of each, but don't dwell on them. The important thing is to embrace today and make the most of the time and love you have now.\",\n", "        b\"Apr 27, 2024 - A female visitor could come to your door today with some interesting, useful information, <PERSON>. It might involve anything from stock market trends to a forthcoming wedding to occult and metaphysical matters. Whatever it is, you'll find it captivating, and might just sit and listen to your guest for quite a while. By the time she leaves, your mind may well be spinning. Take a walk to clear your head or you'll be awake all night.\",\n", "        b'Feb 6, 2024 - Why not take some much deserved rest and relaxation today, <PERSON><PERSON><PERSON>? After all, even you need to jump out of the rat race once in a while. If there are some things you really need to care of, you can still make plans for leisure or recreation afterward. Spoil yourself with an afternoon nap or ordering out for dinner. Take a leisurely walk or a long bath. Take care of yourself by resting as well as accomplishing.',\n", "        b\"Jun 12, 2024 - Sometimes we all feel a little lost, <PERSON><PERSON>. We often want to move mountains and use all our energy and enthusiasm to try to do so. But a few moments later we could feel that all efforts have been in vain. It pays to remember one of the hard facts of life - take nothing for granted. You shouldn't let that keep you from trying to make changes to things around you. Be vigilant.\",\n", "        b\"Apr 8, 2024 - This might prove to be one of those days in which it's very hard to get anything related to work or communication off the ground, Virgo. You could find excuses to stop working more often than usual, particularly if it involves calling people on the phone. Don't fight it. Take care of the most urgent tasks. The world won't come to an end if you put the rest off a while.\",\n", "        b\"Jun 6, 2024 - Today you might find that your social life takes a definite turn for the better. You could receive more than one text or call that piques your interest, <PERSON><PERSON><PERSON>, and you\\xe2\\x80\\x99ll spend a lot of time discussing arrangements for future get-togethers. If you aren't romantically involved now, you may find out about someone promising. If you're involved, make sure you reserve time for an intimate encounter with your special someone, if possible. Have fun.\",\n", "        b\"Jul 7, 2024 - Get your head out of the clouds, <PERSON>. You'll find out that nitpicky details you failed to attend to earlier are now coming back to haunt you. Don't postpone your work any further. The time to take care of the job is now. Balance your checkbook and do your laundry. Clean your bathtub and go grocery shopping. The more you accomplish today, the better you'll feel about yourself tomorrow.\",\n", "        b'Apr 14, 2024 - You might be feeling a little tense because you and your partner are grappling with some thorny financial issues right now, <PERSON>. You could be frustrated because you need to postpone some purchases or investments until the cash starts flowing more freely again. The good news is that this is a fine time to work out such a conflict to a satisfactory resolution. Both of you will need to be open-minded and look at all sides as you make your decisions.',\n", "        b\"May 25, 2024 - Things that have been out of whack in your life should begin to come back into balance, <PERSON><PERSON><PERSON>. The bad news is that it may take a while for the pendulum to return to the optimal point. The good news is that this shift is taking place at all. It will be a gradual change, but you're at a pivotal point in which you can set your sights on exactly what you want.\",\n", "        b\"Jun 16, 2024 - Some people around you might think that you have never really matured, <PERSON><PERSON><PERSON>, that you still have the mind of a child. It might not seem obvious at first, but your lighthearted attitude is also a sign of great wisdom. As with those who have truly committed to long-term romances, you will find out that you'll never lose your lightheartedness.\",\n", "        b\"Mar 7, 2024 - Communication with a significant other might hit a brick wall, and you may not be able to get through. Money could be coming in the mail, <PERSON><PERSON><PERSON>, but probably won't arrive today. This could be a day full of frustrations, but the evening should make up for it. Love and romance look great right now. This is a wonderful day to schedule an evening with your partner - or to look for one. Enjoy!\",\n", "        b\"Jul 17, 2024 - Ingenious techniques for growing your money could come your way today, <PERSON>. While you aren't one to jump into anything, you're certainly likely to give these ideas serious thought. Take care to only go for those that are totally up front, with no hidden sides. Your passion for delving into new professional fields is only surpassed by your romantic passions. Plan a seductive evening with a partner. Make it a night to remember!\",\n", "        b\"Feb 20, 2024 - Your home could be a gloomy place today, Cancer. A member of the household is likely to be away, and this person's presence is apt to be very sorely missed. The only thing you can do under the circumstances is make sure everyone keeps busy until the absent one returns. One way to distract the family is to get them to clean up the house in anticipation of a warm welcome!\",\n", "        b\"May 15, 2024 - Today is a good day to dream, Libra. Be aware that structured forces may try to convince you that the route you want to take isn't the most practical. You don't necessarily have to be practical in order to be successful or prosperous. Use your imagination. Let your creative spirit lead you to the next step. Find true strength in your ability to recognize and understand the needs of others.\",\n", "        b\"Feb 14, 2024 - If you've been thinking about starting a new business partnership, Taurus, this is the day to take some positive action toward that end. Any new business enterprises started today, particularly involving partnerships, show a lot of promise for success. This might make a profound difference in the course of your life, so make sure you allow for as many contingencies as you can. Go for the gold and enjoy the adventure!\",\n", "        b\"May 1, 2024 - Your feelings may deceive you, <PERSON><PERSON>, so be careful of getting too wrapped up in your own drama. Perhaps you feel you're getting pushed and pulled in uncomfortable directions. You might tolerate this tension for a while without expressing your feelings about it. This is a dangerous policy because it could give others the impression that it's OK to continue treating you the way they do.\",\n", "        b\"Jun 29, 2024 - As you follow the path toward acquiring the latest, greatest, fastest, and best, you might find that you're leaving behind some fundamental principles and values along the way, <PERSON><PERSON><PERSON>. Don't lose sight of your foundation. Things could get shaken up today. Your ego might be on trial for pig-headed behavior. Keep yourself in check and be aware of the way you project yourself to others.\",\n", "        b\"Jun 20, 2024 - People would be wise to get out of your way today, <PERSON><PERSON>. You might find yourself feeling like a steam engine that's stoked to the brim with fiery hot coals. You're likely to be adamant about your course, and no one is going to be able to pull you off track. If people look carefully, they might even be able to see the steam coming out of your ears.\",\n", "        b\"Jul 17, 2024 - Social responsibilities might appear to be hitting you all at once. Everyone you know seems to crave your company. As a naturally polite and socially astute person, you might feel obligated to accept any and all invitations. This isn't a good day to commit yourself. Wait a while until you're feeling a little more balanced, then send out your responses. Commit only to those events you would genuinely enjoy.\"],\n", "       dtype=object)>,\n", " <tf.Tensor: shape=(32,), dtype=int64, numpy=\n", " array([0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1,\n", "        1, 1, 1, 0, 1, 1, 0, 0, 0, 0])>)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["list(train_data)[0]"]}, {"cell_type": "markdown", "metadata": {"id": "8fuAgmeoshDq"}, "source": ["Now we want to embed and build our model\n"]}, {"cell_type": "markdown", "metadata": {"id": "TtcnhgntlfTO"}, "source": ["# EMBEDDING LAYER"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eNQyNk4xsgyu"}, "outputs": [], "source": ["# using the embedding layer from tensorflow hub\n", "embedding = \"https://tfhub.dev/google/nnlm-en-dim50/2\"\n", "hub_layer = hub.KerasLayer(embedding, dtype=tf.string, trainable=True)"]}, {"cell_type": "markdown", "metadata": {"id": "9HuvGzCHljZa"}, "source": ["# MODEL"]}, {"cell_type": "markdown", "metadata": {"id": "9ZbNGtUItqem"}, "source": ["Now, we need to build out our neural network model.\n", "We want various layers here built out with the Sequential model since it's a way of stacking the layers one by one, and is the easiest model to understand and visualize. We are also going to be using Dropout layers since it's a good way to prevent overfitting, which can lead your model astray. We are going to be using a dropout of 0.4, 0.3 and 0.2, so 40%, 30% and 20% of our neural networks neurons will be randomly dropped out, or set to zero, so that our model can work better."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VYdwnrglttc6"}, "outputs": [], "source": ["# model\n", "model = tf.keras.Sequential()  # since layer by layer so sequential. most basic form\n", "model.add(hub_layer)\n", "model.add(tf.keras.layers.Dense(128, activation=\"relu\"))  # first neural network layer\n", "model.add(tf.keras.layers.Dropout(0.4))\n", "model.add(tf.keras.layers.Dense(64, activation=\"relu\"))  # second layer\n", "model.add(tf.keras.layers.Dropout(0.3))  # another dropout layer\n", "model.add(tf.keras.layers.Dense(32, activation=\"relu\"))  # third layer\n", "model.add(tf.keras.layers.Dropout(0.2))  # another dropout layer\n", "# sigmoid is used for binary, so great for sentiment analysis\n", "model.add(tf.keras.layers.Dense(1, activation=\"sigmoid\"))  # output layer"]}, {"cell_type": "markdown", "metadata": {"id": "XxZa-30Zu4F8"}, "source": ["Now we want to compile our model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5RQoC_6Mu50b"}, "outputs": [], "source": ["model.compile(\n", "    optimizer=tf.keras.optimizers.<PERSON>(learning_rate=0.0001),\n", "    loss=tf.keras.losses.BinaryCrossentropy(),\n", "    metrics=[\"accuracy\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "397o1KbsxiZC"}, "source": ["Let's now train our model on our training data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dqLwEyrywVWK", "outputId": "b8fda7a5-1751-4b72-a00a-e7f332467957"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/5\n", "25/25 [==============================] - 41s 1s/step - loss: 0.6675 - accuracy: 0.6010 - val_loss: 0.6809 - val_accuracy: 0.6061\n", "Epoch 2/5\n", "25/25 [==============================] - 37s 1s/step - loss: 0.6375 - accuracy: 0.6768 - val_loss: 0.6621 - val_accuracy: 0.6515\n", "Epoch 3/5\n", "25/25 [==============================] - 36s 1s/step - loss: 0.6014 - accuracy: 0.7374 - val_loss: 0.6392 - val_accuracy: 0.6818\n", "Epoch 4/5\n", "25/25 [==============================] - 36s 1s/step - loss: 0.5329 - accuracy: 0.8422 - val_loss: 0.6149 - val_accuracy: 0.6919\n", "Epoch 5/5\n", "25/25 [==============================] - 36s 1s/step - loss: 0.4718 - accuracy: 0.8952 - val_loss: 0.5931 - val_accuracy: 0.6970\n"]}], "source": ["history = model.fit(train_data, epochs=5, validation_data=val_data)"]}, {"cell_type": "markdown", "metadata": {"id": "gsPNlZWjy84B"}, "source": ["Once again we see that the loss may be plateauing in some places, but is overall decreasing. We can see that our val_accuracy is increasing so this means that our model is being trained nicely on our dataset.\n", "\n", "Now, evaluate our model on our test dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gyHB2FxjzG6k", "outputId": "ae880d76-d212-4fcc-ba86-f63280ceb714"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["11/11 [==============================] - 2s 184ms/step - loss: 0.5995 - accuracy: 0.6970\n", "Loss: 0.5995222926139832\n", "Accuracy: 0.6969696879386902\n"]}], "source": ["loss, accuracy = model.evaluate(test_data)\n", "print(f\"Loss: {loss}\")\n", "print(f\"Accuracy: {accuracy}\")"]}, {"cell_type": "markdown", "metadata": {"id": "8vTwYXpelm-G"}, "source": ["# NEW HOROSCOPE PREDICTION"]}, {"cell_type": "markdown", "metadata": {"id": "SyqnuMJfjb5C"}, "source": ["https://www.tensorflow.org/api_docs/python/tf/squeeze\n", "\n", "tf.squeeze is how you can get the probability from our prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fMsYaYtg_UKa", "outputId": "8804bdf1-b101-46a0-aa3a-9b7d9e3be725"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1/1 [==============================] - 0s 408ms/step\n", "model probability: 0.615037739276886\n", "1/1 [==============================] - 0s 268ms/step\n", "model probability: 0.43571737408638\n", "This should be positive: 1\n", "This should be negative: 0\n"]}], "source": ["def predict_sentiment(horoscope):\n", "    # convert to so tensorflow can understand\n", "    encoded_input = tf.constant([horoscope])\n", "\n", "    prediction = model.predict(encoded_input)\n", "\n", "    # prediction from probability\n", "    probability = tf.squeeze(prediction).numpy()\n", "    print(f\"model probability: {probability}\")\n", "\n", "    # set it so that we can see if it's negative or positive\n", "    sentiment = 1 if probability > 0.5 else 0\n", "\n", "    return sentiment\n", "\n", "\n", "# daily horoscope\n", "positive_horoscope = \"You're incredibly productive, with good business sense, Libra.\"\n", "negative_horoscope = \"This isn't the most cheerful time, <PERSON>, because important issues are rearing their heads again and forcing you to address them.\"\n", "pos_sentiment = predict_sentiment(positive_horoscope)\n", "neg_sentiment = predict_sentiment(negative_horoscope)\n", "\n", "print(f\"This should be positive: {pos_sentiment}\")\n", "print(f\"This should be negative: {neg_sentiment}\")"]}, {"cell_type": "markdown", "metadata": {"id": "96paVURP88zc"}, "source": ["## Let's see how our week will be going forward"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Tjaer6hN9UWB", "outputId": "d86aa9ff-8a77-4e81-fa7d-b2b05e25ca7f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1/1 [==============================] - 0s 193ms/step\n", "model probability: 0.6883848905563354\n", "1/1 [==============================] - 0s 202ms/step\n", "model probability: 0.***********17017\n", "1/1 [==============================] - 0s 336ms/step\n", "model probability: 0.5237581729888916\n", "1/1 [==============================] - 0s 278ms/step\n", "model probability: 0.3824429214000702\n", "1/1 [==============================] - 0s 269ms/step\n", "model probability: 0.6003984212875366\n", "1/1 [==============================] - 0s 317ms/step\n", "model probability: 0.6250004172325134\n", "1/1 [==============================] - 0s 265ms/step\n", "model probability: 0.6173655390739441\n", "1/1 [==============================] - 0s 278ms/step\n", "model probability: 0.6848816275596619\n", "1/1 [==============================] - 0s 283ms/step\n", "model probability: 0.6913534998893738\n", "1/1 [==============================] - 0s 281ms/step\n", "model probability: 0.6552363634109497\n", "1/1 [==============================] - 0s 270ms/step\n", "model probability: 0.5873440504074097\n", "1/1 [==============================] - 0s 281ms/step\n", "model probability: 0.395006000995636\n", "Aries horoscope is 1\n", "Taurus horoscope is 1\n", "Gemini horoscope is 1\n", "Cancer horoscope is 0\n", "Leo horoscope is 1\n", "Virgo horoscope is 1\n", "Libra horoscope is 1\n", "Scorpio horoscope is 1\n", "Sagittarius horoscope is 1\n", "Capricorn horoscope is 1\n", "Aquarius horoscope is 1\n", "Pisces horoscope is 0\n"]}], "source": ["file = \"new-week-horoscopes2.csv\"\n", "df = pd.read_csv(file)\n", "\n", "df[\"sentiment\"] = df[\"horoscope\"].apply(predict_sentiment)\n", "\n", "for index, row in df.iterrows():\n", "    zodiac = row[\"zodiac\"]\n", "    horoscope = row[\"horoscope\"]\n", "    sentiment = row[\"sentiment\"]\n", "    print(f\"{zodiac} horoscope is {sentiment}\")"]}, {"cell_type": "markdown", "metadata": {"id": "w0sNFSqVGC4W"}, "source": ["## lets save these back into MongoDB Atlas so we can visualize them in Charts"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WGTp-TB1GGMQ", "outputId": "c3db8061-1f7d-4547-ef2a-84d94e442df7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pym<PERSON>o\n", "  Downloading pymongo-4.8.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (22 kB)\n", "Collecting dnspython<3.0.0,>=1.16.0 (from pymongo)\n", "  Downloading dnspython-2.6.1-py3-none-any.whl.metadata (5.8 kB)\n", "Downloading pymongo-4.8.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m23.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dnspython-2.6.1-py3-none-any.whl (307 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m18.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: dnspython, pymongo\n", "Successfully installed dnspython-2.6.1 pymongo-4.8.0\n"]}], "source": ["pip install pymongo"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gTjIJSXyGPUz", "outputId": "4b6c7963-c270-4d4c-c197-9416cf3ad6d2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter connection string WITH USER + PASS here··········\n", "saved in! go check\n"]}], "source": ["# first connect to MongoDB Atlas\n", "import getpass\n", "\n", "from pymongo import MongoClient\n", "\n", "# set up your MongoDB connection\n", "connection_string = getpass.getpass(\n", "    prompt=\"Enter connection string WITH USER + PASS here\"\n", ")\n", "client = MongoClient(\n", "    connection_string, appname=\"devrel.showcase.tensorflow_mongodbcharts\"\n", ")\n", "\n", "# we are creating a new collection in the same database as before\n", "database = client[\"horoscopes\"]\n", "collection = database[\"new_week_horoscope\"]\n", "\n", "for index, row in df.iterrows():\n", "    zodiac = row[\"zodiac\"]\n", "    horoscope = row[\"horoscope\"]\n", "    sentiment = row[\"sentiment\"]\n", "\n", "    dict = {\"zodiac\": zodiac, \"horoscope\": horoscope, \"sentiment\": sentiment}\n", "\n", "    collection.insert_one(dict)\n", "\n", "\n", "print(\"saved in! go check\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"066597d2e3b545c4ad832adea9a1ea70": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "07403199e5e5427f8243f7d38bce962c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "07df2dd843db4a5eb14cbf55eee02d65": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0a262fc5c2304b299f5a8ae14b8e7ace": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "106986cf19c340c28e07636ab69d4dc4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6e2a096fe51a49dda08e2ad6d7456c0b", "placeholder": "​", "style": "IPY_MODEL_dfbf5321bb4a49fbb046cc0011ebd508", "value": "config.json: 100%"}}, "1fb6b73395194edf80e0c32478068dc4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_066597d2e3b545c4ad832adea9a1ea70", "max": 267832558, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5474edeea875477e84c87febb0e2c98e", "value": 267832558}}, "27f842a5d3a34174a960ef7a7b025665": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2b4e633979de49368460b09486847eb5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bce274686e084ab79f75679e9574729a", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2faa230a9412434d8be564fcac05e9a4", "value": 231508}}, "2faa230a9412434d8be564fcac05e9a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3ac89cbe689f4699a0aa9dc9edab85de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3b33ad4c130444ffbe7a5b23d76384f5", "placeholder": "​", "style": "IPY_MODEL_bbc815ce1bb34c569d24be6734a64891", "value": " 48.0/48.0 [00:00&lt;00:00, 1.72kB/s]"}}, "3b33ad4c130444ffbe7a5b23d76384f5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "457cfed772634defbdb74af748e77f9b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_59a0115b0cbf49b0bd43936292d29678", "placeholder": "​", "style": "IPY_MODEL_9b9344aa7dbd4a02ba2cf757cdbc963f", "value": "model.safetensors: 100%"}}, "4b0e77d8ce3b4be987c52fed07c765a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f624dbdf918425c9545d1e1f3b9fbd3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fc6c8a098ac54feb98c9b24a95ab88a1", "placeholder": "​", "style": "IPY_MODEL_07df2dd843db4a5eb14cbf55eee02d65", "value": " 629/629 [00:00&lt;00:00, 22.0kB/s]"}}, "546387dcdbdc408dae7e728d5c123608": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5474edeea875477e84c87febb0e2c98e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "59a0115b0cbf49b0bd43936292d29678": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5b6727464c6640a08bb4fcb0efd53571": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5bb06a85c9684aa8aea37bee508d4aa4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7953c87952fc46c4a67660e9343972a2", "IPY_MODEL_2b4e633979de49368460b09486847eb5", "IPY_MODEL_ae052cb87ce84cf486accafdf0e9d701"], "layout": "IPY_MODEL_5b6727464c6640a08bb4fcb0efd53571"}}, "60080f8c348c4813a3d1c0b8d3599361": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6e2a096fe51a49dda08e2ad6d7456c0b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f755b7843274dfc9209efcc5ed7d29d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7953c87952fc46c4a67660e9343972a2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_546387dcdbdc408dae7e728d5c123608", "placeholder": "​", "style": "IPY_MODEL_f43e3aaf188a45aaa67d07ff55fd1d42", "value": "vocab.txt: 100%"}}, "8321d09f32d649f5b117289fdfe36b06": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84f42f0541df44b48bf1546086e74c0a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8cfb979551d74482a06eb63f6d5c7a29": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6f755b7843274dfc9209efcc5ed7d29d", "max": 48, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f8e90c44c62c40b4b09b51c5fa34a59b", "value": 48}}, "8dfe9277291e4941b078b642b5fefc22": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9b9344aa7dbd4a02ba2cf757cdbc963f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9e9f251b8fdb45aab48df63b77539adb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8321d09f32d649f5b117289fdfe36b06", "placeholder": "​", "style": "IPY_MODEL_07403199e5e5427f8243f7d38bce962c", "value": " 268M/268M [00:02&lt;00:00, 148MB/s]"}}, "a036e88a5e2c4960ba5df9ccd6726556": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a5dcdc712f674ba9aa3ea6d7bdb3d97d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b1a4590564b34786b15f6e3aedf076a0", "IPY_MODEL_8cfb979551d74482a06eb63f6d5c7a29", "IPY_MODEL_3ac89cbe689f4699a0aa9dc9edab85de"], "layout": "IPY_MODEL_84f42f0541df44b48bf1546086e74c0a"}}, "ae052cb87ce84cf486accafdf0e9d701": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0a262fc5c2304b299f5a8ae14b8e7ace", "placeholder": "​", "style": "IPY_MODEL_8dfe9277291e4941b078b642b5fefc22", "value": " 232k/232k [00:00&lt;00:00, 2.78MB/s]"}}, "b1a4590564b34786b15f6e3aedf076a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b23e522db72344cfa4a2ebdda2e2e2ef", "placeholder": "​", "style": "IPY_MODEL_ebcb407f178642c79b8f0c805ee7dabb", "value": "tokenizer_config.json: 100%"}}, "b23e522db72344cfa4a2ebdda2e2e2ef": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b79ed4c354704e508ccdf7c3c9ccb418": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a036e88a5e2c4960ba5df9ccd6726556", "max": 629, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_60080f8c348c4813a3d1c0b8d3599361", "value": 629}}, "bbc815ce1bb34c569d24be6734a64891": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bce274686e084ab79f75679e9574729a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cc4ab91bf4494416b7b1f4da183e62f7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_106986cf19c340c28e07636ab69d4dc4", "IPY_MODEL_b79ed4c354704e508ccdf7c3c9ccb418", "IPY_MODEL_4f624dbdf918425c9545d1e1f3b9fbd3"], "layout": "IPY_MODEL_27f842a5d3a34174a960ef7a7b025665"}}, "dfbf5321bb4a49fbb046cc0011ebd508": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ebcb407f178642c79b8f0c805ee7dabb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f43e3aaf188a45aaa67d07ff55fd1d42": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f8e90c44c62c40b4b09b51c5fa34a59b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fc6c8a098ac54feb98c9b24a95ab88a1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd752c9c46864fa8ac691e98f2ef4a94": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_457cfed772634defbdb74af748e77f9b", "IPY_MODEL_1fb6b73395194edf80e0c32478068dc4", "IPY_MODEL_9e9f251b8fdb45aab48df63b77539adb"], "layout": "IPY_MODEL_4b0e77d8ce3b4be987c52fed07c765a0"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}