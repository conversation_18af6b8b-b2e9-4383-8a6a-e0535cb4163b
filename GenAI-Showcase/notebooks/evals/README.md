Jupyter Notebooks demonstrating how to evaluate LLM applications built with MongoDB Atlas.

| Title | Stack | Notebook |
|-------|-------|----------|
| RAGAS Evaluation | MongoDB Atlas, RAGAS | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/ragas-evaluation.ipynb) |
| Voyage AI Embeddings Evaluation | MongoDB Atlas, Voyage AI | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/voyageai-embeddings-eval.ipynb) |
| Angle Embeddings Evaluation | MongoDB Atlas, Angle Embeddings | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/angle-embeddings-eval.ipynb) |
| OpenAI Embeddings Evaluation | MongoDB Atlas, OpenAI | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/openai-embeddings-eval.ipynb) |
| Patronus Evaluation | MongoDB Atlas, Patronus | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/Patronus_MongoDB.ipynb) |
