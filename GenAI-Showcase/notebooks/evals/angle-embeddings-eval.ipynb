{"cells": [{"cell_type": "markdown", "id": "1e3b150c", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/angle-embeddings-eval.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/choose-embedding-model-rag/?utm_campaign=devrel&utm_source=cross-post&utm_medium=organic_social&utm_content=https%3A%2F%2Fgithub.com%2Fmongodb-developer%2FGenAI-Showcase&utm_term=apoorva.joshi)\n"]}, {"cell_type": "markdown", "id": "d5a21319-bef7-4b35-a5c0-09151c738979", "metadata": {}, "source": ["# RAG Series Part 1: How to choose the right embedding model for your RAG application\n", "\n", "This notebook evaluates the [UAE-Large-V1](https://huggingface.co/WhereIsAI/UAE-Large-V1) model.\n"]}, {"cell_type": "markdown", "id": "e75f4997-f3ae-4ba1-9e39-9d9033eb9d43", "metadata": {}, "source": ["## Step 1: Install required libraries\n", "\n", "- **datasets**: Python library to get access to datasets available on Hugging Face Hub\n", "<p>\n", "- **sentence-transformers**: Framework for working with text and image embeddings\n", "<p>\n", "- **transformers**: Python library that provides APIs to interact with pre-trained models available on Hugging Face\n", "<p>\n", "- **numpy**: Python library that provides tools to perform mathematical operations on arrays\n", "<p>\n", "- **pandas**: Python library for data analysis, exploration and manipulation\n", "<p>\n", "- **tdqm**: Python module to show a progress meter for loops\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8419fcc1-9477-48b0-972b-5b1f64896f99", "metadata": {"tags": []}, "outputs": [], "source": ["! pip install -qU datasets sentence-transformers transformers numpy pandas tqdm"]}, {"cell_type": "markdown", "id": "c36ed9c1-46a6-4228-b9bd-2af6b246679d", "metadata": {}, "source": ["## Step 3: Download the evaluation dataset\n", "\n", "We will use MongoDB's [cosmopedia-wikihow-chunked](https://huggingface.co/datasets/MongoDB/cosmopedia-wikihow-chunked) dataset, which has chunked versions of WikiHow articles from the [Cosmopedia](https://huggingface.co/datasets/HuggingFaceTB/cosmopedia) dataset released by Hugging Face. The dataset is pretty large, so we will only grab the first 25k records for testing.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "95b0e185-80da-49d8-a457-7a3571e7c919", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# Use streaming=True to load the dataset without downloading it fully\n", "data = load_dataset(\"MongoDB/cosmopedia-wikihow-chunked\", split=\"train\", streaming=True)\n", "# Get first 25k records from the dataset\n", "data_head = data.take(25000)\n", "df = pd.DataFrame(data_head)\n", "\n", "# Use this if you want the full dataset\n", "# data = load_dataset(\"AIatMongoDB/cosmopedia-wikihow-chunked\", split=\"train\")\n", "# df = pd.DataFrame(data)"]}, {"cell_type": "markdown", "id": "2be26fb7-1033-4050-ac45-737fbe516703", "metadata": {}, "source": ["## Step 4: Data analysis\n", "\n", "Make sure the length of the dataset is what we expect (25k), preview the data, drop Nones etc.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f44fc923-dbca-4c1d-88c8-c2a83f6135dd", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["25000"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Ensuring length of dataset is what we expect i.e. 25k\n", "len(df)"]}, {"cell_type": "code", "execution_count": 4, "id": "60c447d5-b4f5-445b-a0e4-9a8635770879", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>doc_id</th>\n", "      <th>chunk_id</th>\n", "      <th>text_token_length</th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>180</td>\n", "      <td>Title: How to Create and Maintain a Compost Pi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>141</td>\n", "      <td>**Step 2: Gather Materials**\\nGather brown (ca...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>182</td>\n", "      <td>_Key guideline:_ For every volume of green mat...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>188</td>\n", "      <td>_Key tip:_ Chop large items like branches and ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>157</td>\n", "      <td>**Step 7: Maturation and Use**\\nAfter 3-4 mont...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   doc_id  chunk_id  text_token_length  \\\n", "0       0         0                180   \n", "1       0         1                141   \n", "2       0         2                182   \n", "3       0         3                188   \n", "4       0         4                157   \n", "\n", "                                                text  \n", "0  Title: How to Create and Maintain a Compost Pi...  \n", "1  **Step 2: Gather Materials**\\nGather brown (ca...  \n", "2  _Key guideline:_ For every volume of green mat...  \n", "3  _Key tip:_ Chop large items like branches and ...  \n", "4  **Step 7: Maturation and Use**\\nAfter 3-4 mont...  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Previewing the contents of the data\n", "df.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "010a03ad-728f-4c7e-870c-b26d124479d4", "metadata": {"tags": []}, "outputs": [], "source": ["# Only keep records where the text field is not null\n", "df = df[df[\"text\"].notna()]"]}, {"cell_type": "code", "execution_count": 6, "id": "1555874f-329b-433e-82ff-105ed513314d", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["4335"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Number of unique documents in the dataset\n", "df.doc_id.nunique()"]}, {"cell_type": "markdown", "id": "5d46d0c9-5a6c-4dda-9a9d-f97ab04805e6", "metadata": {}, "source": ["## Step 5: Creating embeddings\n", "\n", "Define the embedding function, and run a quick test.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d30251df-1583-4f24-aa38-fe2b19b13ed6", "metadata": {"tags": []}, "outputs": [], "source": ["from typing import List\n", "\n", "import torch\n", "from transformers import AutoModel, AutoTokenizer"]}, {"cell_type": "code", "execution_count": 8, "id": "dd216793", "metadata": {}, "outputs": [], "source": ["# Instruction to append to user queries, to improve retrieval\n", "RETRIEVAL_INSTRUCT = \"Represent this sentence for searching relevant passages:\"\n", "\n", "# Check if CUDA (GPU support) is available, and set the device accordingly\n", "device = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "# Load the UAE-Large-V1 model from the Hugging Face\n", "model = AutoModel.from_pretrained(\"WhereIsAI/UAE-Large-V1\").to(device)\n", "# Load the tokenizer associated with the UAE-Large-V1 model\n", "tokenizer = AutoTokenizer.from_pretrained(\"WhereIsAI/UAE-Large-V1\")"]}, {"cell_type": "code", "execution_count": 9, "id": "267b2d69", "metadata": {}, "outputs": [], "source": ["# Decorator to disable gradient calculations\n", "@torch.no_grad()\n", "def get_embeddings(docs: List[str], input_type: str) -> List[List[float]]:\n", "    \"\"\"\n", "    Get embeddings using the UAE-Large-V1 model.\n", "\n", "    Args:\n", "        docs (List[str]): List of texts to embed\n", "        input_type (str): Type of input to embed. Can be \"document\" or \"query\".\n", "\n", "    Returns:\n", "        List[List[float]]: Array of embedddings\n", "    \"\"\"\n", "    # Prepend retrieval instruction to queries\n", "    if input_type == \"query\":\n", "        docs = [f\"{RETRIEVAL_INSTRUCT}{q}\" for q in docs]\n", "    # Tokenize input texts\n", "    inputs = tokenizer(\n", "        docs, padding=True, truncation=True, return_tensors=\"pt\", max_length=512\n", "    ).to(device)\n", "    # Pass tokenized inputs to the model, and obtain the last hidden state\n", "    last_hidden_state = model(**inputs, return_dict=True).last_hidden_state\n", "    # Extract embeddings from the last hidden state\n", "    embeddings = last_hidden_state[:, 0]\n", "    return embeddings.cpu().numpy()"]}, {"cell_type": "code", "execution_count": 10, "id": "cb39db33", "metadata": {}, "outputs": [], "source": ["test_angle_embed = get_embeddings([df.iloc[0][\"text\"]], \"document\")"]}, {"cell_type": "code", "execution_count": 11, "id": "5e54e0d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["1024"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(test_angle_embed[0])"]}, {"cell_type": "markdown", "id": "ecff7ccc", "metadata": {}, "source": ["## Step 6: Evaluation\n"]}, {"cell_type": "markdown", "id": "de671237-a1bc-486f-b51e-c3e8119c3b75", "metadata": {}, "source": ["### Measuring embedding latency\n", "\n", "Create a local vector store (list) of embeddings for the entire dataset.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "760fda68-6893-47fe-a100-b536b2414b84", "metadata": {"tags": []}, "outputs": [], "source": ["from tqdm.auto import tqdm"]}, {"cell_type": "code", "execution_count": 13, "id": "62aecc55-f8e2-4c25-9598-49d49e5fde6e", "metadata": {"tags": []}, "outputs": [], "source": ["texts = df[\"text\"].tolist()"]}, {"cell_type": "code", "execution_count": 14, "id": "0bf8622e-51f4-4f11-b1db-4cf24630ae54", "metadata": {"tags": []}, "outputs": [], "source": ["batch_size = 128"]}, {"cell_type": "code", "execution_count": 15, "id": "ba148a00-1dca-4f2d-9f69-803e69be49e1", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03d283e4273342ef9f636dd0159af714", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/196 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["embeddings = []\n", "# Generate embeddings in batches\n", "for i in tqdm(range(0, len(texts), batch_size)):\n", "    end = min(len(texts), i + batch_size)\n", "    batch = texts[i:end]\n", "    # Generate embeddings for current batch\n", "    batch_embeddings = get_embeddings(batch, \"document\")\n", "    # Add to the list of embeddings\n", "    embeddings.extend(batch_embeddings)"]}, {"cell_type": "markdown", "id": "ed2514e0-cc9d-4203-a3af-53a9fabacb10", "metadata": {}, "source": ["### Measuring retrieval quality\n", "\n", "- Create embedding for the user query\n", "<p>\n", "- Get the top 5 most similar documents from the local vector store using cosine similarity as the similarity metric\n"]}, {"cell_type": "code", "execution_count": 16, "id": "d038aa19-023c-4c1b-9618-df6fd60390cb", "metadata": {"tags": []}, "outputs": [], "source": ["import numpy as np\n", "from sentence_transformers.util import cos_sim"]}, {"cell_type": "code", "execution_count": 17, "id": "b129e4b7-c2b1-436b-8ef6-fedb28061bcd", "metadata": {}, "outputs": [], "source": ["# Converting embeddings list to a Numpy array- required to calculate cosine similarity\n", "embeddings = np.asarray(embeddings)"]}, {"cell_type": "code", "execution_count": 18, "id": "9a247168-ab58-41ce-a135-6c2dcd929df4", "metadata": {}, "outputs": [], "source": ["def query(query: str, top_k: int = 3) -> None:\n", "    \"\"\"\n", "    Query the local vector store for the top 3 most relevant documents.\n", "\n", "    Args:\n", "        query (str): User query\n", "        top_k (int, optional): Number of documents to return. Defaults to 3.\n", "    \"\"\"\n", "    # Generate embedding for the user query\n", "    query_emb = np.asarray(get_embeddings([query], \"query\"))\n", "    # Calculate cosine similarity\n", "    scores = cos_sim(query_emb, embeddings)[0]\n", "    # Get indices of the top k records\n", "    idxs = np.argsort(-scores)[:top_k]\n", "\n", "    print(f\"Query: {query}\")\n", "    for idx in idxs:\n", "        print(f\"Score: {scores[idx]:.4f}\")\n", "        print(texts[idx])\n", "        print(\"--------\")"]}, {"cell_type": "code", "execution_count": 19, "id": "c9a02484-5e0c-41a8-bf15-da08f8e95d2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: Give me some tips to improve my mental health.\n", "Score: 0.7515\n", "Key Tips & Guidelines:\n", "\n", "* Acknowledge that prioritizing mental health is not selfish but necessary for maintaining optimal functioning.\n", "* Accept that everyone experiences ups and downs; seeking support when needed demonstrates strength rather than weakness.\n", "\n", "Step 2: Practice Self-Awareness\n", "Developing mindfulness skills allows individuals to become aware of their thoughts, feelings, and physical sensations without judgment. Being present in the moment fosters self-compassion, promotes relaxation, and enhances problem-solving abilities.\n", "\n", "Key Tips & Guidelines:\n", "\n", "* Set aside time each day for meditation or deep breathing exercises.\n", "* Journal regularly to explore thoughts and emotions objectively.\n", "* Engage in activities (e.g., yoga, tai chi) that encourage both physical movement and introspection.\n", "\n", "Step 3: Establish Healthy Lifestyle Habits\n", "Physical health significantly impacts mental well-being. Adopting healthy habits contributes to improved mood regulation, reduced anxiety symptoms, and enhanced cognitive function.\n", "\n", "Key Tips & Guidelines:\n", "--------\n", "Score: 0.7452\n", "Key Tips & Guidelines:\n", "\n", "* Follow a balanced diet rich in fruits, vegetables, lean proteins, whole grains, and omega-3 fatty acids.\n", "* Exercise consistently, aiming for at least 30 minutes per day most days of the week.\n", "* Get sufficient sleep (7-9 hours nightly). Develop a bedtime routine and create a conducive sleeping environment.\n", "\n", "Step 4: Cultivate Positive Relationships\n", "Social connections play a vital role in promoting mental health. Building strong, supportive networks encourages open communication, empathy, and validation while combating loneliness and isolation.\n", "\n", "Key Tips & Guidelines:\n", "\n", "* Nurture existing friendships by scheduling regular meetups and engaging in shared interests.\n", "* Seek out new social opportunities through clubs, classes, or volunteering.\n", "* Learn to identify unhealthy relationship patterns and establish boundaries accordingly.\n", "\n", "Step 5: Manage Stress Effectively\n", "Chronic stress can wreak havoc on mental health. Implementing effective coping mechanisms enables individuals to navigate adversities constructively and minimize negative consequences.\n", "--------\n", "Score: 0.7256\n", "Title: How to Care About Your Mental Health\n", "\n", "Introduction:\n", "Mental health is an essential aspect of overall well-being that often goes overlooked. Taking care of your mental health can help you lead a happier, more fulfilling life, increase productivity, strengthen relationships, and reduce stress levels. This comprehensive guide will provide you with actionable steps and valuable insights into caring about your mental health. By following these steps, you'll be better equipped to manage daily pressures, cope with setbacks, and maintain emotional balance.\n", "\n", "Step 1: Recognize the Importance of Mental Health\n", "Understanding why mental health matters is crucial before diving into strategies for improvement. Mental health affects every facet of our lives – from work performance to personal relationships. Prioritizing self-care and managing emotions contribute to increased resilience during challenging times. Additionally, taking charge of your mental health reduces the risk of developing severe psychological conditions such as depression or anxiety disorders.\n", "\n", "Key Tips & Guidelines:\n", "--------\n"]}], "source": ["query_emb = query(\"Give me some tips to improve my mental health.\")"]}, {"cell_type": "code", "execution_count": 20, "id": "5990050f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: Give me some tips for writing good code.\n", "Score: 0.7326\n", "Step 6: Improve Code Quality\n", "Strive for clean, readable, maintainable code. Adopt consistent naming conventions, indentation styles, and formatting rules. Utilize version control systems like Git to track changes and collaborate effectively. Leverage linters and static analyzers to enforce style guides automatically. Document your work using comments and dedicated documentation tools. High-quality code facilitates collaboration, promotes longevity, and simplifies troubleshooting.\n", "\n", "Step 7: <PERSON><PERSON><PERSON> Best Practices\n", "Follow established best practices relevant to your chosen language and domain. Examples include Object-Oriented Design Principles, SOLID principles, Test-Driven Development (TDD), Dependency Injection, Asynchronous Programming, etc. While seemingly overwhelming initially, integrating them gradually enhances design patterns, scalability, and extensibility. Consult authoritative blogs, books, and articles to stay updated on current trends and recommendations.\n", "--------\n", "Score: 0.6975\n", "### Step 6: Read Books About Programming\n", "Books offer comprehensive coverage, deep insights, best practices, historical context, and advanced topics unavailable in most online tutorials. Deliberately select texts targeting beginners yet providing progressive pathways towards proficiency. Popular recommendations include \"Python Crash Course\" by <PERSON>, \"Eloquent JavaScript\" by <PERSON><PERSON>, and \"JavaScript & JQuery: Interactive Front-End Web Development\" by <PERSON>.\n", "\n", "#### Key Tips & Guidelines\n", "* Combine reading with active coding sessions to reinforce comprehension and application abilities.\n", "* Take notes summarizing critical points, highlighting unfamiliar terminology, and jotting down potential areas for future study.\n", "* Engage with author-recommended exercises, assignments, or challenges prompted throughout chapters.\n", "--------\n", "Score: 0.6656\n", "Title: How to Become a Good Programmer\n", "\n", "Introduction:\n", "Programming is an essential skill in today's digital world. It opens up various opportunities in different industries such as software development, web development, data science, artificial intelligence, and many more. This comprehensive guide will provide you with actionable steps and valuable insights to become a good programmer. By following this tutorial, you'll learn programming fundamentals, best practices, and strategies for continuous improvement.\n", "\n", "Step 1: Choose Your Programming Language\n", "Choosing the right programming language is crucial when starting your coding journey. Key factors include your interests, career goals, and available resources. Some popular choices are Python (general-purpose), JavaScript (web development), Java (enterprise applications), or C# (game development). Research and select one that aligns with your objectives. For beginners, Python and JavaScript are great options due to their simplicity and versatility.\n", "--------\n"]}], "source": ["query_emb = query(\"Give me some tips for writing good code.\")"]}, {"cell_type": "code", "execution_count": 21, "id": "9fc742d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: How do I create a basic webpage?\n", "Score: 0.8068\n", "Creating a webpage with basic HTML is an exciting endeavor that allows you to share information, showcase your creativity, or build the foundation for a more complex website. This comprehensive guide will walk you through each step required to create a simple yet effective static webpage using HTML (HyperText Markup Language), the standard language used to structure content on the World Wide Web.\n", "\n", "**Step 1: <PERSON><PERSON> a Text Editor**\n", "\n", "Before diving into coding, select a suitable text editor for writing your HTML code. Popular choices include Notepad++ (Windows), Visual Studio Code (cross-platform), Atom (cross-platform), and Sublime Text (cross-platform). These editors offer features such as syntax highlighting, auto-completion, and error detection, making them ideal for beginners and professionals alike.\n", "\n", "Key tip: Avoid using word processors like Microsoft Word or Google Docs, as they may add unnecessary formatting codes that can interfere with your HTML.\n", "\n", "**Step 2: Create an HTML Document**\n", "--------\n", "Score: 0.7550\n", "Designing a personal web page can be an exciting and rewarding experience. It allows you to express yourself, showcase your skills or hobbies, and even build an online portfolio. This comprehensive guide will walk you through the process of creating your own personal webpage from scratch, with clear explanations, helpful tips, and best practices along the way.\n", "\n", "### Part 1: Planning Your Personal Web Page\n", "\n", "#### Step 1: Define Your Goals and Objectives\n", "\n", "Before diving into design and development, take some time to reflect on what you want to accomplish with your personal web page. Here are some questions to consider:\n", "\n", "* What is the primary purpose of your website? (e.g., resume, blog, portfolio)\n", "* Who is your target audience? (e.g., potential employers, friends, like-minded enthusiasts)\n", "* Which elements do you want to include on your site? (e.g., about me, contact form, photo gallery)\n", "--------\n", "Score: 0.7497\n", "Step 4: Create Basic HTML Structure\n", "Start building your codebit by adding basic HTML structure elements like `<!DOCTYPE html>`, `<html lang=\"en\">`, `<head>`, and `<body>`. The DOCTYPE declaration sets the document type for modern web browsers; setting the language attribute within the `<html>` tag ensures proper rendering and screen reader support. Here is what the initial boilerplate code would look like:\n", "\n", "```html\n", "<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <title>Document Title</title>\n", "</head>\n", "<body>\n", "    <!-- Insert Content Here -->\n", "</body>\n", "</html>\n", "```\n", "\n", "Replace 'Document Title' with a suitable title for your page.\n", "\n", "Key Tips & Guidelines:\n", "--------\n"]}], "source": ["query(\"How do I create a basic webpage?\")"]}, {"cell_type": "code", "execution_count": 22, "id": "3bcdd57c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: What are some environment-friendly practices I can incorporate in everyday life?\n", "Score: 0.7612\n", "Step 15: Share Knowledge With Others\n", "Educate friends, family, and colleagues about eco-friendly practices and benefits. Offer support during their transitions. Key tip: Lead by example without judgment. Guideline: Host workshops or discussion groups centered around sustainable living topics.\n", "\n", "Conclusion:\n", "By following these 15 steps, you'll become an earthy girl who embodies respect for our planet and its resources. Remember, every effort counts—big or small! Celebrate successes along the journey while striving for continuous improvement.\n", "--------\n", "Score: 0.7339\n", "* **Carry Reusable Bags**: Keep cloth or other reusable shopping bags handy for grocery trips and errands. Store extra bags in your car, purse, or backpack for convenience.\n", "* **Choose Sustainable Alternatives**: Opt for reusable water bottles, coffee cups, straws, and cutlery made from eco-friendly materials like stainless steel, glass, silicone, or bamboo.\n", "* **Support Businesses that Minimize Packaging**: Patronize establishments that prioritize sustainable practices, such as using biodegradable packaging or offering package-free options.\n", "\n", "Step 3: Conserve Energy and Water\n", "Explanation: Efficient energy and water usage help decrease reliance on nonrenewable resources and lower greenhouse gas emissions.\n", "--------\n", "Score: 0.7062\n", "* **Participate in Local Tree-Planting Initiatives**: Join citywide planting events organized by parks departments, schools, or environmental organizations. Volunteer at community gardens, nature preserves, or restoration projects.\n", "* **Cultivate Native Flora**: Integrate regionally appropriate plants into home landscaping designs to support pollinators and provide ecosystem services. Remove invasive exotic species that outcompete natives and disrupt ecological balance.\n", "\n", "Step 6: Adopt Eco-Friendly Transportation Methods\n", "Explanation: Choosing alternative transportation modes decreases fossil fuel consumption and associated greenhouse gasses.\n", "--------\n"]}], "source": ["query(\n", "    \"What are some environment-friendly practices I can incorporate in everyday life?\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 5}