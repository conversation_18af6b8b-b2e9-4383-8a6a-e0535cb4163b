{"cells": [{"cell_type": "markdown", "id": "8dd22323", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/openai-embeddings-eval.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/choose-embedding-model-rag/?utm_campaign=devrel&utm_source=cross-post&utm_medium=organic_social&utm_content=https%3A%2F%2Fgithub.com%2Fmongodb-developer%2FGenAI-Showcase&utm_term=apoorva.joshi)"]}, {"cell_type": "markdown", "id": "39b4d49e-31a1-4093-9255-9cb8e6f96b0d", "metadata": {"tags": []}, "source": ["# RAG Series Part 1: How to choose the right embedding model for your RAG application\n", "\n", "This notebook evaluates the [text-embedding-3-large](https://openai.com/blog/new-embedding-models-and-api-updates) model.\n"]}, {"cell_type": "markdown", "id": "f3a115b9-68e5-44f7-9ea7-fff56bc9ee59", "metadata": {}, "source": ["## Step 1: Install required libraries\n", "\n", "- **datasets**: Python library to get access to datasets available on Hugging Face Hub\n", "<p>\n", "- **openai**: Python library to interact with OpenAI APIs\n", "<p>\n", "- **sentence-transformers**: Framework for working with text and image embeddings\n", "<p>\n", "- **numpy**: Python library that provides tools to perform mathematical operations on arrays\n", "<p>\n", "- **pandas**: Python library for data analysis, exploration and manipulation\n", "<p>\n", "- **tdqm**: Python module to show a progress meter for loops\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a999fe13-3eee-4fd8-a9fd-0f2f37171ed3", "metadata": {"tags": []}, "outputs": [], "source": ["! pip install -qU datasets openai sentence-transformers numpy pandas tqdm"]}, {"cell_type": "markdown", "id": "87bd8b3e-984b-4dff-bd7f-615e577a9ef8", "metadata": {}, "source": ["## Step 2: Setup pre-requisites\n", "\n", "Set OpenAI API key as environment variable, and initialize the OpenAI client.\n", "\n", "Steps to obtain an OpenAI API Key can be found [here](https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f62e40d3-852c-4abf-9151-875a1d32e93e", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 3, "id": "a8e8bcde-c242-4641-a7c8-5f69c60d021e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key:········\n"]}], "source": ["os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"OpenAI API Key:\")\n", "openai_client = OpenAI()"]}, {"cell_type": "markdown", "id": "b5a99a68-a7d2-4657-8f05-ea75f19b6748", "metadata": {}, "source": ["## Step 3: Download the evaluation dataset\n", "\n", "We will use MongoDB's [cosmopedia-wikihow-chunked](https://huggingface.co/datasets/MongoDB/cosmopedia-wikihow-chunked) dataset, which has chunked versions of WikiHow articles from the [Cosmopedia](https://huggingface.co/datasets/HuggingFaceTB/cosmopedia) dataset released by Hugging Face. The dataset is pretty large, so we will only grab the first 25k records for testing.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "7862e2db-fec8-4294-ad75-9753e69adc1a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# Use streaming=True to load the dataset without downloading it fully\n", "data = load_dataset(\"MongoDB/cosmopedia-wikihow-chunked\", split=\"train\", streaming=True)\n", "# Get first 25k records from the dataset\n", "data_head = data.take(25000)\n", "df = pd.DataFrame(data_head)\n", "\n", "# Use this if you want the full dataset\n", "# data = load_dataset(\"AIatMongoDB/cosmopedia-wikihow-chunked\", split=\"train\")\n", "# df = pd.DataFrame(data)"]}, {"cell_type": "markdown", "id": "70d329bc-cdb7-4651-bef0-8d2ae09d9e4b", "metadata": {}, "source": ["## Step 4: Data analysis\n", "\n", "Make sure the length of the dataset is what we expect (25k), preview the data, drop Nones etc.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "39c0f32d-c6f7-4faa-92e1-fae25e9eb2ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["25000"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Ensuring length of dataset is what we expect i.e. 25k\n", "len(df)"]}, {"cell_type": "code", "execution_count": 6, "id": "6782ab49-3d9d-4f67-8b33-474f02b7e993", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>doc_id</th>\n", "      <th>chunk_id</th>\n", "      <th>text_token_length</th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>180</td>\n", "      <td>Title: How to Create and Maintain a Compost Pi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>141</td>\n", "      <td>**Step 2: Gather Materials**\\nGather brown (ca...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>182</td>\n", "      <td>_Key guideline:_ For every volume of green mat...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>188</td>\n", "      <td>_Key tip:_ Chop large items like branches and ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>157</td>\n", "      <td>**Step 7: Maturation and Use**\\nAfter 3-4 mont...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   doc_id  chunk_id  text_token_length  \\\n", "0       0         0                180   \n", "1       0         1                141   \n", "2       0         2                182   \n", "3       0         3                188   \n", "4       0         4                157   \n", "\n", "                                                text  \n", "0  Title: How to Create and Maintain a Compost Pi...  \n", "1  **Step 2: Gather Materials**\\nGather brown (ca...  \n", "2  _Key guideline:_ For every volume of green mat...  \n", "3  _Key tip:_ Chop large items like branches and ...  \n", "4  **Step 7: Maturation and Use**\\nAfter 3-4 mont...  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Previewing the contents of the data\n", "df.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "04563eaf-bbd8-4969-9671-eb5312817402", "metadata": {}, "outputs": [], "source": ["# Only keep records where the text field is not null\n", "df = df[df[\"text\"].notna()]"]}, {"cell_type": "code", "execution_count": 8, "id": "cd5a91c3-2f68-4157-a747-05bbc934d53a", "metadata": {}, "outputs": [{"data": {"text/plain": ["4335"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Number of unique documents in the dataset\n", "df.doc_id.nunique()"]}, {"cell_type": "markdown", "id": "0400259f-65ca-4301-a245-7af0b746abf1", "metadata": {}, "source": ["## Step 5: Creating embeddings\n", "\n", "Define the embedding function, and run a quick test.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "3d936743-f18b-410e-8397-c0acf9c61a5e", "metadata": {}, "outputs": [], "source": ["from typing import List"]}, {"cell_type": "code", "execution_count": 10, "id": "bda20d74-7296-40df-ab19-ea63a5b47e6d", "metadata": {}, "outputs": [], "source": ["def get_embeddings(\n", "    docs: List[str], model: str = \"text-embedding-3-large\"\n", ") -> List[List[float]]:\n", "    \"\"\"\n", "    Generate embeddings using the OpenAI API.\n", "\n", "    Args:\n", "        docs (List[str]): List of texts to embed\n", "        model (str, optional): Model name. Defaults to \"text-embedding-3-large\".\n", "\n", "    Returns:\n", "        List[float]: Array of embeddings\n", "    \"\"\"\n", "    # replace newlines, which can negatively affect performance.\n", "    docs = [doc.replace(\"\\n\", \" \") for doc in docs]\n", "    response = openai_client.embeddings.create(input=docs, model=model)\n", "    response = [r.embedding for r in response.data]\n", "    return response"]}, {"cell_type": "code", "execution_count": 11, "id": "0da5f1da-f4bd-4551-871e-350d44ed0d31", "metadata": {}, "outputs": [], "source": ["# Generating a test embedding\n", "test_openai_embed = get_embeddings([df.iloc[0][\"text\"]])"]}, {"cell_type": "code", "execution_count": 12, "id": "a3f8cd22-d3e7-45cb-abe1-4993208f1391", "metadata": {}, "outputs": [{"data": {"text/plain": ["3072"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Sanity check to make sure embedding dimensions are as expected i.e. 3072\n", "len(test_openai_embed[0])"]}, {"cell_type": "markdown", "id": "b7020c55", "metadata": {}, "source": ["## Step 6: Evaluation\n"]}, {"cell_type": "markdown", "id": "17d7c15a-8d3e-4680-acf1-a61a5be5c998", "metadata": {}, "source": ["### Measuring embedding latency\n", "\n", "Create a local vector store (list) of embeddings for the entire dataset.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "76e0e043-dea1-4fb7-a779-6aeba0c690e4", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from tqdm.auto import tqdm"]}, {"cell_type": "code", "execution_count": 14, "id": "9e0c475e-8f36-4183-997f-c13b2320b280", "metadata": {}, "outputs": [], "source": ["texts = df[\"text\"].tolist()"]}, {"cell_type": "code", "execution_count": 15, "id": "d793b764-88ec-4bb6-ae71-52dd06791128", "metadata": {}, "outputs": [], "source": ["batch_size = 128"]}, {"cell_type": "code", "execution_count": 16, "id": "501dc5a1-daed-4ae9-a246-b388b0698e22", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c50a272f483742f1b7de610e4e16e9d1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/196 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["embeddings = []\n", "# Generate embeddings in batches\n", "for i in tqdm(range(0, len(texts), batch_size)):\n", "    end = min(len(texts), i + batch_size)\n", "    batch = texts[i:end]\n", "    # Generate embeddings for current batch\n", "    batch_embeddings = get_embeddings(batch)\n", "    # Add to the list of embeddings\n", "    embeddings.extend(np.array(batch_embeddings))"]}, {"cell_type": "markdown", "id": "3918f00e-b31f-4225-80fd-1761fbf3a3d2", "metadata": {}, "source": ["### Measuring retrieval quality\n", "\n", "- Create embedding for the user query\n", "<p>\n", "- Get the top 5 most similar documents from the local vector store using cosine similarity as the similarity metric\n"]}, {"cell_type": "code", "execution_count": 17, "id": "7fa4806b-7311-4516-aea2-a71230c4f571", "metadata": {}, "outputs": [], "source": ["from sentence_transformers.util import cos_sim"]}, {"cell_type": "code", "execution_count": 18, "id": "ff11c827-5e24-481b-af48-8389b9963bda", "metadata": {}, "outputs": [], "source": ["# Converting embeddings list to a Numpy array- required to calculate cosine similarity\n", "embeddings = np.asarray(embeddings)"]}, {"cell_type": "code", "execution_count": 19, "id": "f9d9c773-896b-4098-8234-fe77360820c9", "metadata": {}, "outputs": [], "source": ["def query(query: str, top_k: int = 3) -> None:\n", "    \"\"\"\n", "    Query the local vector store for the top 3 most relevant documents.\n", "\n", "    Args:\n", "        query (str): User query\n", "        top_k (int, optional): Number of documents to return. Defaults to 3.\n", "    \"\"\"\n", "    # Generate embedding for the user query\n", "    query_emb = np.asarray(get_embeddings([query]))\n", "    # Calculate cosine similarity\n", "    scores = cos_sim(query_emb, embeddings)[0]\n", "    # Get indices of the top k records\n", "    idxs = np.argsort(-scores)[:top_k]\n", "\n", "    print(f\"Query: {query}\")\n", "    for idx in idxs:\n", "        print(f\"Score: {scores[idx]:.4f}\")\n", "        print(texts[idx])\n", "        print(\"--------\")"]}, {"cell_type": "code", "execution_count": 20, "id": "ed8ad9ef-67ad-454d-8fa7-65b1e4a35e03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: Give me some tips to improve my mental health.\n", "Score: 0.6206\n", "Key Tips & Guidelines:\n", "\n", "* Follow a balanced diet rich in fruits, vegetables, lean proteins, whole grains, and omega-3 fatty acids.\n", "* Exercise consistently, aiming for at least 30 minutes per day most days of the week.\n", "* Get sufficient sleep (7-9 hours nightly). Develop a bedtime routine and create a conducive sleeping environment.\n", "\n", "Step 4: Cultivate Positive Relationships\n", "Social connections play a vital role in promoting mental health. Building strong, supportive networks encourages open communication, empathy, and validation while combating loneliness and isolation.\n", "\n", "Key Tips & Guidelines:\n", "\n", "* Nurture existing friendships by scheduling regular meetups and engaging in shared interests.\n", "* Seek out new social opportunities through clubs, classes, or volunteering.\n", "* Learn to identify unhealthy relationship patterns and establish boundaries accordingly.\n", "\n", "Step 5: Manage Stress Effectively\n", "Chronic stress can wreak havoc on mental health. Implementing effective coping mechanisms enables individuals to navigate adversities constructively and minimize negative consequences.\n", "--------\n", "Score: 0.6180\n", "Key Tips & Guidelines:\n", "\n", "* Acknowledge that prioritizing mental health is not selfish but necessary for maintaining optimal functioning.\n", "* Accept that everyone experiences ups and downs; seeking support when needed demonstrates strength rather than weakness.\n", "\n", "Step 2: Practice Self-Awareness\n", "Developing mindfulness skills allows individuals to become aware of their thoughts, feelings, and physical sensations without judgment. Being present in the moment fosters self-compassion, promotes relaxation, and enhances problem-solving abilities.\n", "\n", "Key Tips & Guidelines:\n", "\n", "* Set aside time each day for meditation or deep breathing exercises.\n", "* Journal regularly to explore thoughts and emotions objectively.\n", "* Engage in activities (e.g., yoga, tai chi) that encourage both physical movement and introspection.\n", "\n", "Step 3: Establish Healthy Lifestyle Habits\n", "Physical health significantly impacts mental well-being. Adopting healthy habits contributes to improved mood regulation, reduced anxiety symptoms, and enhanced cognitive function.\n", "\n", "Key Tips & Guidelines:\n", "--------\n", "Score: 0.6045\n", "Key Tip: Be honest with yourself about your feelings; acknowledging them is the first step toward addressing them.\n", "\n", "Step 2: Practice Self-Care\n", "Self-care is an essential aspect of maintaining mental wellbeing. Engage in activities that make you feel good about yourself, both physically and emotionally. Here are some self-care ideas:\n", "\n", "* Take a warm bath or shower\n", "* Put on comfortable clothes\n", "* Prepare a nutritious meal\n", "* Exercise (even a short walk can be beneficial)\n", "* Get enough sleep\n", "* Meditate or practice deep breathing exercises\n", "\n", "These activities can boost your mood by releasing endorphins, promoting relaxation, and improving overall health.\n", "\n", "Key Guideline: Make self-care a regular part of your routine, not just something you do when you're feeling low. Consistency is vital for reaping the benefits of self-care.\n", "--------\n"]}], "source": ["query(\"Give me some tips to improve my mental health.\")"]}, {"cell_type": "code", "execution_count": 21, "id": "143fcd7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: Give me some tips for writing good code.\n", "Score: 0.5894\n", "Step 6: Improve Code Quality\n", "Strive for clean, readable, maintainable code. Adopt consistent naming conventions, indentation styles, and formatting rules. Utilize version control systems like Git to track changes and collaborate effectively. Leverage linters and static analyzers to enforce style guides automatically. Document your work using comments and dedicated documentation tools. High-quality code facilitates collaboration, promotes longevity, and simplifies troubleshooting.\n", "\n", "Step 7: <PERSON><PERSON><PERSON> Best Practices\n", "Follow established best practices relevant to your chosen language and domain. Examples include Object-Oriented Design Principles, SOLID principles, Test-Driven Development (TDD), Dependency Injection, Asynchronous Programming, etc. While seemingly overwhelming initially, integrating them gradually enhances design patterns, scalability, and extensibility. Consult authoritative blogs, books, and articles to stay updated on current trends and recommendations.\n", "--------\n", "Score: 0.4585\n", "Conclusion:\n", "Becoming a good programmer requires dedication, persistence, and patience. By methodically progressing through these steps, mastering core concepts, practicing diligently, engaging with peers, and committing to continuous improvement, you'll be well on your way to achieving your goal. Remember, every expert was once a beginner - keep pushing forward!\n", "--------\n", "Score: 0.4463\n", "Step 4: Work On Real Projects\n", "Theory alone won't make you a proficient programmer; hands-on experience does. Apply what you've learned by working on real projects. Begin with small tasks like creating calculators, text-based games, or automating repetitive tasks. Gradually move towards larger projects related to your field of interest. Participate in open-source initiatives, contribute to existing repositories, or develop your application ideas. These experiences enhance problem-solving skills and prepare you for professional challenges.\n", "\n", "Step 5: Master Debugging Techniques\n", "Debugging is inevitable when writing code. Identifying and resolving errors early improves productivity and reduces frustration. Use debuggers provided within IDEs, print statements, or log messages to trace execution flow and pinpoint issues. Familiarize yourself with common error types and causes, especially those specific to your chosen language. Over time, proactive debugging becomes second nature.\n", "--------\n"]}], "source": ["query_emb = query(\"Give me some tips for writing good code.\")"]}, {"cell_type": "code", "execution_count": 22, "id": "6fd44daa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: How do I create a basic webpage?\n", "Score: 0.6989\n", "Creating a webpage with basic HTML is an exciting endeavor that allows you to share information, showcase your creativity, or build the foundation for a more complex website. This comprehensive guide will walk you through each step required to create a simple yet effective static webpage using HTML (HyperText Markup Language), the standard language used to structure content on the World Wide Web.\n", "\n", "**Step 1: <PERSON><PERSON> a Text Editor**\n", "\n", "Before diving into coding, select a suitable text editor for writing your HTML code. Popular choices include Notepad++ (Windows), Visual Studio Code (cross-platform), Atom (cross-platform), and Sublime Text (cross-platform). These editors offer features such as syntax highlighting, auto-completion, and error detection, making them ideal for beginners and professionals alike.\n", "\n", "Key tip: Avoid using word processors like Microsoft Word or Google Docs, as they may add unnecessary formatting codes that can interfere with your HTML.\n", "\n", "**Step 2: Create an HTML Document**\n", "--------\n", "Score: 0.6681\n", "Creating a simple webpage with HTML\n", "\n", "Step 1: Understand What HTML Is\n", "HTML (HyperText Markup Language) is the standard markup language used to create web pages. It provides the structure of a webpage, while CSS (Cascading Style Sheets) and JavaScript handle the presentation and functionality respectively.\n", "\n", "Key Tips and Guidelines:\n", "\n", "* Familiarize yourself with basic HTML tags such as `<html>`, `<head>`, `<body>`, `<h1>` through `<h6>`, `<p>`, `<a>`, `<img>`, `<div>`, and `<span>`.\n", "* Always start an HTML document with a doctype declaration, which informs the browser about the version of HTML being used. For example: `<!DOCTYPE html>`.\n", "--------\n", "Score: 0.5845\n", "**Step 2: Create an HTML Document**\n", "\n", "In your chosen text editor, create a new file and save it with the \".html\" extension, e.g., \"mypage.html\". By convention, HTML files have this extension, signaling to web browsers that the document contains HTML markup.\n", "\n", "Guideline: Use lowercase letters for naming your HTML files; some servers are case sensitive, and consistency ensures proper loading of resources.\n", "\n", "**Step 3: Define the DOCTYPE Declaration**\n", "\n", "At the top of your HTML file, insert the following line to declare the Document Type Definition (DOCTYPE):\n", "```python\n", "<!DOCTYPE html>\n", "```\n", "This declaration informs web browsers which version of HTML the page uses, enabling consistent rendering across different platforms. Although optional in modern HTML5 documents, including it is still considered good practice.\n", "\n", "**Step 4: Set Up the HTML Structure**\n", "--------\n"]}], "source": ["query(\"How do I create a basic webpage?\")"]}, {"cell_type": "code", "execution_count": 23, "id": "ce25fee3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: What are some environment-friendly practices I can incorporate in everyday life?\n", "Score: 0.6416\n", "* **Carry Reusable Bags**: Keep cloth or other reusable shopping bags handy for grocery trips and errands. Store extra bags in your car, purse, or backpack for convenience.\n", "* **Choose Sustainable Alternatives**: Opt for reusable water bottles, coffee cups, straws, and cutlery made from eco-friendly materials like stainless steel, glass, silicone, or bamboo.\n", "* **Support Businesses that Minimize Packaging**: Patronize establishments that prioritize sustainable practices, such as using biodegradable packaging or offering package-free options.\n", "\n", "Step 3: Conserve Energy and Water\n", "Explanation: Efficient energy and water usage help decrease reliance on nonrenewable resources and lower greenhouse gas emissions.\n", "--------\n", "Score: 0.6297\n", "* Use public transportation, carpool, bike, or walk instead of driving alone.\n", "* Switch to energy-efficient appliances and light bulbs.\n", "* Unplug electronics when they're not in use.\n", "* Install solar panels on your home.\n", "* Insulate your home to reduce heating and cooling costs.\n", "* Eat less meat and consume locally sourced, organic foods.\n", "* Plant trees and maintain a garden.\n", "\n", "These actions decrease greenhouse gas emissions, slow global warming, and promote sustainability.\n", "\n", "Step 2: Conserve Water\n", "Freshwater scarcity is becoming an increasingly severe issue worldwide. To conserve water:\n", "\n", "* Fix leaks promptly.\n", "* Take shorter showers and turn off the faucet while brushing your teeth.\n", "* Install low-flow toilets and showerheads.\n", "* Collect rainwater for irrigation.\n", "* Only run full loads in washing machines and dishwashers.\n", "\n", "Water conservation efforts protect ecosystems, ensure food security, and alleviate stress on water infrastructure.\n", "--------\n", "Score: 0.6284\n", "Step 6: Conserve Water\n", "Install low-flow faucets and showerheads, fix leaks promptly, and turn off taps when not in use. Collect rainwater for gardening. Key tip: Limit showers to five minutes or less and wash full loads of laundry. Guideline: Check local regulations regarding rainwater collection systems.\n", "\n", "Step 7: Compost Kitchen Scraps\n", "Composting reduces landfill waste and enriches soil. Set up a compost bin indoors or outdoors. Key tip: Avoid adding meat, dairy, grease, or diseased plants to prevent pests and odor issues. Guideline: Turn compost regularly to speed decomposition.\n", "\n", "Step 8: Shop Locally\n", "Support farmers markets, community gardens, and CSAs (Community Supported Agriculture) to reduce transportation emissions and promote local economies. Key tip: Bring reusable bags and cash. Guideline: Visit multiple vendors to diversify your purchases.\n", "--------\n"]}], "source": ["query(\n", "    \"What are some environment-friendly practices I can incorporate in everyday life?\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "conda_pytorch_p310", "language": "python", "name": "conda_pytorch_p310"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 5}