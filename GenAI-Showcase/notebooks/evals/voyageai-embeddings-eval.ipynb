{"cells": [{"cell_type": "markdown", "id": "eb32f974", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/voyageai-embeddings-eval.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/choose-embedding-model-rag/?utm_campaign=devrel&utm_source=cross-post&utm_medium=organic_social&utm_content=https%3A%2F%2Fgithub.com%2Fmongodb-developer%2FGenAI-Showcase&utm_term=apoorva.joshi)"]}, {"cell_type": "markdown", "id": "39b4d49e-31a1-4093-9255-9cb8e6f96b0d", "metadata": {"tags": []}, "source": ["# RAG Series Part 1: How to choose the right embedding model for your RAG application\n", "\n", "This notebook evaluates the [voyage-lite-02-instruct](https://docs.voyageai.com/embeddings/) model.\n"]}, {"cell_type": "markdown", "id": "f3a115b9-68e5-44f7-9ea7-fff56bc9ee59", "metadata": {}, "source": ["## Step 1: Install required libraries\n", "\n", "- **datasets**: Python library to get access to datasets available on Hugging Face Hub\n", "<p>\n", "- **voyageai**: Python library to interact with Voyage AI APIs\n", "<p>\n", "- **sentence-transformers**: Framework for working with text and image embeddings\n", "<p>\n", "- **numpy**: Python library that provides tools to perform mathematical operations on arrays\n", "<p>\n", "- **pandas**: Python library for data analysis, exploration and manipulation\n", "<p>\n", "- **tdqm**: Python module to show a progress meter for loops\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a999fe13-3eee-4fd8-a9fd-0f2f37171ed3", "metadata": {"tags": []}, "outputs": [], "source": ["! pip install -qU datasets voyageai sentence-transformers numpy pandas tqdm"]}, {"cell_type": "markdown", "id": "87bd8b3e-984b-4dff-bd7f-615e577a9ef8", "metadata": {}, "source": ["## Step 2: Setup pre-requisites\n", "\n", "Set Voyage API key as environment variable, and initialize the Voyage AI client.\n", "\n", "Steps to obtain a Voyage AI API Key can be found [here](https://docs.voyageai.com/docs/api-key-and-installation).\n"]}, {"cell_type": "code", "execution_count": 4, "id": "f62e40d3-852c-4abf-9151-875a1d32e93e", "metadata": {}, "outputs": [], "source": ["import getpass\n", "\n", "import voyageai"]}, {"cell_type": "code", "execution_count": 5, "id": "a8e8bcde-c242-4641-a7c8-5f69c60d021e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Voyage API Key:········\n"]}], "source": ["VOYAGE_API_KEY = getpass.getpass(\"Voyage API Key:\")\n", "voyage_client = voyageai.Client(api_key=VOYAGE_API_KEY)"]}, {"cell_type": "markdown", "id": "b5a99a68-a7d2-4657-8f05-ea75f19b6748", "metadata": {}, "source": ["## Step 3: Download the evaluation dataset\n", "\n", "We will use MongoDB's [cosmopedia-wikihow-chunked](https://huggingface.co/datasets/MongoDB/cosmopedia-wikihow-chunked) dataset, which has chunked versions of WikiHow articles from the [Cosmopedia](https://huggingface.co/datasets/HuggingFaceTB/cosmopedia) dataset released by Hugging Face. The dataset is pretty large, so we will only grab the first 25k records for testing.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "7862e2db-fec8-4294-ad75-9753e69adc1a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datasets import load_dataset\n", "\n", "# Use streaming=True to load the dataset without downloading it fully\n", "data = load_dataset(\"MongoDB/cosmopedia-wikihow-chunked\", split=\"train\", streaming=True)\n", "# Get first 25k records from the dataset\n", "data_head = data.take(25000)\n", "df = pd.DataFrame(data_head)\n", "\n", "# Use this if you want the full dataset\n", "# data = load_dataset(\"AIatMongoDB/cosmopedia-wikihow-chunked\", split=\"train\")\n", "# df = pd.DataFrame(data)"]}, {"cell_type": "markdown", "id": "70d329bc-cdb7-4651-bef0-8d2ae09d9e4b", "metadata": {}, "source": ["## Step 4: Data analysis\n", "\n", "Make sure the length of the dataset is what we expect (25k), preview the data, drop Nones etc.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "39c0f32d-c6f7-4faa-92e1-fae25e9eb2ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["25000"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Ensuring length of dataset is what we expect i.e. 25k\n", "len(df)"]}, {"cell_type": "code", "execution_count": 8, "id": "6782ab49-3d9d-4f67-8b33-474f02b7e993", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>doc_id</th>\n", "      <th>chunk_id</th>\n", "      <th>text_token_length</th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>180</td>\n", "      <td>Title: How to Create and Maintain a Compost Pi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>141</td>\n", "      <td>**Step 2: Gather Materials**\\nGather brown (ca...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>182</td>\n", "      <td>_Key guideline:_ For every volume of green mat...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>188</td>\n", "      <td>_Key tip:_ Chop large items like branches and ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>157</td>\n", "      <td>**Step 7: Maturation and Use**\\nAfter 3-4 mont...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   doc_id  chunk_id  text_token_length  \\\n", "0       0         0                180   \n", "1       0         1                141   \n", "2       0         2                182   \n", "3       0         3                188   \n", "4       0         4                157   \n", "\n", "                                                text  \n", "0  Title: How to Create and Maintain a Compost Pi...  \n", "1  **Step 2: Gather Materials**\\nGather brown (ca...  \n", "2  _Key guideline:_ For every volume of green mat...  \n", "3  _Key tip:_ Chop large items like branches and ...  \n", "4  **Step 7: Maturation and Use**\\nAfter 3-4 mont...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Previewing the contents of the data\n", "df.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "04563eaf-bbd8-4969-9671-eb5312817402", "metadata": {}, "outputs": [], "source": ["# Only keep records where the text field is not null\n", "df = df[df[\"text\"].notna()]"]}, {"cell_type": "code", "execution_count": 10, "id": "cd5a91c3-2f68-4157-a747-05bbc934d53a", "metadata": {}, "outputs": [{"data": {"text/plain": ["4335"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Number of unique documents in the dataset\n", "df.doc_id.nunique()"]}, {"cell_type": "markdown", "id": "0400259f-65ca-4301-a245-7af0b746abf1", "metadata": {}, "source": ["## Step 5: Creating embeddings\n", "\n", "Define the embedding function, and run a quick test.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "3d936743-f18b-410e-8397-c0acf9c61a5e", "metadata": {}, "outputs": [], "source": ["from typing import List"]}, {"cell_type": "code", "execution_count": 12, "id": "bda20d74-7296-40df-ab19-ea63a5b47e6d", "metadata": {}, "outputs": [], "source": ["def get_embeddings(\n", "    docs: List[str], input_type: str, model: str = \"voyage-lite-02-instruct\"\n", ") -> List[List[float]]:\n", "    \"\"\"\n", "    Get embeddings using the Voyage AI API.\n", "\n", "    Args:\n", "        docs (List[str]): List of texts to embed\n", "        input_type (str): Type of input to embed. Can be \"document\" or \"query\".\n", "        model (str, optional): Model name. Defaults to \"voyage-lite-02-instruct\".\n", "\n", "    Returns:\n", "        List[List[float]]: Array of embedddings\n", "    \"\"\"\n", "    response = voyage_client.embed(docs, model=model, input_type=input_type)\n", "    return response.embeddings"]}, {"cell_type": "code", "execution_count": 13, "id": "0da5f1da-f4bd-4551-871e-350d44ed0d31", "metadata": {}, "outputs": [], "source": ["# Generating a test embedding\n", "test_voyageai_embed = get_embeddings([df.iloc[0][\"text\"]], \"document\")"]}, {"cell_type": "code", "execution_count": 14, "id": "a3f8cd22-d3e7-45cb-abe1-4993208f1391", "metadata": {}, "outputs": [{"data": {"text/plain": ["1024"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Sanity check to make sure embedding dimensions are as expected i.e. 1024\n", "len(test_voyageai_embed[0])"]}, {"cell_type": "markdown", "id": "17d7c15a-8d3e-4680-acf1-a61a5be5c998", "metadata": {}, "source": ["## Step 6: Evaluation\n"]}, {"cell_type": "markdown", "id": "62b14e37", "metadata": {}, "source": ["### Measuring embedding latency\n", "\n", "Create a local vector store (list) of embeddings for the entire dataset.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "76e0e043-dea1-4fb7-a779-6aeba0c690e4", "metadata": {}, "outputs": [], "source": ["from tqdm.auto import tqdm"]}, {"cell_type": "code", "execution_count": 16, "id": "9e0c475e-8f36-4183-997f-c13b2320b280", "metadata": {}, "outputs": [], "source": ["texts = df[\"text\"].tolist()"]}, {"cell_type": "code", "execution_count": 17, "id": "d793b764-88ec-4bb6-ae71-52dd06791128", "metadata": {}, "outputs": [], "source": ["batch_size = 128"]}, {"cell_type": "code", "execution_count": 18, "id": "501dc5a1-daed-4ae9-a246-b388b0698e22", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4175df1521f447b89cd382c14bffe054", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/196 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["embeddings = []\n", "# Generate embeddings in batches\n", "for i in tqdm(range(0, len(texts), batch_size)):\n", "    end = min(len(texts), i + batch_size)\n", "    batch = texts[i:end]\n", "    # Generate embeddings for current batch\n", "    batch_embeddings = get_embeddings(batch, \"document\")\n", "    # Add to the list of embeddings\n", "    embeddings.extend(batch_embeddings)"]}, {"cell_type": "markdown", "id": "3918f00e-b31f-4225-80fd-1761fbf3a3d2", "metadata": {}, "source": ["### Measuring retrieval quality\n", "\n", "- Create embedding for the user query\n", "<p>\n", "- Get the top 5 most similar documents from the local vector store using cosine similarity as the similarity metric\n"]}, {"cell_type": "code", "execution_count": 19, "id": "7fa4806b-7311-4516-aea2-a71230c4f571", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from sentence_transformers.util import cos_sim"]}, {"cell_type": "code", "execution_count": 20, "id": "ff11c827-5e24-481b-af48-8389b9963bda", "metadata": {}, "outputs": [], "source": ["# Converting embeddings list to a Numpy array- required to calculate cosine similarity\n", "embeddings = np.asarray(embeddings)"]}, {"cell_type": "code", "execution_count": 21, "id": "f9d9c773-896b-4098-8234-fe77360820c9", "metadata": {}, "outputs": [], "source": ["def query(query: str, top_k: int = 3) -> None:\n", "    \"\"\"\n", "    Query the local vector store for the top 3 most relevant documents.\n", "\n", "    Args:\n", "        query (str): User query\n", "        top_k (int, optional): Number of documents to return. Defaults to 3.\n", "    \"\"\"\n", "    # Generate embedding for the user query\n", "    query_emb = np.asarray(get_embeddings([query], \"query\"))\n", "    # Calculate cosine similarity\n", "    scores = cos_sim(query_emb, embeddings)[0]\n", "    # Get indices of the top k records\n", "    idxs = np.argsort(-scores)[:top_k]\n", "\n", "    print(f\"Query: {query}\")\n", "    for idx in idxs:\n", "        print(f\"Score: {scores[idx]:.4f}\")\n", "        print(texts[idx])\n", "        print(\"--------\")"]}, {"cell_type": "code", "execution_count": 22, "id": "ed8ad9ef-67ad-454d-8fa7-65b1e4a35e03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: Give me some tips to improve my mental health.\n", "Score: 0.9247\n", "*Explanation:* Regularly assess symptom improvement and reassess treatment efficacy. Modifications to existing plans might be required as circumstances change.\n", "\n", "Recommendations:\n", "\n", "* Keep track of mood fluctuations, thoughts, and behaviors using mobile apps, journals, or calendars.\n", "* Share progress updates with therapists and other members of the support network.\n", "* Discuss any challenges encountered while implementing recommended strategies.\n", "\n", "Conclusion\n", "==========\n", "\n", "Managing suicidal thoughts necessitates a multifaceted approach encompassing various methods and tools. By following this comprehensive guide, individuals grappling with these difficult emotions can access valuable resources, cultivate essential skills, and foster connections vital to overcoming adversity.\n", "--------\n", "Score: 0.9243\n", "Key Tips:\n", "\n", "* Learn to recognize early signs of stress and address them proactively.\n", "* Share concerns with trusted friends, family members, or mental health professionals.\n", "* Develop a list of coping mechanisms to deploy during high-stress periods.\n", "\n", "Step 6: Cultivate Social Connections\n", "Isolation can worsen depression. Nurture relationships with loved ones and participate in social events to foster a sense of belonging.\n", "\n", "* Join clubs, groups, or communities centered around shared interests.\n", "* Schedule regular phone calls or video chats with distant friends and relatives.\n", "* Volunteer for causes close to your heart.\n", "\n", "Key Guidelines:\n", "\n", "* Set boundaries when necessary to protect your emotional well-being.\n", "* Communicate openly about your struggles with trusted confidants.\n", "* Seek professional guidance if social anxiety impedes relationship development.\n", "--------\n", "Score: 0.9239\n", "Key Tips & Guidelines:\n", "\n", "* Acknowledge that prioritizing mental health is not selfish but necessary for maintaining optimal functioning.\n", "* Accept that everyone experiences ups and downs; seeking support when needed demonstrates strength rather than weakness.\n", "\n", "Step 2: Practice Self-Awareness\n", "Developing mindfulness skills allows individuals to become aware of their thoughts, feelings, and physical sensations without judgment. Being present in the moment fosters self-compassion, promotes relaxation, and enhances problem-solving abilities.\n", "\n", "Key Tips & Guidelines:\n", "\n", "* Set aside time each day for meditation or deep breathing exercises.\n", "* Journal regularly to explore thoughts and emotions objectively.\n", "* Engage in activities (e.g., yoga, tai chi) that encourage both physical movement and introspection.\n", "\n", "Step 3: Establish Healthy Lifestyle Habits\n", "Physical health significantly impacts mental well-being. Adopting healthy habits contributes to improved mood regulation, reduced anxiety symptoms, and enhanced cognitive function.\n", "\n", "Key Tips & Guidelines:\n", "--------\n"]}], "source": ["query(\"Give me some tips to improve my mental health.\")"]}, {"cell_type": "code", "execution_count": 23, "id": "e0e9e8dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: Give me some tips for writing good code.\n", "Score: 0.9177\n", "Step 6: Improve Code Quality\n", "Strive for clean, readable, maintainable code. Adopt consistent naming conventions, indentation styles, and formatting rules. Utilize version control systems like Git to track changes and collaborate effectively. Leverage linters and static analyzers to enforce style guides automatically. Document your work using comments and dedicated documentation tools. High-quality code facilitates collaboration, promotes longevity, and simplifies troubleshooting.\n", "\n", "Step 7: <PERSON><PERSON><PERSON> Best Practices\n", "Follow established best practices relevant to your chosen language and domain. Examples include Object-Oriented Design Principles, SOLID principles, Test-Driven Development (TDD), Dependency Injection, Asynchronous Programming, etc. While seemingly overwhelming initially, integrating them gradually enhances design patterns, scalability, and extensibility. Consult authoritative blogs, books, and articles to stay updated on current trends and recommendations.\n", "--------\n", "Score: 0.8941\n", "---\n", "\n", "## **8. Implementing Gameplay: Coding Core Features**\n", "\n", "Code primary gameplay mechanics, such as combat, inventory management, dialogue trees, quest systems, and AI behaviors. <PERSON><PERSON><PERSON> test individual components before integrating them into larger systems. Debug issues promptly and iteratively refine code to improve stability and efficiency.\n", "\n", "*Key Best Practices:*\n", "\n", "- Modularize code for easier maintenance and reusability.\n", "- Employ version control systems like Git to track changes and collaborate effectively.\n", "- Apply object-oriented principles to structure game logic and data.\n", "\n", "---\n", "\n", "## **9. Testing and Iterating: Refine Your Game Through Playtesting**\n", "\n", "Playtest early and often to identify bugs, imbalances, and areas requiring improvement. Solicit constructive criticism from friends, family members, and fellow developers. Analyze playthrough recordings to pinpoint problematic patterns or sequences. Address reported issues systematically, prioritizing high-impact fixes and enhancements.\n", "\n", "*Key Strategies:*\n", "--------\n", "Score: 0.8936\n", "Key Tips & Guidelines\n", "\n", "* Monitor social media channels and newsletters related to software testing for up-to-date information.\n", "* Engage in professional networks and forums to learn from experienced testers and share best practices.\n", "* Periodically reevaluate your toolset and methodologies to incorporate improvements.\n", "\n", "By diligently applying these steps, you'll develop the necessary expertise to excel as a glitch hunter, contributing meaningfully to software quality assurance efforts.\n", "--------\n"]}], "source": ["query(\"Give me some tips for writing good code.\")"]}, {"cell_type": "code", "execution_count": 24, "id": "087fed97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: How to create a basic webpage?\n", "Score: 0.9276\n", "Creating a simple webpage with HTML\n", "\n", "Step 1: Understand What HTML Is\n", "HTML (HyperText Markup Language) is the standard markup language used to create web pages. It provides the structure of a webpage, while CSS (Cascading Style Sheets) and JavaScript handle the presentation and functionality respectively.\n", "\n", "Key Tips and Guidelines:\n", "\n", "* Familiarize yourself with basic HTML tags such as `<html>`, `<head>`, `<body>`, `<h1>` through `<h6>`, `<p>`, `<a>`, `<img>`, `<div>`, and `<span>`.\n", "* Always start an HTML document with a doctype declaration, which informs the browser about the version of HTML being used. For example: `<!DOCTYPE html>`.\n", "--------\n", "Score: 0.9244\n", "Creating a webpage with basic HTML is an exciting endeavor that allows you to share information, showcase your creativity, or build the foundation for a more complex website. This comprehensive guide will walk you through each step required to create a simple yet effective static webpage using HTML (HyperText Markup Language), the standard language used to structure content on the World Wide Web.\n", "\n", "**Step 1: <PERSON><PERSON> a Text Editor**\n", "\n", "Before diving into coding, select a suitable text editor for writing your HTML code. Popular choices include Notepad++ (Windows), Visual Studio Code (cross-platform), Atom (cross-platform), and Sublime Text (cross-platform). These editors offer features such as syntax highlighting, auto-completion, and error detection, making them ideal for beginners and professionals alike.\n", "\n", "Key tip: Avoid using word processors like Microsoft Word or Google Docs, as they may add unnecessary formatting codes that can interfere with your HTML.\n", "\n", "**Step 2: Create an HTML Document**\n", "--------\n", "Score: 0.9145\n", "Step 2: Navigate to Site Builder\n", "a) From the dashboard, locate the 'Sites' section.\n", "b) Click on 'Create A New Site', then select 'Website'.\n", "c) Choose a template from the available options; this does not need to be complex as we are only creating a guestbook.\n", "d) Name your site (e.g., MyGuestBook), then hit 'Continue'.\n", "\n", "These actions enable creation of a basic website which serves as the foundation for your guestbook. Selecting a simple template keeps things manageable while ensuring visual appeal.\n", "\n", "Step 3: Design Your Page\n", "a) Customize colors, fonts, and layout under 'Design'.\n", "b) Delete unnecessary elements like text boxes, photos, etc. via right-click > delete.\n", "c) Keep design minimalistic yet inviting, focusing primarily on inserting the actual guestbook component.\n", "--------\n"]}], "source": ["query(\"How to create a basic webpage?\")"]}, {"cell_type": "code", "execution_count": 25, "id": "e48d2fa4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: What are some environment-friendly practices I can incorporate in everyday life?\n", "Score: 0.9214\n", "### Step 5: Embrace Resource Conservation\n", "\n", "Another critical aspect of Jawa culture revolves around conserving resources due to limited availability on their home planet. Implement sustainable practices such as reducing waste, reusing materials, and recycling whenever possible.\n", "\n", "Key Tips\n", "--------\n", "\n", "* Monitor daily consumption habits and identify areas where reductions could occur (water usage, electricity, paper products, etc.).\n", "* Repair damaged goods instead of purchasing replacements whenever feasible.\n", "* Participate in local environmental initiatives focused on preserving natural resources.\n", "\n", "Why It Matters\n", "---------------\n", "\n", "Adopting eco-friendly attitudes and actions contributes positively to both individual wellbeing and global sustainability efforts. Moreover, practicing conservation aligns closely with Jawa values, further deepening your connection to the character.\n", "\n", "Conclusion\n", "==========\n", "--------\n", "Score: 0.9172\n", "Step 9: Recycle Properly\n", "Familiarize yourself with local recycling programs and sort materials accordingly. Rinse containers and remove caps if necessary. Key tip: Never place non-recyclable items in bins. Guideline: Educate family members on proper recycling techniques.\n", "\n", "Step 10: Green Transportation Options\n", "Choose walking, cycling, public transit, carpooling, or electric vehicles over gasoline-powered cars. Combine errands to minimize trips. Key tip: Schedule regular vehicle maintenance checks to maximize efficiency. Guideline: Investigate incentives for green transportation options in your area.\n", "\n", "Step 11: Energy Efficiency Upgrades\n", "Replace incandescent bulbs with LED lights, install programmable thermostats, seal drafts, and insulate attics. Consider solar panels or wind turbines. Key tip: Perform energy audits annually. Guideline: Prioritize high-impact areas first (e.g., windows, heating/cooling systems).\n", "--------\n", "Score: 0.9169\n", "By consistently implementing these steps, every individual can actively contribute to helping the world become a cleaner, greener, and more resilient place for future generations.\n", "--------\n"]}], "source": ["query(\n", "    \"What are some environment-friendly practices I can incorporate in everyday life?\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "conda_pytorch_p310", "language": "python", "name": "conda_pytorch_p310"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 5}