{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# The Technical Guide on RAG Evaluation with Patronus and MongoDB\n", "\n", "## How to Query and Retrieve Results from Atlas Vector Store\n", "\n", "To query and retrieve results from MongoDB Atlas vector store, follow these three steps:\n", "\n", "### Set Up the Database on Atlas\n", "First, you need to create an account on MongoDB Atlas. This involves signing in to your MongoDB Atlas account, creating a new cluster, and adding a database and collection. You can skip this step if you have already have your collection for vector search.\n", "\n", "### Create an Atlas Index\n", "You can create an index either via code or using the Atlas UI. Here’s an example of how to create an index using the Atlas UI:\n", "1. Navigate to your collection.\n", "2. <PERSON><PERSON> on “Atlas search” and then “Create Index”.\n", "3. Define the index fields and type. \n", "\n", "Alternatively, you can create an index programmatically. The following index definition indexes the vector embeddings field (`fieldToIndex`) for performing vector search.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "import pymongo\n", "from pymongo.mongo_client import MongoClient\n", "from pymongo.operations import SearchIndexModel\n", "\n", "# Connect to your Atlas deployment\n", "uri = \"<connectionString>\"\n", "client = MongoClient(uri)\n", "\n", "# Access your database and collection\n", "database = client[\"<databaseName>\"]\n", "collection = database[\"<collectionName>\"]\n", "\n", "# Create your index model, then create the search index\n", "search_index_model = SearchIndexModel(\n", "  definition={\n", "    \"fields\": [\n", "      {\n", "        \"type\": \"vector\",\n", "        \"numDimensions\": <numberofDimensions>,\n", "        \"path\": \"<fieldToIndex>\",\n", "        \"similarity\": \"euclidean | cosine | dotProduct\"\n", "      },\n", "      {\n", "        \"type\": \"filter\",\n", "        \"path\": \"<fieldToIndex>\"\n", "      },\n", "      # Add more fields as needed\n", "    ]\n", "  },\n", "  name=\"<index name>\",\n", "  type=\"vectorSearch\",\n", ")\n", "\n", "result = collection.create_search_index(model=search_index_model)\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Perform a Vector Query with Code\n", "\n", "After setting up your database and creating the necessary indexes, you can perform a vector query. Below is a sample Python code using the PyMongo library:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "import pymongo\n", "\n", "# connect to your Atlas cluster\n", "client = pymongo.MongoClient(\"<connection-string>\")\n", "\n", "# define pipeline\n", "pipeline = [\n", "  {\n", "    '$vectorSearch': {\n", "      'index': \"<index-name>\", \n", "      'path':  \"<field-to-search>\",\n", "      'queryVector': [<array-of-numbers>],\n", "      'numCandidates': <number-of-candidates>,\n", "      'limit': <number-of-results>\n", "    }\n", "  }\n", "]\n", "\n", "# run pipeline\n", "result = client[\"db_name\"][\"collection_name\"].aggregate(pipeline)\n", "\n", "# print results\n", "for i in result:\n", "    print(i)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to Choose the Right Model\n", "\n", "Patronus provides two versions of the Lynx model:\n", "\n", "## Using the Large Model\n", "\n", "The Lynx-70B model requires significant computational resources. Ensure you have enough memory and computing power to handle it. You would require an instance with an A100 or H100 GPU. We would use vLLM to run the model on GPU and get an endpoint for our evaluation process.\n", "\n", "## Using Smaller Model\n", "\n", "If the 70B model is too large for your use case, consider using a smaller variant of the Lynx model (Lynx-8B-Instruct). This can be executed on a local system using Ollama. You can find different sizes on the Hugging Face model hub under the PatronusAI repository.\n", "\n", "# How to Download the Model from Hugging Face\n", "\n", "Follow these steps to download the Lynx model from Hugging Face. This involves setting up the environment and the Hugging Face CLI, logging into Hugging Face, and then downloading the model.\n", "\n", "## Step 1: Install the Hugging Face Hub CLI\n", "\n", "First, you need to install the Hugging Face Hub CLI. This lets you interact directly with Hugging Face’s model hub from the command line."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["pip3 install -U huggingface_hub[cli]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Log In to Hugging Face\n", "\n", "Next, log in to your Hugging Face account. If you don’t have an account, you’ll need to create one at Hugging Face."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["huggingface-cli login"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You will be prompted to enter your Hugging Face token. You can find your token in your Hugging Face account settings under “Access Tokens.”\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Download the Lynx Model\n", "\n", "After logging in, you can download the Lynx model. Here is an example command to download the 70B variant of the Lynx model:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["huggingface-cli download PatronusAI/Patronus-Lynx-8B-Instruct --local-dir Patronus_8B\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This command will download the model to a local directory named Patronus_8B.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to Deploy Lynx onto the Server Using vLLM\n", "\n", "With the vLLM inference server running, you will obtain a URI (for instance, http://localhost:5123/). You can use the OpenAI API specification to send requests and evaluate the faithfulness of AI-generated responses. This section covers sending a cURL request to test the server and implementing a structured prompt template for hallucination detection.\n", "\n", "## Step 1: Create a New Conda Environment and Install vLLM\n", "\n", "Creating a dedicated conda environment helps manage dependencies and avoid conflicts. Here’s how to set up a new environment with Python 3.10:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["# (Recommended) Create a new conda environment.\n", "conda create -n myenv python=3.10 -y\n", "# Activate the newly created environment.\n", "conda activate myenv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Install vLLM, a library designed to efficiently serve large language models. If you have CUDA 12.1 installed, you can install vLLM with CUDA support for better performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["# Install vLLM with CUDA 12.1.\n", "pip install vllm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Step 2: Run the Lynx Model on a Server\n", "\n", "Once vLLM is installed, you can start the server to host the Lynx model. This involves specifying the port, model, and tokenizer. The following command runs the model on port 5123:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["python -m vllm.entrypoints.openai.api_server --port 5123 --model PatronusAI/Patronus-Lynx-8B-Instruct --tokenizer meta-llama/Meta-Llama-3-8B"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to Catch Hallucinations in Atlas-based RAG System Using Local Lynx API\n", "\n", "With the vLLM inference server running on http://localhost:5123/, you can use the OpenAI API specification to send requests and evaluate the faithfulness of AI-generated responses. This section covers sending a cURL request to test the server and implementing a structured prompt template for hallucination detection.\n", "\n", "## Step 1: Test the Server with a cURL Request\n", "\n", "Verify that the server is working by sending a cURL request. This request queries the model to define what a hallucination is:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["curl http://localhost:5123/v1/chat/completions \\\n", "  -H \"Content-Type: application/json\" \\\n", "  -d '{\n", "  \"model\": \"PatronusAI/Patronus-Lynx-70B-Instruct\",\n", "  \"messages\": [\n", "    {\"role\": \"user\", \"content\": \"What is a hallucination?\"}\n", "  ]\n", "}'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Define the Prompt Template\n", "\n", "Use a structured prompt template to evaluate the faithfulness of AI-generated responses. The template helps ensure that the answer is faithful to the document provided and does not contain hallucinations."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["# Prompt template\n", "Given the following QUESTION, DOCUMENT and ANSWER you must analyze the provided answer and determine whether it is faithful to the contents of the DOCUMENT.\n", "\n", "The ANSWER must not offer new information beyond the context provided in the DOCUMENT.\n", "\n", "The ANSWER also must not contradict information provided in the DOCUMENT.\n", "\n", "Output your final score by strictly following this format: \"PASS\" if the answer is faithful to the DOCUMENT and \"FAIL\" if the answer is not faithful to the DOCUMENT.\n", "\n", "Show your reasoning.\n", "\n", "--\n", "QUESTION (THIS DOES NOT COUNT AS BA<PERSON><PERSON>GROUND INFORMATION):\n", "{{ user_input }}\n", "\n", "--\n", "DOCUMENT:\n", "{{ provided_context }}\n", "\n", "--\n", "ANSWER:\n", "{{ bot_response }}\n", "\n", "--\n", "\n", "Your output should be in JSON FORMAT with the keys \"REASONING\" and \"SCORE\".\n", "\n", "Ensure that the JSON is valid and properly formatted.\n", "\n", "{\"REASONING\": [\"<your reasoning as bullet points>\"], \"SCORE\": \"<final score>\"}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Implement the Evaluation Function\n", "\n", "Use Python to send a structured request to the local Lynx API, including the question, document, and answer. The following code demonstrates how to format the request and handle the response:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "# Define the endpoint URL\n", "url = \"http://localhost:5123/v1/chat/completions\"\n", "\n", "# Define the prompt template\n", "prompt_template = \"\"\"\n", "Given the following QUESTION, DOCUMENT and ANSWER you must analyze the provided answer and determine whether it is faithful to the contents of the DOCUMENT.\n", "\n", "The ANSWER must not offer new information beyond the context provided in the DOCUMENT.\n", "\n", "The ANSWER also must not contradict information provided in the DOCUMENT.\n", "\n", "Output your final score by strictly following this format: \"PASS\" if the answer is faithful to the DOCUMENT and \"FAIL\" if the answer is not faithful to the DOCUMENT.\n", "\n", "Show your reasoning.\n", "\n", "--\n", "QUESTION (THIS DOES NOT COUNT AS BA<PERSON><PERSON>GROUND INFORMATION):\n", "{user_input}\n", "\n", "--\n", "DOCUMENT:\n", "{provided_context}\n", "\n", "--\n", "ANSWER"]}], "metadata": {"language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 2}