{"cells": [{"cell_type": "markdown", "id": "1ae07b50", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/evals/ragas-evaluation.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/evaluate-llm-applications-rag/?utm_campaign=devrel&utm_source=cross-post&utm_medium=organic_social&utm_content=https%3A%2F%2Fgithub.com%2Fmongodb-developer%2FGenAI-Showcase&utm_term=apoorva.joshi)\n"]}, {"cell_type": "markdown", "id": "705420ac", "metadata": {}, "source": ["# RAG Series Part 2: How to evaluate your RAG application\n", "\n", "This notebook shows how to evaluate a RAG application using the [RAGAS](https://docs.ragas.io/en/stable/index.html) framework.\n"]}, {"cell_type": "markdown", "id": "c666fbf2", "metadata": {}, "source": ["## Step 1: Install required libraries\n", "\n", "- **datasets**: Python library to get access to datasets available on Hugging Face Hub\n", "<p>\n", "- **ragas**: Python library for the RAGAS framework\n", "<p>\n", "- **langchain**: Python library to develop LLM applications using LangChain\n", "<p>\n", "- **langchain-mongodb**: Python package to use MongoDB Atlas vector Search with LangChain\n", "<p>\n", "- **langchain-openai**: Python package to use OpenAI models in LangChain\n", "<p>\n", "- **pymongo**: Python driver to interacting with MongoDB\n", "<p>\n", "- **pandas**: Python library for data analysis, exploration and manipulation\n", "<p>\n", "- **tdqm**: Python module to show a progress meter for loops\n", "<p>\n", "- **<PERSON><PERSON><PERSON><PERSON><PERSON>, seaborn**: Python libraries for data visualization\n"]}, {"cell_type": "code", "execution_count": 13, "id": "f973db17", "metadata": {}, "outputs": [], "source": ["! pip install -qU datasets ragas langchain langchain-mongodb langchain-openai \\\n", "pymongo pandas tqdm matplotlib seaborn"]}, {"cell_type": "markdown", "id": "9e2e9a15", "metadata": {}, "source": ["## Step 2: Setup pre-requisites\n"]}, {"cell_type": "markdown", "id": "5f98e464", "metadata": {}, "source": ["- Set the MongoDB connection string. Follow the steps [here](https://www.mongodb.com/docs/manual/reference/connection-string/) to get the connection string from the Atlas UI.\n", "\n", "- Set the OpenAI API key. Steps to obtain an API key as [here](https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "5740a5cd", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 28, "id": "e5c0c3bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your OpenAI API Key:········\n"]}], "source": ["os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter your OpenAI API Key:\")\n", "openai_client = OpenAI()"]}, {"cell_type": "code", "execution_count": 12, "id": "195b4d1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your MongoDB connection string:········\n"]}], "source": ["MONGODB_URI = getpass.getpass(\"Enter your MongoDB connection string:\")"]}, {"cell_type": "markdown", "id": "44deb05f", "metadata": {}, "source": ["## Step 3: Download the evaluation dataset\n"]}, {"cell_type": "code", "execution_count": 30, "id": "88f1cb10", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datasets import load_dataset"]}, {"cell_type": "code", "execution_count": 31, "id": "180f3174", "metadata": {}, "outputs": [], "source": ["data = load_dataset(\"explodinggradients/ragas-wikiqa\", split=\"train\")\n", "df = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": 32, "id": "7c8a4c61", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>correct_answer</th>\n", "      <th>incorrect_answer</th>\n", "      <th>question_id</th>\n", "      <th>generated_with_rag</th>\n", "      <th>context</th>\n", "      <th>generated_without_rag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HOW AFRICAN AMERICANS WERE IMMIGRATED TO THE US</td>\n", "      <td>As such, African immigrants are to be distingu...</td>\n", "      <td>From the Immigration and Nationality Act of 19...</td>\n", "      <td>Q0</td>\n", "      <td>\\nAfrican Americans were immigrated to the Uni...</td>\n", "      <td>[African immigration to the United States refe...</td>\n", "      <td>African Americans were immigrated to the US in...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          question  \\\n", "0  HOW AFRICAN AMERICANS WERE IMMIGRATED TO THE US   \n", "\n", "                                      correct_answer  \\\n", "0  As such, African immigrants are to be distingu...   \n", "\n", "                                    incorrect_answer question_id  \\\n", "0  From the Immigration and Nationality Act of 19...          Q0   \n", "\n", "                                  generated_with_rag  \\\n", "0  \\nAfrican Americans were immigrated to the Uni...   \n", "\n", "                                             context  \\\n", "0  [African immigration to the United States refe...   \n", "\n", "                               generated_without_rag  \n", "0  African Americans were immigrated to the US in...  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(1)"]}, {"cell_type": "code", "execution_count": 33, "id": "7e2228b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["232"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df)"]}, {"cell_type": "markdown", "id": "8abfa781", "metadata": {}, "source": ["## Step 4: Create reference document chunks\n"]}, {"cell_type": "code", "execution_count": 21, "id": "fc218dab", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter"]}, {"cell_type": "code", "execution_count": 22, "id": "ceed4f01", "metadata": {}, "outputs": [], "source": ["# Split text by tokens using the tiktoken tokenizer\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    encoding_name=\"cl100k_base\", keep_separator=False, chunk_size=200, chunk_overlap=30\n", ")"]}, {"cell_type": "code", "execution_count": 23, "id": "417f79e6", "metadata": {}, "outputs": [], "source": ["def split_texts(texts: List[str]) -> List[str]:\n", "    \"\"\"\n", "    Split large texts into chunks\n", "\n", "    Args:\n", "        texts (List[str]): List of reference texts\n", "\n", "    Returns:\n", "        List[str]: List of chunked texts\n", "    \"\"\"\n", "    chunked_texts = []\n", "    for text in texts:\n", "        chunks = text_splitter.create_documents([text])\n", "        chunked_texts.extend([chunk.page_content for chunk in chunks])\n", "    return chunked_texts"]}, {"cell_type": "code", "execution_count": 24, "id": "b3116297", "metadata": {}, "outputs": [], "source": ["# Split the context field into chunks\n", "df[\"chunks\"] = df[\"context\"].apply(lambda x: split_texts(x))"]}, {"cell_type": "code", "execution_count": 25, "id": "bc620e35", "metadata": {}, "outputs": [], "source": ["# Aggregate list of all chunks\n", "all_chunks = df[\"chunks\"].tolist()\n", "docs = [item for chunk in all_chunks for item in chunk]"]}, {"cell_type": "code", "execution_count": 29, "id": "9c1b9b66", "metadata": {}, "outputs": [{"data": {"text/plain": ["3795"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(docs)"]}, {"cell_type": "code", "execution_count": 40, "id": "19fed721", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON><PERSON> had problems because permits were not issued for some street scenes. This caused him to film some scenes on the Las Vegas strip in one take to avoid the police, which <PERSON><PERSON><PERSON> said benefited production and the authenticity of the acting, remarking \"I\\'ve always hated the convention of shooting on a street, and then having to stop the traffic, and then having to tell the actors, \\'Well, there\\'s meant to be traffic here, so you\\'re going to have to shout.\\' And they\\'re shouting, but it\\'s quiet and they feel really stupid, because it\\'s unnatural. You put them up against a couple of trucks, with it all happening around them, and their voices become great\". Filming took place over 28 days.'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[100]"]}, {"cell_type": "markdown", "id": "116fd2b3", "metadata": {}, "source": ["## Step 5: Create embeddings and ingest them into MongoDB\n"]}, {"cell_type": "code", "execution_count": 14, "id": "bd6128ba", "metadata": {}, "outputs": [], "source": ["from pymongo import MongoClient\n", "from tqdm.auto import tqdm"]}, {"cell_type": "code", "execution_count": 42, "id": "3810090d", "metadata": {}, "outputs": [], "source": ["def get_embeddings(docs: List[str], model: str) -> List[List[float]]:\n", "    \"\"\"\n", "    Generate embeddings using the OpenAI API.\n", "\n", "    Args:\n", "        docs (List[str]): List of texts to embed\n", "        model (str, optional): Model name. Defaults to \"text-embedding-3-large\".\n", "\n", "    Returns:\n", "        List[float]: Array of embeddings\n", "    \"\"\"\n", "    # replace newlines, which can negatively affect performance.\n", "    docs = [doc.replace(\"\\n\", \" \") for doc in docs]\n", "    response = openai_client.embeddings.create(input=docs, model=model)\n", "    response = [r.embedding for r in response.data]\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "id": "8f1a433a", "metadata": {}, "outputs": [], "source": ["client = MongoClient(MONGODB_URI, appname=\"devrel.showcase.ragas_eval\")\n", "DB_NAME = \"ragas_evals\"\n", "db = client[DB_NAME]"]}, {"cell_type": "code", "execution_count": 44, "id": "284c00dd", "metadata": {}, "outputs": [], "source": ["batch_size = 128"]}, {"cell_type": "code", "execution_count": 45, "id": "ccbce870", "metadata": {}, "outputs": [], "source": ["EVAL_EMBEDDING_MODELS = [\"text-embedding-ada-002\", \"text-embedding-3-small\"]"]}, {"cell_type": "code", "execution_count": 46, "id": "b604a706", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Getting embeddings for the text-embedding-ada-002 model\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "57464acec4674b439109fabeda7c3f57", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/30 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Finished getting embeddings for the text-embedding-ada-002 model\n", "Inserting embeddings for the text-embedding-ada-002 model\n", "Finished inserting embeddings for the text-embedding-ada-002 model\n", "Getting embeddings for the text-embedding-3-small model\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "681f975707ec4476a7cf2c0bb213047d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/30 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Finished getting embeddings for the text-embedding-3-small model\n", "Inserting embeddings for the text-embedding-3-small model\n", "Finished inserting embeddings for the text-embedding-3-small model\n"]}], "source": ["for model in EVAL_EMBEDDING_MODELS:\n", "    embedded_docs = []\n", "    print(f\"Getting embeddings for the {model} model\")\n", "    for i in tqdm(range(0, len(docs), batch_size)):\n", "        end = min(len(docs), i + batch_size)\n", "        batch = docs[i:end]\n", "        # Generate embeddings for current batch\n", "        batch_embeddings = get_embeddings(batch, model)\n", "        # Creating the documents to ingest into MongoDB for current batch\n", "        batch_embedded_docs = [\n", "            {\"text\": batch[i], \"embedding\": batch_embeddings[i]}\n", "            for i in range(len(batch))\n", "        ]\n", "        embedded_docs.extend(batch_embedded_docs)\n", "    print(f\"Finished getting embeddings for the {model} model\")\n", "\n", "    # Bulk insert documents into a MongoDB collection\n", "    print(f\"Inserting embeddings for the {model} model\")\n", "    collection = db[model]\n", "    collection.delete_many({})\n", "    collection.insert_many(embedded_docs)\n", "    print(f\"Finished inserting embeddings for the {model} model\")"]}, {"cell_type": "markdown", "id": "3a038b19", "metadata": {}, "source": ["## Step 6: Compare embedding models for retrieval\n"]}, {"cell_type": "code", "execution_count": 47, "id": "ad8c421f", "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "from datasets import Dataset\n", "from langchain_core.vectorstores import VectorStoreRetriever\n", "from langchain_mongodb import MongoDBAtlasVectorSearch\n", "from langchain_openai import OpenAIEmbeddings\n", "from ragas import RunConfig, evaluate\n", "from ragas.metrics import context_precision, context_recall\n", "\n", "# Allow nested use of asyncio (used by RAGAS)\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 48, "id": "6fbdb607", "metadata": {}, "outputs": [], "source": ["def get_retriever(model: str, k: int) -> VectorStoreRetriever:\n", "    \"\"\"\n", "    Given an embedding model and top k, get a vector store retriever object\n", "\n", "    Args:\n", "        model (str): Embedding model to use\n", "        k (int): Number of results to retrieve\n", "\n", "    Returns:\n", "        VectorStoreRetriever: A vector store retriever object\n", "    \"\"\"\n", "    embeddings = OpenAIEmbeddings(model=model)\n", "\n", "    vector_store = MongoDBAtlasVectorSearch.from_connection_string(\n", "        connection_string=MONGODB_URI,\n", "        namespace=f\"{DB_NAME}.{model}\",\n", "        embedding=embeddings,\n", "        index_name=\"vector_index\",\n", "        text_key=\"text\",\n", "    )\n", "\n", "    retriever = vector_store.as_retriever(\n", "        search_type=\"similarity\", search_kwargs={\"k\": k}\n", "    )\n", "    return retriever"]}, {"cell_type": "code", "execution_count": 49, "id": "2733c6c5", "metadata": {}, "outputs": [], "source": ["QUESTIONS = df[\"question\"].to_list()\n", "GROUND_TRUTH = df[\"correct_answer\"].tolist()"]}, {"cell_type": "code", "execution_count": 50, "id": "64d1f50a", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f4c3aa6ff87745e4acb54617fe390221", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/232 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "28a1370738a8460f8e8d8d50f359313b", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/464 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Failed to parse output. Returning None.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Result for the text-embedding-ada-002 model: {'context_precision': 0.9310, 'context_recall': 0.8561}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6193f91e3cec4cf8aae243dcec7b0af5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/232 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b320fdc87b9d4878b583918cd51f7889", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/464 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Result for the text-embedding-3-small model: {'context_precision': 0.9116, 'context_recall': 0.8826}\n"]}], "source": ["for model in EVAL_EMBEDDING_MODELS:\n", "    data = {\"question\": [], \"ground_truth\": [], \"contexts\": []}\n", "    data[\"question\"] = QUESTIONS\n", "    data[\"ground_truth\"] = GROUND_TRUTH\n", "\n", "    retriever = get_retriever(model, 2)\n", "    # Getting relevant documents for the evaluation dataset\n", "    for question in tqdm(QUESTIONS):\n", "        data[\"contexts\"].append(\n", "            [doc.page_content for doc in retriever.get_relevant_documents(question)]\n", "        )\n", "    # RAGAS expects a Dataset object\n", "    dataset = Dataset.from_dict(data)\n", "    # RAGAS runtime settings to avoid hitting OpenAI rate limits\n", "    run_config = RunConfig(max_workers=4, max_wait=180)\n", "    result = evaluate(\n", "        dataset=dataset,\n", "        metrics=[context_precision, context_recall],\n", "        run_config=run_config,\n", "        raise_exceptions=False,\n", "    )\n", "    print(f\"Result for the {model} model: {result}\")"]}, {"cell_type": "markdown", "id": "a01417f5", "metadata": {}, "source": ["## Step 7: Compare completion models for generation\n"]}, {"cell_type": "code", "execution_count": 51, "id": "7920a58f", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.runnables.base import RunnableSequence\n", "from langchain_openai import ChatOpenAI\n", "from ragas.metrics import answer_relevancy, faithfulness"]}, {"cell_type": "code", "execution_count": 52, "id": "44221aa4", "metadata": {}, "outputs": [], "source": ["def get_rag_chain(retriever: VectorStoreRetriever, model: str) -> RunnableSequence:\n", "    \"\"\"\n", "    Create a basic RAG chain\n", "\n", "    Args:\n", "        retriever (VectorStoreRetriever): Vector store retriever object\n", "        model (str): Chat completion model to use\n", "\n", "    Returns:\n", "        RunnableSequence: A RAG chain\n", "    \"\"\"\n", "    # Generate context using the retriever, and pass the user question through\n", "    retrieve = {\n", "        \"context\": retriever\n", "        | (lambda docs: \"\\n\\n\".join([d.page_content for d in docs])),\n", "        \"question\": <PERSON><PERSON>blePassthrough(),\n", "    }\n", "    template = \"\"\"Answer the question based only on the following context: \\\n", "    {context}\n", "\n", "    Question: {question}\n", "    \"\"\"\n", "    # Defining the chat prompt\n", "    prompt = ChatPromptTemplate.from_template(template)\n", "    # Defining the model to be used for chat completion\n", "    llm = ChatOpenAI(temperature=0, model=model)\n", "    # Parse output as a string\n", "    parse_output = StrOutputParser()\n", "\n", "    # Naive RAG chain\n", "    rag_chain = retrieve | prompt | llm | parse_output\n", "    return rag_chain"]}, {"cell_type": "code", "execution_count": 54, "id": "81b685ba", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8e0dbbc8da9a4e7bae2f8b891833c5f1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/232 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c2831fa2ae464fcdabc90642ee4e46b8", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/464 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["No statements were generated from the answer.\n", "No statements were generated from the answer.\n", "No statements were generated from the answer.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Result for the gpt-3.5-turbo-1106 model: {'faithfulness': 0.9671, 'answer_relevancy': 0.9105}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "79f5f67fabc94832ba448a36fe12f725", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/232 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e628a1af3a614c90ac0a11746f996187", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/464 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["No statements were generated from the answer.\n", "No statements were generated from the answer.\n", "No statements were generated from the answer.\n", "No statements were generated from the answer.\n", "No statements were generated from the answer.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Result for the gpt-3.5-turbo model: {'faithfulness': 0.9714, 'answer_relevancy': 0.9087}\n"]}], "source": ["for model in [\"gpt-3.5-turbo-1106\", \"gpt-3.5-turbo\"]:\n", "    data = {\"question\": [], \"ground_truth\": [], \"contexts\": [], \"answer\": []}\n", "    data[\"question\"] = QUESTIONS\n", "    data[\"ground_truth\"] = GROUND_TRUTH\n", "    # Using the best embedding model from the retriever evaluation\n", "    retriever = get_retriever(\"text-embedding-3-small\", 2)\n", "    rag_chain = get_rag_chain(retriever, model)\n", "    for question in tqdm(QUESTIONS):\n", "        data[\"answer\"].append(rag_chain.invoke(question))\n", "        data[\"contexts\"].append(\n", "            [doc.page_content for doc in retriever.get_relevant_documents(question)]\n", "        )\n", "    # RAGAS expects a Dataset object\n", "    dataset = Dataset.from_dict(data)\n", "    # RAGAS runtime settings to avoid hitting OpenAI rate limits\n", "    run_config = RunConfig(max_workers=4, max_wait=180)\n", "    result = evaluate(\n", "        dataset=dataset,\n", "        metrics=[faithfulness, answer_relevancy],\n", "        run_config=run_config,\n", "        raise_exceptions=False,\n", "    )\n", "    print(f\"Result for the {model} model: {result}\")"]}, {"cell_type": "markdown", "id": "fa0987b0", "metadata": {}, "source": ["## Step 8: Measure overall performance of the RAG application\n"]}, {"cell_type": "code", "execution_count": 55, "id": "d2634bef", "metadata": {}, "outputs": [], "source": ["from ragas.metrics import answer_correctness, answer_similarity"]}, {"cell_type": "code", "execution_count": 56, "id": "2df51425", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "213c0d9e4690497a830a31bc0f235a4a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/232 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "df0b655529e345d982aa017057c5b55d", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/464 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Overall metrics: {'answer_similarity': 0.8873, 'answer_correctness': 0.5922}\n"]}], "source": ["data = {\"question\": [], \"ground_truth\": [], \"answer\": []}\n", "data[\"question\"] = QUESTIONS\n", "data[\"ground_truth\"] = GROUND_TRUTH\n", "# Using the best embedding model from the retriever evaluation\n", "retriever = get_retriever(\"text-embedding-3-small\", 2)\n", "# Using the best completion model from the generator evaluation\n", "rag_chain = get_rag_chain(retriever, \"gpt-3.5-turbo\")\n", "for question in tqdm(QUESTIONS):\n", "    data[\"answer\"].append(rag_chain.invoke(question))\n", "\n", "dataset = Dataset.from_dict(data)\n", "run_config = RunConfig(max_workers=4, max_wait=180)\n", "result = evaluate(\n", "    dataset=dataset,\n", "    metrics=[answer_similarity, answer_correctness],\n", "    run_config=run_config,\n", "    raise_exceptions=False,\n", ")\n", "print(f\"Overall metrics: {result}\")"]}, {"cell_type": "code", "execution_count": 57, "id": "693d6962", "metadata": {}, "outputs": [], "source": ["result_df = result.to_pandas()"]}, {"cell_type": "code", "execution_count": 58, "id": "fc41aaf4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>ground_truth</th>\n", "      <th>answer</th>\n", "      <th>answer_similarity</th>\n", "      <th>answer_correctness</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HOW AFRICAN AMERICANS WERE IMMIGRATED TO THE US</td>\n", "      <td>As such, African immigrants are to be distingu...</td>\n", "      <td>African Americans were involuntarily brought f...</td>\n", "      <td>0.913499</td>\n", "      <td>0.603375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>what are points on a mortgage</td>\n", "      <td>Points, sometimes also called a \"discount poin...</td>\n", "      <td>Points on a mortgage are a form of pre-paid in...</td>\n", "      <td>0.914100</td>\n", "      <td>0.528525</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>how does interlibrary loan work</td>\n", "      <td>The user makes a request with their local libr...</td>\n", "      <td>Interlibrary loan works by allowing patrons of...</td>\n", "      <td>0.885328</td>\n", "      <td>0.864189</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>WHAT IS A FY QUARTER</td>\n", "      <td>A fiscal year (or financial year, or sometimes...</td>\n", "      <td>A FY quarter is a three-month period within a ...</td>\n", "      <td>0.872582</td>\n", "      <td>0.718120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>who wrote a rose is a rose is a rose</td>\n", "      <td>The sentence \"Rose is a rose is a rose is a ro...</td>\n", "      <td><PERSON></td>\n", "      <td>0.868273</td>\n", "      <td>0.592068</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          question  \\\n", "0  HOW AFRICAN AMERICANS WERE IMMIGRATED TO THE US   \n", "1                    what are points on a mortgage   \n", "2                  how does interlibrary loan work   \n", "3                             WHAT IS A FY QUARTER   \n", "4             who wrote a rose is a rose is a rose   \n", "\n", "                                        ground_truth  \\\n", "0  As such, African immigrants are to be distingu...   \n", "1  Points, sometimes also called a \"discount poin...   \n", "2  The user makes a request with their local libr...   \n", "3  A fiscal year (or financial year, or sometimes...   \n", "4  The sentence \"Rose is a rose is a rose is a ro...   \n", "\n", "                                              answer  answer_similarity  \\\n", "0  African Americans were involuntarily brought f...           0.913499   \n", "1  Points on a mortgage are a form of pre-paid in...           0.914100   \n", "2  Interlibrary loan works by allowing patrons of...           0.885328   \n", "3  A FY quarter is a three-month period within a ...           0.872582   \n", "4                                     <PERSON>           0.868273   \n", "\n", "   answer_correctness  \n", "0            0.603375  \n", "1            0.528525  \n", "2            0.864189  \n", "3            0.718120  \n", "4            0.592068  "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df.head(5)"]}, {"cell_type": "code", "execution_count": 60, "id": "5c981019", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>ground_truth</th>\n", "      <th>answer</th>\n", "      <th>answer_similarity</th>\n", "      <th>answer_correctness</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HOW AFRICAN AMERICANS WERE IMMIGRATED TO THE US</td>\n", "      <td>As such, African immigrants are to be distingu...</td>\n", "      <td>African Americans were involuntarily brought f...</td>\n", "      <td>0.913499</td>\n", "      <td>0.603375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>what are points on a mortgage</td>\n", "      <td>Points, sometimes also called a \"discount poin...</td>\n", "      <td>Points on a mortgage are a form of pre-paid in...</td>\n", "      <td>0.914100</td>\n", "      <td>0.528525</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>who wrote a rose is a rose is a rose</td>\n", "      <td>The sentence \"Rose is a rose is a rose is a ro...</td>\n", "      <td><PERSON></td>\n", "      <td>0.868273</td>\n", "      <td>0.592068</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>what bacteria grow on macconkey agar</td>\n", "      <td>MacConkey agar is a culture medium designed to...</td>\n", "      <td>Gram-negative and enteric bacteria grow on Mac...</td>\n", "      <td>0.905663</td>\n", "      <td>0.526416</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>what is a day care for?</td>\n", "      <td>Child care or day care is the care of a child ...</td>\n", "      <td>A day care is for the care and supervision of ...</td>\n", "      <td>0.945214</td>\n", "      <td>0.536303</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224</th>\n", "      <td>how many asian indians live in usa</td>\n", "      <td>Indian Americans are citizens of the United St...</td>\n", "      <td>Approximately 4.5 million Indian Americans liv...</td>\n", "      <td>0.888910</td>\n", "      <td>0.597227</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225</th>\n", "      <td>what happened to george o'malley on grey's ana...</td>\n", "      <td>In 2007, <PERSON>'s co-star <PERSON> ( ...</td>\n", "      <td><PERSON> died on Grey's Anatomy.</td>\n", "      <td>0.805490</td>\n", "      <td>0.201374</td>\n", "    </tr>\n", "    <tr>\n", "      <th>226</th>\n", "      <td>how much did yankee stadium cost</td>\n", "      <td>Also controversial was the price tag of $1.5 b...</td>\n", "      <td>$2.3 billion</td>\n", "      <td>0.773524</td>\n", "      <td>0.193381</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229</th>\n", "      <td>how kimberlite pipes form</td>\n", "      <td>Volcanic pipes are relatively rare.</td>\n", "      <td>Kimberlite pipes form when an eruption ejects ...</td>\n", "      <td>0.818068</td>\n", "      <td>0.204517</td>\n", "    </tr>\n", "    <tr>\n", "      <th>230</th>\n", "      <td>what county is coatesville indiana located in</td>\n", "      <td>Coatesville is a town in Clay Township , Hendr...</td>\n", "      <td>Hendricks County</td>\n", "      <td>0.799358</td>\n", "      <td>0.699839</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                              question  \\\n", "0      HOW AFRICAN AMERICANS WERE IMMIGRATED TO THE US   \n", "1                        what are points on a mortgage   \n", "4                 who wrote a rose is a rose is a rose   \n", "6                 what bacteria grow on macconkey agar   \n", "10                             what is a day care for?   \n", "..                                                 ...   \n", "224                 how many asian indians live in usa   \n", "225  what happened to george o'malley on grey's ana...   \n", "226                   how much did yankee stadium cost   \n", "229                          how kimberlite pipes form   \n", "230      what county is coatesville indiana located in   \n", "\n", "                                          ground_truth  \\\n", "0    As such, African immigrants are to be distingu...   \n", "1    Points, sometimes also called a \"discount poin...   \n", "4    The sentence \"Rose is a rose is a rose is a ro...   \n", "6    MacConkey agar is a culture medium designed to...   \n", "10   Child care or day care is the care of a child ...   \n", "..                                                 ...   \n", "224  Indian Americans are citizens of the United St...   \n", "225  In 2007, <PERSON>'s co-star <PERSON> ( ...   \n", "226  Also controversial was the price tag of $1.5 b...   \n", "229                Volcanic pipes are relatively rare.   \n", "230  Coatesville is a town in Clay Township , Hendr...   \n", "\n", "                                                answer  answer_similarity  \\\n", "0    African Americans were involuntarily brought f...           0.913499   \n", "1    Points on a mortgage are a form of pre-paid in...           0.914100   \n", "4                                       <PERSON>           0.868273   \n", "6    Gram-negative and enteric bacteria grow on Mac...           0.905663   \n", "10   A day care is for the care and supervision of ...           0.945214   \n", "..                                                 ...                ...   \n", "224  Approximately 4.5 million Indian Americans liv...           0.888910   \n", "225            <PERSON> died on Grey's Anatomy.           0.805490   \n", "226                                       $2.3 billion           0.773524   \n", "229  Kimberlite pipes form when an eruption ejects ...           0.818068   \n", "230                                   Hendricks County           0.799358   \n", "\n", "     answer_correctness  \n", "0              0.603375  \n", "1              0.528525  \n", "4              0.592068  \n", "6              0.526416  \n", "10             0.536303  \n", "..                  ...  \n", "224            0.597227  \n", "225            0.201374  \n", "226            0.193381  \n", "229            0.204517  \n", "230            0.699839  \n", "\n", "[112 rows x 5 columns]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df[result_df[\"answer_correctness\"] < 0.7]"]}, {"cell_type": "code", "execution_count": 59, "id": "9ad440d0", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(\n", "    result_df[1:10].set_index(\"question\")[[\"answer_similarity\", \"answer_correctness\"]],\n", "    annot=True,\n", "    cmap=\"flare\",\n", ")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "3594fb81", "metadata": {}, "source": ["## Step 9: Tracking performance over time\n"]}, {"cell_type": "code", "execution_count": 2, "id": "ce96abfe", "metadata": {}, "outputs": [], "source": ["from datetime import datetime"]}, {"cell_type": "code", "execution_count": 6, "id": "6460ac25", "metadata": {}, "outputs": [], "source": ["result[\"timestamp\"] = datetime.now()"]}, {"cell_type": "code", "execution_count": 22, "id": "06c2361e", "metadata": {}, "outputs": [{"data": {"text/plain": ["InsertOneResult(ObjectId('66132f1305da5dc970ad919c'), acknowledged=True)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["collection = db[\"metrics\"]\n", "collection.insert_one(result)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 5}