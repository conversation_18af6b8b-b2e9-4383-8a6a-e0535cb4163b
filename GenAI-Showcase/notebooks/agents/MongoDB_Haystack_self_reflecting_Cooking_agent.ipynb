{"cells": [{"cell_type": "markdown", "metadata": {"id": "QFdG4eYf3h0L"}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/agents/MongoDB_Haystack_self_reflecting_Cooking_agent.ipynb)\n"]}, {"cell_type": "markdown", "metadata": {"id": "rrobdhRcNb5I"}, "source": ["# Haystack and MongoDB Atlas Agentic RAG pipelines\n", "\n", "Haystack and MongoDB enhanced example building on top of the basic RAG pipeline demonstrated on the following [notebook](https://github.com/mongodb-developer/GenAI-Showcase/blob/main/notebooks/rag/haystack_mongodb_cooking_advisor_pipeline.ipynb). Here the pipelines uses advanced technics of self reflection to advise on reciepes considering prices associated from the MongoDB Atlas vector store.\n", "\n", "Install dependencies:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "76dK0ehtNY2L", "outputId": "4bf711f0-1f33-4542-d70c-ae2f52ae22a3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting haystack-ai\n", "  Downloading haystack_ai-2.2.3-py3-none-any.whl (345 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m345.2/345.2 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting mongodb-atlas-haystack\n", "  Downloading mongodb_atlas_haystack-0.3.0-py3-none-any.whl (13 kB)\n", "Collecting tik<PERSON>en\n", "  Downloading tiktoken-0.7.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting datasets\n", "  Downloading datasets-2.20.0-py3-none-any.whl (547 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m547.8/547.8 kB\u001b[0m \u001b[31m13.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (3.1.4)\n", "Collecting lazy-imports (from haystack-ai)\n", "  Downloading lazy_imports-0.3.1-py3-none-any.whl (12 kB)\n", "Requirement already satisfied: more-itertools in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (10.1.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (3.3)\n", "Requirement already satisfied: numpy<2 in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (1.25.2)\n", "Collecting openai>=1.1.0 (from haystack-ai)\n", "  Downloading openai-1.35.9-py3-none-any.whl (328 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m328.3/328.3 kB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (2.0.3)\n", "Collecting posthog (from haystack-ai)\n", "  Downloading posthog-3.5.0-py2.py3-none-any.whl (41 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.3/41.3 kB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (2.8.2)\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (6.0.1)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (2.31.0)\n", "Requirement already satisfied: tenacity!=8.4.0 in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (8.4.2)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (4.66.4)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from haystack-ai) (4.12.2)\n", "Collecting pymongo[srv] (from mongodb-atlas-haystack)\n", "  Downloading pymongo-4.8.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m11.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken) (2024.5.15)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from datasets) (3.15.4)\n", "Collecting pyarrow>=15.0.0 (from datasets)\n", "  Downloading pyarrow-16.1.0-cp310-cp310-manylinux_2_28_x86_64.whl (40.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m40.8/40.8 MB\u001b[0m \u001b[31m12.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pyarrow-hotfix in /usr/local/lib/python3.10/dist-packages (from datasets) (0.6)\n", "Collecting dill<0.3.9,>=0.3.0 (from datasets)\n", "  Downloading dill-0.3.8-py3-none-any.whl (116 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.3/116.3 kB\u001b[0m \u001b[31m10.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting requests (from haystack-ai)\n", "  Downloading requests-2.32.3-py3-none-any.whl (64 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m64.9/64.9 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting xxhash (from datasets)\n", "  Downloading xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.1/194.1 kB\u001b[0m \u001b[31m10.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting multiprocess (from datasets)\n", "  Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m134.8/134.8 kB\u001b[0m \u001b[31m11.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: fsspec[http]<=2024.5.0,>=2023.1.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (2023.6.0)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from datasets) (3.9.5)\n", "Requirement already satisfied: huggingface-hub>=0.21.2 in /usr/local/lib/python3.10/dist-packages (from datasets) (0.23.4)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from datasets) (24.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (4.0.3)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai>=1.1.0->haystack-ai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->haystack-ai) (1.7.0)\n", "Collecting httpx<1,>=0.23.0 (from openai>=1.1.0->haystack-ai)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from openai>=1.1.0->haystack-ai) (2.7.4)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai>=1.1.0->haystack-ai) (1.3.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->haystack-ai) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->haystack-ai) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->haystack-ai) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->haystack-ai) (2024.6.2)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->haystack-ai) (2.1.5)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->haystack-ai) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->haystack-ai) (2024.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil->haystack-ai) (1.16.0)\n", "Collecting monotonic>=1.5 (from posthog->haystack-ai)\n", "  Downloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)\n", "Collecting backoff>=1.10.0 (from posthog->haystack-ai)\n", "  Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "\u001b[33mWARNING: pymongo 4.8.0 does not provide the extra 'srv'\u001b[0m\u001b[33m\n", "\u001b[0mCollecting dnspython<3.0.0,>=1.16.0 (from pymongo[srv]->mongodb-atlas-haystack)\n", "  Downloading dnspython-2.6.1-py3-none-any.whl (307 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m22.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai>=1.1.0->haystack-ai) (1.2.1)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai>=1.1.0->haystack-ai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m7.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->openai>=1.1.0->haystack-ai)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai>=1.1.0->haystack-ai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai>=1.1.0->haystack-ai) (2.18.4)\n", "Installing collected packages: monotonic, xxhash, requests, pyarrow, lazy-imports, h11, dnspython, dill, backoff, tiktoken, pymongo, posthog, multiprocess, httpcore, httpx, openai, datasets, haystack-ai, mongodb-atlas-haystack\n", "  Attempting uninstall: requests\n", "    Found existing installation: requests 2.31.0\n", "    Uninstalling requests-2.31.0:\n", "      Successfully uninstalled requests-2.31.0\n", "  Attempting uninstall: p<PERSON><PERSON>\n", "    Found existing installation: pyarrow 14.0.2\n", "    Uninstalling pyarrow-14.0.2:\n", "      Successfully uninstalled pyarrow-14.0.2\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "cudf-cu12 24.4.1 requires pyarrow<15.0.0a0,>=14.0.1, but you have pyarrow 16.1.0 which is incompatible.\n", "google-colab 1.0.0 requires requests==2.31.0, but you have requests 2.32.3 which is incompatible.\n", "ibis-framework 8.0.0 requires pyarrow<16,>=2, but you have pyarrow 16.1.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed backoff-2.2.1 datasets-2.20.0 dill-0.3.8 dnspython-2.6.1 h11-0.14.0 haystack-ai-2.2.3 httpcore-1.0.5 httpx-0.27.0 lazy-imports-0.3.1 mongodb-atlas-haystack-0.3.0 monotonic-1.6 multiprocess-0.70.16 openai-1.35.9 posthog-3.5.0 pyarrow-16.1.0 pymongo-4.8.0 requests-2.32.3 tiktoken-0.7.0 xxhash-3.4.1\n"]}], "source": ["pip install haystack-ai mongodb-atlas-haystack tiktoken datasets"]}, {"cell_type": "markdown", "metadata": {"id": "aeg_wcIiPYnY"}, "source": ["\n", "## Setup MongoDB Atlas connection and Open AI\n", "\n", "\n", "* Set the MongoDB connection string. Follow the steps [here](https://www.mongodb.com/docs/manual/reference/connection-string/) to get the connection string from the Atlas UI. If you wish to use google collab we recommend to allow access on Atlas Network tab to `0.0.0.0/0` so the notebook node can access the database.\n", "\n", "* Set the OpenAI API key. Steps to obtain an API key as [here](https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "MZokdDxIPb9p"}, "outputs": [], "source": ["import getpass\n", "import os"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "57gYJTBVPfBX", "outputId": "96ac3e3f-d5f3-4b98-ad46-d13c802de250"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your MongoDB connection string:··········\n"]}], "source": ["os.environ[\"MONGO_CONNECTION_STRING\"] = getpass.getpass(\n", "    \"Enter your MongoDB connection string:\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "J8Gd-SMuRSH-", "outputId": "c4de1340-4ad9-4f92-df6e-1554295888b3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your Open AI Key:··········\n"]}], "source": ["os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter your Open AI Key:\")"]}, {"cell_type": "markdown", "metadata": {"id": "Fv1pPHqXQFa-"}, "source": ["## Create vector search index on collection\n", "\n", "Follow this [tutorial](https://www.mongodb.com/docs/atlas/atlas-vector-search/create-index/) to create a vector index on database: `haystack_test` collection `test_collection`.\n", "\n", "Verify that the index name is `vector_index` and the syntax specify:\n", "```\n", "{\n", "  \"fields\": [\n", "    {\n", "      \"type\": \"vector\",\n", "      \"path\": \"embedding\",\n", "      \"numDimensions\": 1536,\n", "      \"similarity\": \"cosine\"\n", "    }\n", "  ]\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {"id": "cOMyplbvOMDk"}, "source": ["### Setup vector store to load documents:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "-y9waymAOOgs"}, "outputs": [], "source": ["from bson import json_util\n", "from haystack import Document, Pipeline\n", "from haystack.components.builders.prompt_builder import PromptBuilder\n", "from haystack.components.embedders import OpenAIDocumentEmbedder, OpenAITextEmbedder\n", "from haystack.components.generators import OpenAIGenerator\n", "from haystack.components.writers import DocumentWriter\n", "from haystack.document_stores.types import DuplicatePolicy\n", "from haystack_integrations.components.retrievers.mongodb_atlas import (\n", "    MongoDBAtlasEmbeddingRetriever,\n", ")\n", "from haystack_integrations.document_stores.mongodb_atlas import (\n", "    MongoDBAtlasDocumentStore,\n", ")\n", "\n", "dataset = {\n", "    \"train\": [\n", "        {\n", "            \"title\": \"Spinach Lasagna Sheets\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"Infused with spinach, these sheets add a pop of color and extra nutrients.\",\n", "            \"category\": \"Pasta\",\n", "            \"emoji\": \"📗\",\n", "        },\n", "        {\n", "            \"title\": \"Gluten-Free Lasagna Sheets\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"Perfect for those with gluten intolerance, made with a blend of rice and corn flour.\",\n", "            \"category\": \"Pasta\",\n", "            \"emoji\": \"🍚🌽\",\n", "        },\n", "        {\n", "            \"title\": \"Mascarpone\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"Creamy and rich, this cheese adds a luxurious touch to lasagna.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON>\",\n", "            \"price\": \"$3.00\",\n", "            \"description\": \"A mild, crumbly cheese that can be a suitable replacement for ricotta.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Vegetarian Lentil <PERSON>\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"A meatless option made with cooked lentils that mimics the texture of ground meat.\",\n", "            \"category\": \"Vegetarian\",\n", "            \"emoji\": \"🍲\",\n", "        },\n", "        {\n", "            \"title\": \"Turkey Bolognese\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"A leaner alternative to beef, turkey provides a lighter but flavorful taste.\",\n", "            \"category\": \"Poultry\",\n", "            \"emoji\": \"🦃\",\n", "        },\n", "        {\n", "            \"title\": \"Mu<PERSON><PERSON> and Walnut Sauce\",\n", "            \"price\": \"$5.50\",\n", "            \"description\": \"Combining chopped mushrooms and walnuts for a hearty vegetarian filling.\",\n", "            \"category\": \"Vegetarian\",\n", "            \"emoji\": \"🍄🥜\",\n", "        },\n", "        {\n", "            \"title\": \"Chicken Bolognese\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"Ground chicken offers a different twist on the classic meat sauce.\",\n", "            \"category\": \"Poultry\",\n", "            \"emoji\": \"🐔\",\n", "        },\n", "        {\n", "            \"title\": \"Vegan Soy Meat Sauce\",\n", "            \"price\": \"$4.50\",\n", "            \"description\": \"Made from soy protein, this vegan meat sauce replicates the texture and flavor of traditional meat.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🌱\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON>ato <PERSON>\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"A tangy alternative to béchamel, made with fresh tomatoes and basil.\",\n", "            \"category\": \"Vegetarian\",\n", "            \"emoji\": \"🍅\",\n", "        },\n", "        {\n", "            \"title\": \"Pesto Cream Sauce\",\n", "            \"price\": \"$5.50\",\n", "            \"description\": \"A fusion of creamy béchamel and rich basil pesto for a unique flavor.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🍝\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON>\",\n", "            \"price\": \"$4.50\",\n", "            \"description\": \"A rich and creamy white sauce made with parmesan and butter.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧈\",\n", "        },\n", "        {\n", "            \"title\": \"Coconut Milk Béchamel\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"A dairy-free version of the classic béchamel made with coconut milk.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🥥\",\n", "        },\n", "        {\n", "            \"title\": \"Vegan Cashew Cream Sauce\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"A rich and creamy sauce made from blended cashews as a dairy-free alternative.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🥜\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON>\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"Another leafy green option, kale offers a chewy texture and rich nutrients.\",\n", "            \"category\": \"Leafy Greens\",\n", "            \"emoji\": \"🥬\",\n", "        },\n", "        {\n", "            \"title\": \"Bell Peppers\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"Sliced bell peppers in various colors add sweetness and crunch.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🫑\",\n", "        },\n", "        {\n", "            \"title\": \"Artichoke Hearts\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"Tender and flavorful, artichoke hearts bring a Mediterranean twist to the dish.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🍽️\",\n", "        },\n", "        {\n", "            \"title\": \"Spinach\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"Fresh or frozen spinach adds a pop of color and nutrients.\",\n", "            \"category\": \"Leafy Greens\",\n", "            \"emoji\": \"🥬\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON><PERSON><PERSON>\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"Small broccoli florets provide texture and a distinct flavor.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🥦\",\n", "        },\n", "        {\n", "            \"title\": \"Whole Wheat Lasagna Sheets\",\n", "            \"price\": \"$3.00\",\n", "            \"description\": \"Made from whole wheat grains, these sheets are healthier and provide a nutty flavor.\",\n", "            \"category\": \"Pasta\",\n", "            \"emoji\": \"🌾\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON><PERSON><PERSON> Slices\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"Thinly sliced zucchini can replace traditional pasta for a low-carb version.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🥒\",\n", "        },\n", "        {\n", "            \"title\": \"Eggplant Slices\",\n", "            \"price\": \"$2.75\",\n", "            \"description\": \"Thin slices of eggplant provide a meaty texture, ideal for vegetarian lasagna.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🍆\",\n", "        },\n", "        {\n", "            \"title\": \"Ground Turkey\",\n", "            \"price\": \"$4.50\",\n", "            \"description\": \"A leaner alternative to beef, turkey provides a lighter but flavorful taste.\",\n", "            \"category\": \"Meat\",\n", "            \"emoji\": \"🦃\",\n", "        },\n", "        {\n", "            \"title\": \"Vegetarian Lentil Min<PERSON>\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"A meatless option made with cooked lentils that mimics the texture of ground meat.\",\n", "            \"category\": \"Vegetarian\",\n", "            \"emoji\": \"🍲\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON><PERSON> and Walnut Mince\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"Combining chopped mushrooms and walnuts for a hearty vegetarian filling.\",\n", "            \"category\": \"Vegetarian\",\n", "            \"emoji\": \"🍄🥜\",\n", "        },\n", "        {\n", "            \"title\": \"Ground Chicken\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"Ground chicken offers a different twist on the classic meat sauce.\",\n", "            \"category\": \"Poultry\",\n", "            \"emoji\": \"🐔\",\n", "        },\n", "        {\n", "            \"title\": \"Vegan Soy Meat Crumbles\",\n", "            \"price\": \"$4.50\",\n", "            \"description\": \"Made from soy protein, these crumbles replicate the texture and flavor of traditional meat.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🥩\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON>o <PERSON>\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"A green, aromatic sauce made from basil, pine nuts, and garlic.\",\n", "            \"category\": \"Canned Goods\",\n", "            \"emoji\": \"🌿\",\n", "        },\n", "        {\n", "            \"title\": \"Marinara Sauce\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"A classic Italian tomato sauce with garlic, onions, and herbs.\",\n", "            \"category\": \"Canned Goods\",\n", "            \"emoji\": \"🍅\",\n", "        },\n", "        {\n", "            \"title\": \"Bolognese Sauce\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"A meat-based sauce simmered with tomatoes, onions, celery, and carrots.\",\n", "            \"category\": \"Canned Goods\",\n", "            \"emoji\": \"🍖🍅🧅🥕\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"A spicy tomato sauce made with red chili peppers.\",\n", "            \"category\": \"Canned Goods\",\n", "            \"emoji\": \"🌶️🍅\",\n", "        },\n", "        {\n", "            \"title\": \"Provolone Cheese\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"Semi-hard cheese with a smooth texture, it melts beautifully in dishes.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Cheddar Cheese\",\n", "            \"price\": \"$3.00\",\n", "            \"description\": \"A popular cheese with a sharp and tangy flavor profile.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Gouda Cheese\",\n", "            \"price\": \"$4.50\",\n", "            \"description\": \"A Dutch cheese known for its rich and creamy texture.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON>ina <PERSON>eese\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"A semi-soft cheese with a strong flavor, great for melting.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Vegan <PERSON>\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"Dairy-free alternative made from nuts or soy, melts similarly to regular mozzarella.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Cottage Cheese\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"A lighter alternative to ricotta, with small curds that provide a similar texture.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Goat Cheese\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"A tangy and creamy cheese that can provide a unique flavor to lasagna.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Mascarpone Cheese\",\n", "            \"price\": \"$4.50\",\n", "            \"description\": \"An Italian cream cheese with a rich and creamy texture.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Tofu Ricotta\",\n", "            \"price\": \"$3.00\",\n", "            \"description\": \"A vegan alternative made from crumbled tofu seasoned with herbs.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🌱\",\n", "        },\n", "        {\n", "            \"title\": \"Feta Cheese\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"A crumbly cheese with a salty profile, it can bring a Mediterranean twist to the dish.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Parmesan cheese\",\n", "            \"price\": \"$4.00\",\n", "            \"description\": \"A hard, granular cheese originating from Italy, known for its rich umami flavor.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON><PERSON><PERSON>\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"A salty, hard cheese made from sheep's milk, perfect for grating over dishes.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Asiago Cheese\",\n", "            \"price\": \"$4.50\",\n", "            \"description\": \"Semi-hard cheese with a nutty flavor, great for shaving or grating.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Grana Padano\",\n", "            \"price\": \"$5.50\",\n", "            \"description\": \"A grainy, hard cheese that's similar to Parmesan but milder in flavor.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Manchego Cheese\",\n", "            \"price\": \"$6.00\",\n", "            \"description\": \"A Spanish hard cheese with a rich and nutty flavor.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🧀\",\n", "        },\n", "        {\n", "            \"title\": \"Eggs\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"Rich in protein and versatile, eggs are used in a variety of culinary applications.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🥚\",\n", "        },\n", "        {\n", "            \"title\": \"Tofu\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"Blended silken tofu can act as a binder in various dishes.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🍲\",\n", "        },\n", "        {\n", "            \"title\": \"Flaxseed Meal\",\n", "            \"price\": \"$1.50\",\n", "            \"description\": \"Mix with water to create a gel-like consistency that can replace eggs.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🥚\",\n", "        },\n", "        {\n", "            \"title\": \"Chia Seeds\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"Mix with water to form a gel that can be used as an egg substitute.\",\n", "            \"category\": \"Vegan\",\n", "            \"emoji\": \"🥚\",\n", "        },\n", "        {\n", "            \"title\": \"Apple Sauce\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"A sweet alternative that can replace eggs in certain recipes.\",\n", "            \"category\": \"Baking\",\n", "            \"emoji\": \"🥚\",\n", "        },\n", "        {\n", "            \"title\": \"Onion\",\n", "            \"price\": \"$1.00\",\n", "            \"description\": \"A kitchen staple, onions provide depth and flavor to a myriad of dishes.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🧅\",\n", "        },\n", "        {\n", "            \"title\": \"Shallots\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"Milder and sweeter than regular onions, they add a delicate flavor.\",\n", "            \"category\": \"Produce\",\n", "            \"emoji\": \"🧅\",\n", "        },\n", "        {\n", "            \"title\": \"Green Onions\",\n", "            \"price\": \"$1.50\",\n", "            \"description\": \"Milder in flavor, green onions or scallions are great for garnishing.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🌱\",\n", "        },\n", "        {\n", "            \"title\": \"Red Onion\",\n", "            \"price\": \"$1.20\",\n", "            \"description\": \"Sweeter and more vibrant in color, red onions add a pop to dishes.\",\n", "            \"category\": \"Vegetables\",\n", "            \"emoji\": \"🔴\",\n", "        },\n", "        {\n", "            \"title\": \"Leeks\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"With a light onion flavor, leeks are great when sautéed or used in soups.\",\n", "            \"category\": \"Produce\",\n", "            \"emoji\": \"🍲\",\n", "        },\n", "        {\n", "            \"title\": \"Gar<PERSON>\",\n", "            \"price\": \"$0.50\",\n", "            \"description\": \"Aromatic and flavorful, garlic is a foundational ingredient in many cuisines.\",\n", "            \"category\": \"Produce\",\n", "            \"emoji\": \"🧄\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON><PERSON>\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"A convenient dried version of garlic that provides a milder flavor.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🧄\",\n", "        },\n", "        {\n", "            \"title\": \"Garlic Flakes\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"Dried garlic flakes can be rehydrated or used as they are for a burst of garlic flavor.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🧄\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON><PERSON><PERSON> Paste\",\n", "            \"price\": \"$3.00\",\n", "            \"description\": \"A smooth blend of garlic, perfect for adding to sauces or marinades.\",\n", "            \"category\": \"Condiments\",\n", "            \"emoji\": \"🧄\",\n", "        },\n", "        {\n", "            \"title\": \"Olive Oil\",\n", "            \"price\": \"$6.00\",\n", "            \"description\": \"A staple in Mediterranean cuisine, olive oil is known for its heart-healthy properties.\",\n", "            \"category\": \"Condiments\",\n", "            \"emoji\": \"🍽️\",\n", "        },\n", "        {\n", "            \"title\": \"Canola Oil\",\n", "            \"price\": \"$3.50\",\n", "            \"description\": \"A neutral-tasting oil suitable for various cooking methods.\",\n", "            \"category\": \"Condiments\",\n", "            \"emoji\": \"🍳\",\n", "        },\n", "        {\n", "            \"title\": \"Coconut Oil\",\n", "            \"price\": \"$5.00\",\n", "            \"description\": \"A fragrant oil ideal for sautéing and baking.\",\n", "            \"category\": \"Condiments\",\n", "            \"emoji\": \"🍳\",\n", "        },\n", "        {\n", "            \"title\": \"Avocado Oil\",\n", "            \"price\": \"$7.00\",\n", "            \"description\": \"Known for its high smoke point, it's great for high-heat cooking.\",\n", "            \"category\": \"Condiments\",\n", "            \"emoji\": \"🍳\",\n", "        },\n", "        {\n", "            \"title\": \"Grapeseed Oil\",\n", "            \"price\": \"$6.50\",\n", "            \"description\": \"A light, neutral oil that's good for dressings and sautéing.\",\n", "            \"category\": \"Condiments\",\n", "            \"emoji\": \"🥗\",\n", "        },\n", "        {\n", "            \"title\": \"Salt\",\n", "            \"price\": \"$1.00\",\n", "            \"description\": \"An essential seasoning that enhances the flavor of dishes.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🧂\",\n", "        },\n", "        {\n", "            \"title\": \"Himalayan Pink Salt\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"A natural and unrefined salt with a slightly earthy flavor.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🧂\",\n", "        },\n", "        {\n", "            \"title\": \"Sea Salt\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"Derived from evaporated seawater, it provides a briny touch.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🌊\",\n", "        },\n", "        {\n", "            \"title\": \"Kosher Salt\",\n", "            \"price\": \"$1.50\",\n", "            \"description\": \"A coarse salt without additives, commonly used in cooking.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🧂\",\n", "        },\n", "        {\n", "            \"title\": \"Black Salt (<PERSON><PERSON>)\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"A sulfurous salt often used in South Asian cuisine, especially vegan dishes to mimic an eggy flavor.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🧂\",\n", "        },\n", "        {\n", "            \"title\": \"Black Pepper\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"A versatile spice known for its sharp and mildly spicy flavor.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🌶️\",\n", "        },\n", "        {\n", "            \"title\": \"White Pepper\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"Milder than black pepper, it's often used in light-colored dishes.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🌶️\",\n", "        },\n", "        {\n", "            \"title\": \"Cayenne Pepper\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"A spicy chili pepper, ground into powder. Adds heat to dishes.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🌶️\",\n", "        },\n", "        {\n", "            \"title\": \"Crushed Red Pepper Flakes\",\n", "            \"price\": \"$1.50\",\n", "            \"description\": \"Adds a spicy kick to dishes, commonly used as a pizza topping.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🌶️\",\n", "        },\n", "        {\n", "            \"title\": \"Sichuan (or Szechuan) Peppercorns\",\n", "            \"price\": \"$3.00\",\n", "            \"description\": \"Known for their unique tingling sensation, they're used in Chinese cuisine.\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🥡\",\n", "        },\n", "        {\n", "            \"title\": \"Banana\",\n", "            \"price\": \"$0.60\",\n", "            \"description\": \"A sweet and portable fruit, packed with essential vitamins.\",\n", "            \"category\": \"Produce\",\n", "            \"emoji\": \"🍌\",\n", "        },\n", "        {\n", "            \"title\": \"Milk\",\n", "            \"price\": \"$2.50\",\n", "            \"description\": \"A calcium-rich dairy product, perfect for drinking or cooking.\",\n", "            \"category\": \"Dairy\",\n", "            \"emoji\": \"🥛\",\n", "        },\n", "        {\n", "            \"title\": \"Bread\",\n", "            \"price\": \"$2.00\",\n", "            \"description\": \"Freshly baked, perfect for sandwiches or toast.\",\n", "            \"category\": \"Bakery\",\n", "            \"emoji\": \"🍞\",\n", "        },\n", "        {\n", "            \"title\": \"Apple\",\n", "            \"price\": \"$1.00\",\n", "            \"description\": \"Crisp and juicy, great for snacking or baking.\",\n", "            \"category\": \"Produce\",\n", "            \"emoji\": \"🍏\",\n", "        },\n", "        {\n", "            \"title\": \"Orange\",\n", "            \"price\": \"3.99$\",\n", "            \"description\": \"Great as a juice and vitamin\",\n", "            \"category\": \"Produce\",\n", "            \"emoji\": \"🍊\",\n", "        },\n", "        {\n", "            \"title\": \"<PERSON>\",\n", "            \"price\": \"1.00\",\n", "            \"description\": \"very sweet substance\",\n", "            \"category\": \"Spices\",\n", "            \"emoji\": \"🍰\",\n", "        },\n", "    ]\n", "}\n", "\n", "insert_data = []\n", "\n", "for product in dataset[\"train\"]:\n", "    doc_product = json_util.loads(json_util.dumps(product))\n", "    haystack_doc = Document(content=doc_product[\"title\"], meta=doc_product)\n", "    insert_data.append(haystack_doc)\n", "\n", "\n", "document_store = MongoDBAtlasDocumentStore(\n", "    database_name=\"ai_shop\",\n", "    collection_name=\"test_collection\",\n", "    vector_search_index=\"vector_index\",\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "3MMitwR3P0uj"}, "source": ["Build the writer pipeline to load documnets"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dYEo2ZkMQptv", "outputId": "f832857c-c636-4b39-92f4-d1d9be5a294e"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating embeddings: 100%|██████████| 3/3 [00:01<00:00,  2.36it/s]\n"]}, {"data": {"text/plain": ["{'doc_embedder': {'meta': {'model': 'text-embedding-3-small',\n", "   'usage': {'prompt_tokens': 1456, 'total_tokens': 1456}}},\n", " 'doc_writer': {'documents_written': 81}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Setting up a document writer to handle the insertion of documents into the MongoDB collection.\n", "doc_writer = DocumentWriter(document_store=document_store, policy=DuplicatePolicy.SKIP)\n", "\n", "# Initializing a document embedder to convert text content into vectorized form.\n", "doc_embedder = OpenAIDocumentEmbedder(\n", "    model=\"text-embedding-3-small\", meta_fields_to_embed=[\"description\"]\n", ")\n", "\n", "# Creating a pipeline for indexing documents. The pipeline includes embedding and writing documents.\n", "indexing_pipe = Pipeline()\n", "indexing_pipe.add_component(instance=doc_embedder, name=\"doc_embedder\")\n", "indexing_pipe.add_component(instance=doc_writer, name=\"doc_writer\")\n", "\n", "# Connecting the components of the pipeline for document flow.\n", "indexing_pipe.connect(\"doc_embedder.documents\", \"doc_writer.documents\")\n", "\n", "# Running the pipeline with the list of documents to index them in MongoDB.\n", "indexing_pipe.run({\"doc_embedder\": {\"documents\": insert_data}})"]}, {"cell_type": "markdown", "metadata": {"id": "fJhXHzeyODGV"}, "source": ["## Build a Pipeline to have\n", "\n", "First lets add prices to the augmenting considerations by enhancing our prompt template with Price: `{{ doc.meta['price']}}`"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LaPV1fkJODGV", "outputId": "d8bcdb3f-573e-4f88-a130-98260adf342e"}, "outputs": [{"data": {"text/plain": ["<haystack.core.pipeline.pipeline.Pipeline object at 0x7d71d81b9ea0>\n", "🚅 Components\n", "  - text_embedder: OpenAITextEmbedder\n", "  - retriever: MongoDBAtlasEmbeddingRetriever\n", "  - prompt_builder: PromptBuilder\n", "  - llm: OpenAIGenerator\n", "🛤️ Connections\n", "  - text_embedder.embedding -> retriever.query_embedding (List[float])\n", "  - retriever.documents -> prompt_builder.documents (List[Document])\n", "  - prompt_builder.prompt -> llm.prompt (str)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Template for generating prompts for a movie recommendation engine.\n", "prompt_template = \"\"\"\n", "    You are a recipe builder assistant. Below you have a list of ingredients followed by its price for each ingredient.\n", "    Based on the requested food, provide a step by step recipe, followed by an itemized and total shopping list cost.\n", "\n", "    Your recipe should have the following sections:\n", "    - Ingredients\n", "    - Steps\n", "    - Cost\n", "\n", "    {% for doc in documents %}\n", "        Ingredient: {{ doc.content }}\n", "        Price: {{ doc.meta['price']}}\n", "    {% endfor %}\n", "\n", "    Query: {{query}}\n", "\n", "    Recipe:\n", "\"\"\"\n", "\n", "# Setting up a retrieval-augmented generation (RAG) pipeline for generating responses.\n", "rag_pipeline = Pipeline()\n", "rag_pipeline.add_component(\n", "    \"text_embedder\", OpenAITextEmbedder(model=\"text-embedding-3-small\")\n", ")\n", "\n", "# Adding a component for retrieving related documents from MongoDB based on the query embedding.\n", "rag_pipeline.add_component(\n", "    instance=MongoDBAtlasEmbeddingRetriever(document_store=document_store, top_k=50),\n", "    name=\"retriever\",\n", ")\n", "\n", "# Building prompts based on retrieved documents to be used for generating responses.\n", "rag_pipeline.add_component(\n", "    instance=PromptBuilder(template=prompt_template), name=\"prompt_builder\"\n", ")\n", "\n", "# Adding a language model generator to produce the final text output.\n", "rag_pipeline.add_component(instance=OpenAIGenerator(model=\"gpt-4o\"), name=\"llm\")\n", "\n", "# Connecting the components of the RAG pipeline to ensure proper data flow.\n", "rag_pipeline.connect(\"text_embedder.embedding\", \"retriever.query_embedding\")\n", "rag_pipeline.connect(\"retriever.documents\", \"prompt_builder.documents\")\n", "rag_pipeline.connect(\"prompt_builder\", \"llm\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qizRPuagODGV", "outputId": "3bdd65d0-156f-429d-fbaf-8ee9175cff3c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sure! Let's create a delicious lasagna recipe for you. We will use common lasagna ingredients for a classic lasagna recipe with a bit of veggie twist. Here is the recipe:\n", "\n", "### Classic Veggie Lasagna Recipe\n", "\n", "#### Ingredients:\n", "- Whole Wheat Lasagna Sheets – $3.00\n", "- <PERSON><PERSON> – $3.50\n", "- Tofu Ricotta – $3.00\n", "- <PERSON><PERSON><PERSON><PERSON> Slices – $2.50\n", "- Spinach – $2.00\n", "- <PERSON><PERSON><PERSON> – $4.00\n", "- <PERSON><PERSON><PERSON> – $3.00\n", "- Bell Peppers – $2.50\n", "- Cottage Cheese – $2.50\n", "\n", "#### Steps:\n", "1. **Prepare the Vegetables:**\n", "   - Preheat your oven to 375°F (190°C).\n", "   - Slice the zucchini and bell peppers thinly.\n", "   - In a skillet, sauté the zucchini slices, bell peppers, and garlic paste over medium heat until they are tender.\n", "   \n", "2. **Prepare the Spinach:**\n", "   - Wash the spinach thoroughly.\n", "   - In a separate pan, sauté the spinach in a little water until wilted. Drain any excess water.\n", "   \n", "3. **Cook the Lasagna Sheets:**\n", "   - Bring a large pot of salted water to a boil.\n", "   - Cook the whole wheat lasagna sheets according to the package instructions until they are al dente.\n", "   - <PERSON><PERSON> and lay them flat on a clean surface to prevent sticking.\n", "\n", "4. **Layer the Lasagna:**\n", "   - Spread a thin layer of marinara sauce on the bottom of a baking dish.\n", "   - Place a layer of lasagna sheets over the sauce.\n", "   - Spread a generous layer of tofu ricotta over the lasagna sheets.\n", "   - Add a layer of sautéed vegetables (zucchini, bell peppers, garlic) and wilted spinach.\n", "   - Sprinkle cottage cheese on top of the veggies.\n", "   - Add another layer of marinara sauce and repeat the layers.\n", "   - Finish with a final layer of lasagna sheets, a generous spread of marinara sauce, and a final sprinkle of parmesan cheese.\n", "   \n", "5. **Bake the Lasagna:**\n", "   - Cover the baking dish with aluminum foil.\n", "   - Bake in the preheated oven for 25 minutes.\n", "   - Remove the foil and bake for an additional 15 minutes or until the top is golden and bubbly.\n", "   \n", "6. **Let it Cool:**\n", "   - Remove the lasagna from the oven and let it rest for about 10 minutes before slicing and serving.\n", "\n", "#### Cost:\n", "1. Whole Wheat Lasagna Sheets: $3.00\n", "2. <PERSON><PERSON>: $3.50\n", "3. Tofu <PERSON>: $3.00\n", "4. <PERSON><PERSON><PERSON><PERSON>: $2.50\n", "5. <PERSON><PERSON>: $2.00\n", "6. <PERSON><PERSON><PERSON>eese: $4.00\n", "7. <PERSON><PERSON><PERSON>: $3.00\n", "8. Bell Peppers: $2.50\n", "9. Cottage Cheese: $2.50\n", "\n", "**Total Cost:** $26.00\n", "\n", "Enjoy your homemade classic veggie lasagna! This recipe is perfect for a family dinner or meal prep for the week.\n"]}], "source": ["query = \"How can I cook a lasagne?\"\n", "result = rag_pipeline.run(\n", "    {\n", "        \"text_embedder\": {\"text\": query},\n", "        \"prompt_builder\": {\"query\": query},\n", "    },\n", "    include_outputs_from=[\"prompt_builder\"],\n", ")\n", "print(result[\"llm\"][\"replies\"][0])"]}, {"cell_type": "markdown", "metadata": {"id": "KlHHqk_0ODGW"}, "source": ["## Make it cheaper with self-reflection!\n", "\n", "Here the agentic workflow is built around self reflection of the LLM to reconsider the suggested set of ingridiants in order to find the cheapest reciepe possible."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nkj7qDRgODGW", "outputId": "58bb274e-7278-4696-adac-d542d18f29d0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting colorama\n", "  Downloading colorama-0.4.6-py2.py3-none-any.whl (25 kB)\n", "Installing collected packages: colorama\n", "Successfully installed colorama-0.4.6\n"]}], "source": ["!pip install colorama"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "62U64CHHODGW"}, "outputs": [], "source": ["from typing import List\n", "\n", "from colorama import Fore\n", "from haystack import component\n", "\n", "\n", "@component\n", "class RecipeChecker:\n", "    @component.output_types(recipe_to_check=str, recipe=str)\n", "    def run(self, replies: List[str]):\n", "        if \"DONE\" in replies[0]:\n", "            return {\"recipe\": replies[0].replace(\"done\", \"\")}\n", "        print(Fore.RED + \"Not done yet, could make recipe more efficient\")\n", "        return {\"recipe_to_check\": replies[0]}"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JwBITphFODGW", "outputId": "34007c9b-af15-4f2e-a0db-d00841a82cb8"}, "outputs": [{"data": {"text/plain": ["<haystack.core.pipeline.pipeline.Pipeline object at 0x7d71d3d01450>\n", "🚅 Components\n", "  - text_embedder: OpenAITextEmbedder\n", "  - retriever: MongoDBAtlasEmbeddingRetriever\n", "  - prompt_builder: PromptBuilder\n", "  - checker: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "  - llm: OpenAIGenerator\n", "🛤️ Connections\n", "  - text_embedder.embedding -> retriever.query_embedding (List[float])\n", "  - retriever.documents -> prompt_builder.documents (List[Document])\n", "  - prompt_builder.prompt -> llm.prompt (str)\n", "  - checker.recipe_to_check -> prompt_builder.recipe_to_check (str)\n", "  - llm.replies -> checker.replies (List[str])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Template for generating prompts for a movie recommendation engine.\n", "prompt_template = \"\"\"\n", "    You are a recipe builder assistant. Below you have a list of ingredients followed by its price for each ingredient.\n", "    Based on the requested food, provide a step by step recipe, followed by an itemized and total shopping list cost.\n", "\n", "    Your recipe should have the following sections:\n", "    - Ingredients\n", "    - Steps\n", "    - Cost\n", "\n", "    {% for doc in documents %}\n", "        Ingredient: {{ doc.content }}\n", "        Price: {{ doc.meta['price']}}\n", "    {% endfor %}\n", "\n", "    Query: {{query}}\n", "    {% if recipe_to_check %}\n", "        Here is the recipe you previously generated: {{recipe_to_check[0]}}\n", "        Is this the most efficient and cheap way to do this recipe?\n", "        If yes, say '<PERSON>ON<PERSON>' and return the recipe s in the next line\n", "        If not, say 'incomplete' and return the recipe in the next line\n", "    {% endif %}\n", "    \\nRecipe:\n", "\"\"\"\n", "\n", "reflecting_rag_pipeline = Pipeline(max_loops_allowed=5)\n", "reflecting_rag_pipeline.add_component(\n", "    \"text_embedder\", OpenAITextEmbedder(model=\"text-embedding-3-small\")\n", ")\n", "reflecting_rag_pipeline.add_component(\n", "    instance=MongoDBAtlasEmbeddingRetriever(document_store=document_store, top_k=50),\n", "    name=\"retriever\",\n", ")\n", "reflecting_rag_pipeline.add_component(\n", "    instance=PromptBuilder(template=prompt_template), name=\"prompt_builder\"\n", ")\n", "reflecting_rag_pipeline.add_component(instance=RecipeChecker(), name=\"checker\")\n", "reflecting_rag_pipeline.add_component(\n", "    instance=OpenAIGenerator(model=\"gpt-4o\"), name=\"llm\"\n", ")\n", "\n", "reflecting_rag_pipeline.connect(\"text_embedder.embedding\", \"retriever.query_embedding\")\n", "reflecting_rag_pipeline.connect(\"retriever.documents\", \"prompt_builder.documents\")\n", "reflecting_rag_pipeline.connect(\n", "    \"checker.recipe_to_check\", \"prompt_builder.recipe_to_check\"\n", ")\n", "reflecting_rag_pipeline.connect(\"prompt_builder\", \"llm\")\n", "reflecting_rag_pipeline.connect(\"llm\", \"checker\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ztOtX5ghODGW", "outputId": "0b42fcd3-203e-4db0-820e-08932d02a009"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["reflecting_rag_pipeline.show()"]}, {"cell_type": "markdown", "metadata": {"id": "2EcOV1tsODGW"}, "source": ["As you can see the pipeline will loop through itself to find a more efficient reciepe."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1cLI1t1pODGW", "outputId": "b08f4cd6-bad4-46fa-8b4c-da19cc9ae958"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31mNot done yet, could make recipe more efficient\n", "\u001b[31mNot done yet, could make recipe more efficient\n", "\u001b[31mNot done yet, could make recipe more efficient\n", "\u001b[32mIt appears that the previously generated recipe was labeled as \"B,\" but without seeing the content of recipe B, I can't tell whether it's the most efficient and cheap way to cook lasagna. Therefore, I will provide a new lasagna recipe that aims to balance cost efficiency and deliciousness.\n", "\n", "Let's create a simple yet flavorful lasagna using some of the ingredients you have listed. We'll go for a classic vegetarian lasagna, which tends to be slightly more cost-effective than one containing meat.\n", "\n", "### Vegetarian Lasagna Recipe\n", "\n", "#### Ingredients\n", "1. Whole Wheat Lasagna Sheets - $3.00\n", "2. <PERSON><PERSON> - $3.50\n", "3. <PERSON> Cheese - $2.50\n", "4. <PERSON><PERSON> - $2.00\n", "5. <PERSON><PERSON><PERSON><PERSON> - $2.50\n", "6. <PERSON><PERSON><PERSON> - $4.00\n", "7. <PERSON><PERSON><PERSON> - $3.00\n", "\n", "#### Steps\n", "1. **Preheat Oven**: Preheat your oven to 375°F (190°C).\n", "\n", "2. **Prepare Noodles**: Cook the whole wheat lasagna sheets according to the package instructions. Once cooked, drain and set aside.\n", "\n", "3. **Prepare Veggies**: Sauté the zucchini slices and spinach over medium heat in a pan with a bit of garlic paste until tender. Set aside.\n", "\n", "4. **Layering**: In a baking dish, start by spreading a thin layer of tomato basil sauce.\n", "\n", "5. **First Layer**: Place a layer of lasagna sheets on top of the sauce.\n", "\n", "6. **Second Layer**: Spread a layer of cottage cheese over the lasagna sheets, followed by some sautéed zucchini and spinach.\n", "\n", "7. **Top with Sauce**: Pour more tomato basil sauce over the veggies.\n", "\n", "8. **Repeat Layers**: Repeat the layering process until you run out of ingredients, making sure the top layer is lasagna sheets covered with the remaining tomato basil sauce.\n", "\n", "9. **Add Cheese**: Sprinkle Parmesan cheese over the top layer of sauce.\n", "\n", "10. **Bake**: Cover the baking dish with aluminum foil and bake in the preheated oven for 25 minutes. Remove the foil and bake for an additional 15 minutes until the top is bubbly and slightly browned.\n", "\n", "11. **Rest and Serve**: Let the lasagna rest for about 10 minutes before slicing and serving.\n", "\n", "#### Cost\n", "- Whole Wheat Lasagna Sheets: $3.00\n", "- <PERSON><PERSON>: $3.50\n", "- Cottage Cheese: $2.50\n", "- Spinach: $2.00\n", "- Zucchini Slices: $2.50\n", "- Pa<PERSON>esan Cheese: $4.00\n", "- <PERSON><PERSON><PERSON>: $3.00\n", "\n", "**Total Cost**: $20.50\n", "\n", "This recipe is both cost-efficient and straightforward, utilizing simple and readily available ingredients to make a delicious vegetarian lasagna. If this meets your requirements, say ',' and if you need adjustments, you can say 'incomplete' and request modifications.\n"]}], "source": ["query = \"How can I cook a lasagne?\"\n", "result = reflecting_rag_pipeline.run(\n", "    {\"text_embedder\": {\"text\": query}, \"prompt_builder\": {\"query\": query}}\n", ")\n", "print(Fore.GREEN + result[\"checker\"][\"recipe\"])"]}, {"cell_type": "markdown", "metadata": {"id": "bJOKP5-qODGW"}, "source": ["## Use JSON format output\n", "\n", "Developers will usually prefer dealing with a JSON format output from LLMs when building applications, as well the ease of storing JSON objects in MongoDB Atlas for fututre store and use."]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iOFwSLjhODGW", "outputId": "2950934f-53bd-466b-b2db-5808d56f15cf"}, "outputs": [{"data": {"text/plain": ["<haystack.core.pipeline.pipeline.Pipeline object at 0x7d71d3c83460>\n", "🚅 Components\n", "  - text_embedder: OpenAITextEmbedder\n", "  - retriever: MongoDBAtlasEmbeddingRetriever\n", "  - prompt_builder: PromptBuilder\n", "  - checker: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "  - llm: OpenAIGenerator\n", "🛤️ Connections\n", "  - text_embedder.embedding -> retriever.query_embedding (List[float])\n", "  - retriever.documents -> prompt_builder.documents (List[Document])\n", "  - prompt_builder.prompt -> llm.prompt (str)\n", "  - checker.recipe_to_check -> prompt_builder.recipe_to_check (str)\n", "  - llm.replies -> checker.replies (List[str])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt_template = \"\"\"\n", "    You are a recipe builder assistant. Below you have a list of ingredients followed by its price for each ingredient.\n", "    Respond in JSON format to include only relevant reciepe data, it must have all the markdown under 'markdown_text' field, checker_status : ..., 'ingridiants' : []\n", "    Based on the requested food, provide a step by step recipe, followed by an itemized and total shopping list cost.\n", "\n", "    Your recipe should have the following sections:\n", "    - Ingredients\n", "    - Steps\n", "    - Cost\n", "\n", "    {% for doc in documents %}\n", "        Ingredient: {{ doc.content }}\n", "        Price: {{ doc.meta['price']}}\n", "    {% endfor %}\n", "\n", "    Query: {{query}}\n", "    {% if recipe_to_check %}\n", "        Here is the recipe you previously generated: {{recipe_to_check[0]}}\n", "        Is this the most efficient and cheap way to do this recipe?\n", "        If yes, say 'checker_status' : 'DONE' and return the recipe s in the next line\n", "        If not, say 'incomplete' and return the recipe in the next line\n", "    {% endif %}\n", "    \\nRecipe:\n", "\"\"\"\n", "\n", "reflecting_rag_pipeline = Pipeline(max_loops_allowed=10)\n", "reflecting_rag_pipeline.add_component(\n", "    \"text_embedder\", OpenAITextEmbedder(model=\"text-embedding-3-small\")\n", ")\n", "reflecting_rag_pipeline.add_component(\n", "    instance=MongoDBAtlasEmbeddingRetriever(document_store=document_store, top_k=50),\n", "    name=\"retriever\",\n", ")\n", "reflecting_rag_pipeline.add_component(\n", "    instance=PromptBuilder(template=prompt_template), name=\"prompt_builder\"\n", ")\n", "reflecting_rag_pipeline.add_component(instance=RecipeChecker(), name=\"checker\")\n", "reflecting_rag_pipeline.add_component(\n", "    instance=OpenAIGenerator(\n", "        model=\"gpt-4o\",\n", "        generation_kwargs={\n", "            \"response_format\": {\"type\": \"json_object\"},\n", "            \"temperature\": 0,\n", "        },\n", "    ),\n", "    name=\"llm\",\n", ")\n", "\n", "reflecting_rag_pipeline.connect(\"text_embedder.embedding\", \"retriever.query_embedding\")\n", "reflecting_rag_pipeline.connect(\"retriever.documents\", \"prompt_builder.documents\")\n", "reflecting_rag_pipeline.connect(\n", "    \"checker.recipe_to_check\", \"prompt_builder.recipe_to_check\"\n", ")\n", "reflecting_rag_pipeline.connect(\"prompt_builder\", \"llm\")\n", "reflecting_rag_pipeline.connect(\"llm\", \"checker\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uSNizRTnTKE_", "outputId": "f1b976ba-3541-47bc-f21f-532addb71922"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pymongo in /usr/local/lib/python3.10/dist-packages (4.8.0)\n", "Requirement already satisfied: dnspython<3.0.0,>=1.16.0 in /usr/local/lib/python3.10/dist-packages (from pymongo) (2.6.1)\n"]}], "source": ["!pip install pymongo"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "R1kpgR0ITD-7", "outputId": "1b46cf62-1de2-4204-e855-515f42fecf78"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31mNot done yet, could make recipe more efficient\n", "\u001b[32m{\n", "  \"markdown_text\": \"### Lasagna Recipe\\n\\n#### Ingredients\\n- 1 pack of Whole Wheat Lasagna Sheets ($3.00)\\n- 1 jar of Tomato Basil Sauce ($3.50)\\n- 1 pack of Tofu Ricotta ($3.00)\\n- 1 pack of Spinach ($2.00)\\n- 1 pack of Parmesan Cheese ($4.00)\\n- 1 pack of Zucchini Slices ($2.50)\\n\\n#### Steps\\n1. **Preheat Oven**: Preheat your oven to 375°F (190°C).\\n2. **Prepare Lasagna Sheets**: Cook the whole wheat lasagna sheets according to the package instructions. Drain and set aside.\\n3. **Prepare Tofu Ricotta**: In a bowl, mix the tofu ricotta with some salt and pepper to taste.\\n4. **Layering**: In a baking dish, spread a thin layer of tomato basil sauce. Place a layer of lasagna sheets on top. Spread a layer of tofu ricotta, followed by a layer of spinach and zucchini slices. Repeat the layers until all ingredients are used, ending with a layer of lasagna sheets.\\n5. **Top with Cheese**: Sprinkle the top layer with grated Parmesan cheese.\\n6. **Bake**: Cover the baking dish with aluminum foil and bake in the preheated oven for 25 minutes. Remove the foil and bake for an additional 20 minutes, or until the top is golden and bubbly.\\n7. **Serve**: Let the lasagna cool for a few minutes before slicing and serving.\\n\\n#### Cost\\n- Whole Wheat Lasagna Sheets: $3.00\\n- Tomato Basil Sauce: $3.50\\n- Tofu Ricotta: $3.00\\n- Spinach: $2.00\\n- Parmesan Cheese: $4.00\\n- Zucchini Slices: $2.50\\n\\n**Total Cost**: $18.00\",\n", "  \"checker_status\": \"DONE\",\n", "  \"ingredients\": [\n", "    {\n", "      \"name\": \"Whole Wheat Lasagna Sheets\",\n", "      \"price\": 3.00\n", "    },\n", "    {\n", "      \"name\": \"<PERSON><PERSON>\",\n", "      \"price\": 3.50\n", "    },\n", "    {\n", "      \"name\": \"Tofu Ricotta\",\n", "      \"price\": 3.00\n", "    },\n", "    {\n", "      \"name\": \"<PERSON>ach\",\n", "      \"price\": 2.00\n", "    },\n", "    {\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"price\": 4.00\n", "    },\n", "    {\n", "      \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n", "      \"price\": 2.50\n", "    }\n", "  ]\n", "}\n"]}, {"data": {"text/plain": ["InsertOneResult(ObjectId('6684f3d4829008e4fb597fbc'), acknowledged=True)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["import datetime\n", "import json\n", "\n", "from pymongo import MongoClient\n", "\n", "query = \"How can I cook a lasagne?\"\n", "result = reflecting_rag_pipeline.run(\n", "    {\"text_embedder\": {\"text\": query}, \"prompt_builder\": {\"query\": query}}\n", ")\n", "print(Fore.GREEN + result[\"checker\"][\"recipe\"])\n", "\n", "## Load json string output as json\n", "doc = json.loads(result[\"checker\"][\"recipe\"])\n", "\n", "doc[\"date\"] = datetime.datetime.now()\n", "\n", "# Insert JSON reciepe into MongoDB\n", "mongo_client = MongoClient(\n", "    os.environ[\"MONGO_CONNECTION_STRING\"],\n", "    appname=\"devrel.showcase.haystack_cooking_agent\",\n", ")\n", "db = mongo_client[\"ai_shop\"]\n", "collection = db[\"reciepes\"]\n", "collection.insert_one(doc)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 0}