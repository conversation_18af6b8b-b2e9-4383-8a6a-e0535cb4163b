{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/agents/mongodb_with_aws_bedrock_agent.ipynb)"]}, {"cell_type": "markdown", "metadata": {"id": "CmKeBSvBWIcS"}, "source": ["# MongoDB with Bedrock agent quick tutorial\n", "MongoDB Atlas and Amazon Bedrock have joined forces to streamline the development of generative AI applications through their seamless integration. MongoDB Atlas, a robust cloud-based database service, now offers native support for Amazon Bedrock, AWS's managed service for generative AI. This integration leverages Atlas's vector search capabilities, enabling the effective utilization of enterprise data to augment the foundational models provided by Bedrock, such as Anthropic's Claude and Amazon's Titan. The combination ensures that the generative AI models have access to the most relevant and up-to-date data, significantly improving the accuracy and reliability of AI-driven applications​ with [MongoDB](https://www.mongodb.com/developer/products/atlas/rag-workflow-with-atlas-amazon-bedrock/)​.\n", "\n", "This integration simplifies the workflow for developers aiming to implement retrieval-augmented generation (RAG). RAG helps mitigate the issue of hallucinations in AI models by allowing them to fetch and utilize specific data from a predefined knowledge base, in this case, MongoDB Atlas Developers can easily set up this workflow by creating a vector search index in Atlas, which stores the vector embeddings and metadata of the text data. This setup not only enhances the performance and reliability of AI applications but also ensures data privacy and security through features like AWS PrivateLink​​.\n", "\n", "This notebook demonstrates how to interact with a predefined agent using [AWS Bedrock](https://aws.amazon.com/bedrock/) in a Google Colab environment. It utilizes the `boto3` library to communicate with the AWS Bedrock service and allows you to input prompts and receive responses directly within the notebook.\n", "\n", "\n", "\n", "## Key Features:\n", "1. **Secure Handling of AWS Credentials**: The `getpass` module is used to securely enter your AWS Access Key and Secret Key.\n", "2. **Session Management**: Each session is assigned a random session ID to maintain continuity in conversations.\n", "3. **Agent Invocation**: The notebook sends user prompts to a predefined agent and streams the responses back to the user.\n", "\n", "### Requirements:\n", "- AWS Access Key and Secret Key with appropriate permissions.\n", "- Boto3 and Requests libraries for interacting with AWS services and fetching data from URLs.\n", "\n", "\n", "## Setting up MongoDB Atlas\n", "\n", "1. Follow the [getting started with Atlas](https://www.mongodb.com/docs/atlas/getting-started/) guide and setup your cluster with `0.0.0.0/0` allowed connection for this notebook.\n", "2. Predefined an Atlas Vector Index on database `bedrock` collection `agenda`, this collection will host the data for the AWS summit agenda and will serve as a context store for the agent:\n", "**Index name**: `vector_index`\n", "```json\n", "{\n", "  \"fields\": [\n", "    {\n", "      \"type\": \"vector\",\n", "      \"path\": \"embedding\",\n", "      \"numDimensions\": 1024,\n", "      \"similarity\": \"cosine\"\n", "    },\n", "    {\n", "      \"type\" : \"filter\",\n", "      \"path\" : \"metadata\"\n", "    },\n", "    {\n", "      \"type\" : \"filter\",\n", "      \"path\" : \"text\"\n", "    },\n", "  ]\n", "}\n", "```\n", "\n", "\n", "## Setup AWS Bedrock\n", "\n", "**We will use US-EAST-1 AWS region for this notebook**\n", "\n", "Follow our official tutorial to enable a bedrock knowledge base against the created database and collection in MongoDB Atlas. This [guide](https://www.mongodb.com/docs/atlas/atlas-vector-search/ai-integrations/amazon-bedrock/) highlight a detailed step of action to build the knowledge base and agent.\n", "\n", "For this notebook, we will perform the following tasks according to the guide:\n", "\n", "1. Go to the bedrock console and enable\n", "- Amazon Titan Text Embedding model (`amazon.titan-embed-text-v2:0`)\n", "- Claude 3 Sonnet Model (The LLM(\n", "\n", "2. Upload the following source data about the AWS summit agenda to your S3 bucket:\n", "- https://s3.amazonaws.com/bedrocklogs.pavel/ocr_db.aws_events.json\n", "- https://s3.amazonaws.com/bedrocklogs.pavel/ocr_db.aws_sessions.json\n", "\n", "This will be our source data listing the events happening in the summit.\n", "\n", "3. Go to Secrets Manager on the AWS console and create credentials to our atlas cluster via \"Other type of secret\":\n", "- key : username , value : `<ATLAS_USERNAME>`\n", "- key : password , value : `<ATLAS_PASSWORD>`\n", "\n", "4. Follow the setup of the knowledge base wizard to connect Bedrock models with Atlas :\n", "- Click \"Create Knowledge Base\" and input:\n", "\n", "|input|value|\n", "|---|---|\n", "|Name| `<NAME>` |\n", "|Chose| Create and use a new service role|\n", "|Data source name| `<NAME>`|\n", "|S3 URI| Browse for the S3 bucket hosting the 2 uploaded source files|\n", "|Embedding Model| Titan Text Embeddings v2|\n", "\n", "\n", "- let's choose MongoDB Atlas in the \"Vector Database\" choose the \"Choose a vector store you have created\" section:\n", "\n", "|input|value|\n", "|---|---|\n", "|Select your vector store| **MongoDB Atlas** |\n", "|Hostname| Your atlas srv hostname `eg. cluster0.abcd.mongodb.net`|\n", "|Database name| `bedrock`|\n", "|Collection name| `agenda`|\n", "|Credentials secret ARN| Copy the created credentials from the \"Secrets manager\"|\n", "|Vector search index name|`vector_index`|\n", "|Vector embedding field path| `embedding`|\n", "|Text field path| `text`|\n", "|Metadata field path| `metadata` |\n", "5. <PERSON><PERSON> Next, review the details and \"Create Knowledge Base\".\n", "\n", "6. Once the knowledge base is marked with \"Status : Ready\", go to `Data source` section, choose the one datasource we have and click the \"Sync\" button on its right upper corner. This operation should load the data to <PERSON> if everything was setup correctly.\n", "\n", "## Setting up an agenda agent\n", "\n", "We can now set up our agent, who will work with a set of instructions and our knowledge base.\n", "\n", "1. Go to the \"Agents\" tab in the bedrock UI.\n", "2. Click \"Create Agent\" and give it a meaningful name (e.g. agenda_assistant)\n", "3. Input the following data in the agent builder:\n", "\n", "|input|value|\n", "|---|---|\n", "|Agent Name| agenda_assistant |\n", "|Agent resource role| Create and use a new service role |\n", "|Select model| Anthropic - Claude 3 Sonnet |\n", "|Instructions for the Agent| **You are a friendly AI chatbot that helps users find and build agenda Items for AWS Summit Tel Aviv.  elaborate as much as possible on the response.** |\n", "|Agent Name| agenda_assistant |\n", "|Knowledge bases| **Choose your Knowledge Base** |\n", "|Aliases| Create a new <PERSON><PERSON>|\n", "\n", "And now, we have a functioning agent that can be tested via the console.\n", "Let's move to the notebook.\n", "\n", "**Take note of the Agent ID and create an Agent Alias ID for the notebook**"]}, {"cell_type": "markdown", "metadata": {"id": "NmjfN1HavIqF"}, "source": ["## Interacting with the agent\n", "\n", "To interact with the agent, we need to install the AWS python SDK:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6L8lkSTzvig1", "outputId": "0300d850-872d-47e0-aae1-caa5396f3db3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting boto3\n", "  Downloading boto3-1.34.129-py3-none-any.whl (139 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m139.2/139.2 kB\u001b[0m \u001b[31m1.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting botocore<1.35.0,>=1.34.129 (from boto3)\n", "  Downloading botocore-1.34.129-py3-none-any.whl (12.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.3/12.3 MB\u001b[0m \u001b[31m38.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jmespath<2.0.0,>=0.7.1 (from boto3)\n", "  Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)\n", "Collecting s3transfer<0.11.0,>=0.10.0 (from boto3)\n", "  Downloading s3transfer-0.10.1-py3-none-any.whl (82 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m82.2/82.2 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil<3.0.0,>=2.1 in /usr/local/lib/python3.10/dist-packages (from botocore<1.35.0,>=1.34.129->boto3) (2.8.2)\n", "Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /usr/local/lib/python3.10/dist-packages (from botocore<1.35.0,>=1.34.129->boto3) (2.0.7)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil<3.0.0,>=2.1->botocore<1.35.0,>=1.34.129->boto3) (1.16.0)\n", "Installing collected packages: jmespath, botocore, s3transfer, boto3\n", "Successfully installed boto3-1.34.129 botocore-1.34.129 jmespath-1.0.1 s3transfer-0.10.1\n"]}], "source": ["!pip install boto3"]}, {"cell_type": "markdown", "metadata": {"id": "vt-G0dpYvq78"}, "source": ["Let's place the credentials for our AWS account.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tKzzqSX4v3tp", "outputId": "86ed2e5c-28bb-4b69-99b5-919f8cfdfc49"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your AWS Access Key: ··········\n", "Enter your AWS Secret Key: ··········\n"]}], "source": ["import getpass\n", "import random\n", "\n", "import boto3\n", "\n", "# Get AWS credentials from user\n", "aws_access_key = getpass.getpass(\"Enter your AWS Access Key: \")\n", "aws_secret_key = getpass.getpass(\"Enter your AWS Secret Key: \")"]}, {"cell_type": "markdown", "metadata": {"id": "sjT3QKaVwnI6"}, "source": ["Now, we need to initialise the boto3 client and get the agent ID and alias ID input.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cJt6aaxpw1e4", "outputId": "7ed4315a-0352-46d7-ffe8-af66ba7c5a4b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your agent ID··········\n", "Enter your agent Alias ID··········\n"]}], "source": ["bedrock_agent_runtime = boto3.client(\n", "    \"bedrock-agent-runtime\",\n", "    aws_access_key_id=aws_access_key,\n", "    aws_secret_access_key=aws_secret_key,\n", "    region_name=\"us-east-1\",\n", ")\n", "\n", "# Define agent IDs (replace these with your actual agent IDs)\n", "agent_id = getpass.getpass(\"Enter your agent ID\")\n", "agent_alias_id = getpass.getpass(\"Enter your agent <PERSON>as ID\")"]}, {"cell_type": "markdown", "metadata": {"id": "srUoCSPwxIIz"}, "source": ["Let's build the helper function to interact with the agent.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-p1eClRQxL8x"}, "outputs": [], "source": ["def randomise_session_id():\n", "    \"\"\"\n", "    Generate a random session ID.\n", "\n", "    Returns:\n", "        str: A random session ID.\n", "    \"\"\"\n", "    return str(random.randint(1000, 9999))\n", "\n", "\n", "def data_stream_generator(response):\n", "    \"\"\"\n", "    Generator to yield data chunks from the response.\n", "\n", "    Args:\n", "        response (dict): The response dictionary.\n", "\n", "    Yields:\n", "        str: The next chunk of data.\n", "    \"\"\"\n", "    for event in response[\"completion\"]:\n", "        chunk = event.get(\"chunk\", {})\n", "        if \"bytes\" in chunk:\n", "            yield chunk[\"bytes\"].decode()\n", "\n", "\n", "def invoke_agent(bedrock_agent_runtime, agent_id, agent_alias_id, session_id, prompt):\n", "    \"\"\"\n", "    Sends a prompt for the agent to process and respond to, streaming the response data.\n", "\n", "    Args:\n", "        bedrock_agent_runtime (boto3 client): The runtime client to invoke the agent.\n", "        agent_id (str): The unique identifier of the agent to use.\n", "        agent_alias_id (str): The alias of the agent to use.\n", "        session_id (str): The unique identifier of the session. Use the same value across requests to continue the same conversation.\n", "        prompt (str): The prompt that you want the agent to complete.\n", "\n", "    Returns:\n", "        str: The response from the agent.\n", "    \"\"\"\n", "    try:\n", "        response = bedrock_agent_runtime.invoke_agent(\n", "            agentId=agent_id,\n", "            agentAliasId=agent_alias_id,\n", "            sessionId=session_id,\n", "            inputText=prompt,\n", "        )\n", "\n", "        # Use the data stream generator to stream the response\n", "        ret_response = \"\".join(data_stream_generator(response))\n", "\n", "        return ret_response\n", "\n", "    except Exception as e:\n", "        return f\"Error invoking agent: {e}\""]}, {"cell_type": "markdown", "metadata": {"id": "Pu9vtHsPxUsm"}, "source": ["We can now interact with the agent using the application code."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sXs-omN5xYsk", "outputId": "d3f07de7-1b9c-4e16-a787-5cd47d64de83"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your prompt (or type 'exit' to quit): What agenda items are present in the AWS summit\n", "Agent Response:\n", "The AWS Summit agenda items include sessions on digital transformation, generative AI, multi-cloud management, machine learning, vector databases, and OpenSearch services. Other agenda items cover topics like scaling AI within organizations, application resilience with AWS, Amazon Q for GenAI, and leveraging LLM-based AI agents.\n", "Enter your prompt (or type 'exit' to quit): exit\n"]}], "source": ["# Initialize chat history and session ID\n", "session_id = randomise_session_id()\n", "\n", "while True:\n", "    prompt = input(\"Enter your prompt (or type 'exit' to quit): \")\n", "\n", "    if prompt.lower() == \"exit\":\n", "        break\n", "\n", "    response = invoke_agent(\n", "        bedrock_agent_runtime, agent_id, agent_alias_id, session_id, prompt\n", "    )\n", "\n", "    print(\"Agent Response:\")\n", "    print(response)"]}, {"cell_type": "markdown", "metadata": {"id": "4-SdBf5ox0KF"}, "source": ["Here you go! You have a powerful bedrock agent with MongoDB Atlas.\n", "\n", "Conclusions\n", "The integration of MongoDB Atlas with Amazon Bedrock represents a significant advancement in the development and deployment of generative AI applications. By leveraging Atlas's vector search capabilities and the powerful foundational models available through Bedrock, developers can create applications that are both highly accurate and deeply informed by enterprise data. This seamless integration facilitates the retrieval-augmented generation (RAG) workflow, enabling AI models to access and utilize the most relevant data, thereby reducing the likelihood of hallucinations and improving overall performance.\n", "\n", "The benefits of this integration extend beyond just technical enhancements. It also simplifies the generative AI stack, allowing companies to rapidly deploy scalable AI solutions with enhanced privacy and security features, such as those provided by AWS PrivateLink. This makes it an ideal solution for enterprises with stringent data security requirements. Overall, the combination of MongoDB Atlas and Amazon Bedrock provides a robust, efficient, and secure platform for building next-generation AI applications​ .\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 0}