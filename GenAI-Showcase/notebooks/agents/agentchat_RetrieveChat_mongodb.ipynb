{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Using RetrieveChat Powered by MongoDB Atlas for Retrieve Augmented Code Generation and Question Answering\n", "\n", "AutoGen offers conversable agents powered by LLM, tool or human, which can be used to perform tasks collectively via automated chat. This framework allows tool use and human participation through multi-agent conversation.\n", "Please find documentation about this feature [here](https://microsoft.github.io/autogen/docs/Use-Cases/agent_chat).\n", "\n", "RetrieveChat is a conversational system for retrieval-augmented code generation and question answering. In this notebook, we demonstrate how to utilize RetrieveChat to generate code and answer questions based on customized documentations that are not present in the LLM's training dataset. RetrieveChat uses the `AssistantAgent` and `RetrieveUserProxyAgent`, which is similar to the usage of `AssistantAgent` and `UserProxyAgent` in other notebooks (e.g., [Automated Task Solving with Code Generation, Execution & Debugging](https://github.com/microsoft/autogen/blob/main/notebook/agentchat_auto_feedback_from_code_execution.ipynb)). Essentially, `RetrieveUserProxyAgent` implement a different auto-reply mechanism corresponding to the RetrieveChat prompts.\n", "\n", "## Table of Contents\n", "We'll demonstrate six examples of using RetrieveChat for code generation and question answering:\n", "\n", "- [Example 1: Generate code based off docstrings w/o human feedback](#example-1)\n", "\n", "````{=mdx}\n", ":::info Requirements\n", "Some extra dependencies are needed for this notebook, which can be installed via pip:\n", "\n", "```bash\n", "pip install pyautogen[retrievechat-mongodb] flaml[automl]\n", "```\n", "\n", "For more information, please refer to the [installation guide](/docs/installation/).\n", ":::\n", "````\n", "\n", "Ensure you have a MongoDB Atlas instance with Cluster Tier >= M10. Read more on Cluster support [here](https://www.mongodb.com/docs/atlas/atlas-search/manage-indexes/#create-and-manage-fts-indexes)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Set your API Endpoint\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["models to use:  ['gpt-3.5-turbo-0125']\n"]}], "source": ["import os\n", "\n", "from autogen import AssistantAgent\n", "from autogen.agentchat.contrib.retrieve_user_proxy_agent import RetrieveUserProxyAgent\n", "\n", "# Accepted file formats for that can be stored in\n", "# a vector database instance\n", "from autogen.retrieve_utils import TEXT_FORMATS\n", "\n", "config_list = [\n", "    {\n", "        \"model\": \"gpt-3.5-turbo-0125\",\n", "        \"api_key\": os.environ[\"OPENAI_API_KEY\"],\n", "        \"api_type\": \"openai\",\n", "    }\n", "]\n", "assert len(config_list) > 0\n", "print(\"models to use: \", [config_list[i][\"model\"] for i in range(len(config_list))])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["````{=mdx}\n", ":::tip\n", "Learn more about configuring LLMs for agents [here](/docs/topics/llm_configuration).\n", ":::\n", "````\n", "\n", "## Construct agents for RetrieveChat\n", "\n", "We start by initializing the `AssistantAgent` and `RetrieveUserProxyAgent`. The system message needs to be set to \"You are a helpful assistant.\" for AssistantA<PERSON>. The detailed instructions are given in the user message. Later we will use the `RetrieveUserProxyAgent.message_generator` to combine the instructions and a retrieval augmented generation task for an initial prompt to be sent to the LLM assistant."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accepted file formats for `docs_path`:\n", "['txt', 'json', 'csv', 'tsv', 'md', 'html', 'htm', 'rtf', 'rst', 'jsonl', 'log', 'xml', 'yaml', 'yml', 'pdf']\n"]}], "source": ["print(\"Accepted file formats for `docs_path`:\")\n", "print(TEXT_FORMATS)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 1. create an AssistantAgent instance named \"assistant\"\n", "assistant = AssistantA<PERSON>(\n", "    name=\"assistant\",\n", "    system_message=\"You are a helpful assistant.\",\n", "    llm_config={\n", "        \"timeout\": 600,\n", "        \"cache_seed\": 42,\n", "        \"config_list\": config_list,\n", "    },\n", ")\n", "\n", "# 2. create the RetrieveUserProxyAgent instance named \"ragproxyagent\"\n", "# Refer to https://microsoft.github.io/autogen/docs/reference/agentchat/contrib/retrieve_user_proxy_agent\n", "# and https://microsoft.github.io/autogen/docs/reference/agentchat/contrib/vectordb/mongodb\n", "# for more information on the RetrieveUserProxyAgent and MongoDBAtlasVectorDB\n", "ragproxyagent = RetrieveUserProxyAgent(\n", "    name=\"ragproxyagent\",\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=3,\n", "    retrieve_config={\n", "        \"task\": \"code\",\n", "        \"docs_path\": [\n", "            \"https://raw.githubusercontent.com/microsoft/FLAML/main/website/docs/Examples/Integrate%20-%20Spark.md\",\n", "            \"https://raw.githubusercontent.com/microsoft/FLAML/main/website/docs/Research.md\",\n", "        ],\n", "        \"chunk_token_size\": 2000,\n", "        \"model\": config_list[0][\"model\"],\n", "        \"vector_db\": \"mongodb\",  # MongoDB Atlas database\n", "        \"collection_name\": \"demo_collection\",\n", "        \"db_config\": {\n", "            \"connection_string\": os.environ[\n", "                \"MONGODB_URI\"\n", "            ],  # MongoDB Atlas connection string\n", "            \"database_name\": \"test_db\",  # MongoDB Atlas database\n", "            \"index_name\": \"vector_index\",\n", "            \"wait_until_index_ready\": 120.0,  # Setting to wait 120 seconds or until index is constructed before querying\n", "            \"wait_until_document_ready\": 120.0,  # Setting to wait 120 seconds or until document is properly indexed after insertion/update\n", "        },\n", "        \"get_or_create\": True,  # set to False if you don't want to reuse an existing collection\n", "        \"overwrite\": False,  # set to True if you want to overwrite an existing collection, each overwrite will force a index creation and reupload of documents\n", "    },\n", "    code_execution_config=False,  # set to False if you don't want to execute the code\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Example 1\n", "\n", "[Back to top](#table-of-contents)\n", "\n", "Use RetrieveChat to help generate sample code and automatically run the code and fix errors if there is any.\n", "\n", "Problem: Which API should I use if I want to use FLAML for a classification task and I want to train the model in 30 seconds. Use spark to parallel the training. Force cancel jobs if time limit is reached."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-07-25 13:47:30,700 - autogen.agentchat.contrib.retrieve_user_proxy_agent - INFO - \u001b[32mUse the existing collection `demo_collection`.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Trying to create collection.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-07-25 13:47:31,048 - autogen.agentchat.contrib.retrieve_user_proxy_agent - INFO - Found 2 chunks.\u001b[0m\n", "2024-07-25 13:47:31,051 - autogen.agentchat.contrib.vectordb.mongodb - INFO - No documents to insert.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["VectorDB returns doc_ids:  [['bdfbc921', '7968cf3c']]\n", "\u001b[32mAdding content of doc bdfbc921 to context.\u001b[0m\n", "\u001b[32mAdding content of doc 7968cf3c to context.\u001b[0m\n", "\u001b[33mragproxyagent\u001b[0m (to assistant):\n", "\n", "You're a retrieve augmented coding assistant. You answer user's questions based on your own knowledge and the\n", "context provided by the user.\n", "If you can't answer the question with or without the current context, you should reply exactly `UPDATE CONTEXT`.\n", "For code generation, you must obey the following rules:\n", "Rule 1. You MUST NOT install any packages because all the packages needed are already installed.\n", "Rule 2. You must follow the formats below to write your code:\n", "```language\n", "# your code\n", "```\n", "\n", "User's question is: How can I use FLAML to perform a classification task and use spark to do parallel training. Train 30 seconds and force cancel jobs if time limit is reached.\n", "\n", "Context is: # Integrate - Spark\n", "\n", "FLAML has integrated Spark for distributed training. There are two main aspects of integration with Spark:\n", "\n", "- Use Spark ML estimators for AutoML.\n", "- Use Spark to run training in parallel spark jobs.\n", "\n", "## Spark ML Estimators\n", "\n", "FLAML integrates estimators based on Spark ML models. These models are trained in parallel using Spark, so we called them Spark estimators. To use these models, you first need to organize your data in the required format.\n", "\n", "### Data\n", "\n", "For Spark estimators, AutoML only consumes Spark data. FLAML provides a convenient function `to_pandas_on_spark` in the `flaml.automl.spark.utils` module to convert your data into a pandas-on-spark (`pyspark.pandas`) dataframe/series, which Spark estimators require.\n", "\n", "This utility function takes data in the form of a `pandas.Dataframe` or `pyspark.sql.Dataframe` and converts it into a pandas-on-spark dataframe. It also takes `pandas.Series` or `pyspark.sql.Dataframe` and converts it into a [pandas-on-spark](https://spark.apache.org/docs/latest/api/python/user_guide/pandas_on_spark/index.html) series. If you pass in a `pyspark.pandas.Dataframe`, it will not make any changes.\n", "\n", "This function also accepts optional arguments `index_col` and `default_index_type`.\n", "\n", "- `index_col` is the column name to use as the index, default is None.\n", "- `default_index_type` is the default index type, default is \"distributed-sequence\". More info about default index type could be found on Spark official [documentation](https://spark.apache.org/docs/latest/api/python/user_guide/pandas_on_spark/options.html#default-index-type)\n", "\n", "Here is an example code snippet for Spark Data:\n", "\n", "```python\n", "import pandas as pd\n", "from flaml.automl.spark.utils import to_pandas_on_spark\n", "\n", "# Creating a dictionary\n", "data = {\n", "    \"Square_Feet\": [800, 1200, 1800, 1500, 850],\n", "    \"Age_Years\": [20, 15, 10, 7, 25],\n", "    \"Price\": [100000, 200000, 300000, 240000, 120000],\n", "}\n", "\n", "# Creating a pandas DataFrame\n", "dataframe = pd.DataFrame(data)\n", "label = \"Price\"\n", "\n", "# Convert to pandas-on-spark dataframe\n", "psdf = to_pandas_on_spark(dataframe)\n", "```\n", "\n", "To use Spark ML models you need to format your data appropriately. Specifically, use [`VectorAssembler`](https://spark.apache.org/docs/latest/api/python/reference/api/pyspark.ml.feature.VectorAssembler.html) to merge all feature columns into a single vector column.\n", "\n", "Here is an example of how to use it:\n", "\n", "```python\n", "from pyspark.ml.feature import VectorAssembler\n", "\n", "columns = psdf.columns\n", "feature_cols = [col for col in columns if col != label]\n", "featurizer = VectorAssembler(inputCols=feature_cols, outputCol=\"features\")\n", "psdf = featurizer.transform(psdf.to_spark(index_col=\"index\"))[\"index\", \"features\"]\n", "```\n", "\n", "Later in conducting the experiment, use your pandas-on-spark data like non-spark data and pass them using `X_train, y_train` or `dataframe, label`.\n", "\n", "### Estimators\n", "\n", "#### Model List\n", "\n", "- `lgbm_spark`: The class for fine-tuning Spark version LightGBM models, using [SynapseML](https://microsoft.github.io/SynapseML/docs/features/lightgbm/about/) API.\n", "\n", "#### Usage\n", "\n", "First, prepare your data in the required format as described in the previous section.\n", "\n", "By including the models you intend to try in the `estimators_list` argument to `flaml.automl`, FLAML will start trying configurations for these models. If your input is Spark data, FLAML will also use estimators with the `_spark` postfix by default, even if you haven't specified them.\n", "\n", "Here is an example code snippet using SparkML models in AutoML:\n", "\n", "```python\n", "import flaml\n", "\n", "# prepare your data in pandas-on-spark format as we previously mentioned\n", "\n", "automl = flaml.AutoML()\n", "settings = {\n", "    \"time_budget\": 30,\n", "    \"metric\": \"r2\",\n", "    \"estimator_list\": [\"lgbm_spark\"],  # this setting is optional\n", "    \"task\": \"regression\",\n", "}\n", "\n", "automl.fit(\n", "    dataframe=psdf,\n", "    label=label,\n", "    **settings,\n", ")\n", "```\n", "\n", "[Link to notebook](https://github.com/microsoft/FLAML/blob/main/notebook/automl_bankrupt_synapseml.ipynb) | [Open in colab](https://colab.research.google.com/github/microsoft/FLAML/blob/main/notebook/automl_bankrupt_synapseml.ipynb)\n", "\n", "## <PERSON><PERSON><PERSON> Spark Jobs\n", "\n", "You can activate Spark as the parallel backend during parallel tuning in both [AutoML](/docs/Use-Cases/Task-Oriented-AutoML#parallel-tuning) and [Hyperparameter Tuning](/docs/Use-Cases/Tune-User-Defined-Function#parallel-tuning), by setting the `use_spark` to `true`. FLAML will dispatch your job to the distributed Spark backend using [`joblib-spark`](https://github.com/joblib/joblib-spark).\n", "\n", "Please note that you should not set `use_spark` to `true` when applying AutoML and Tuning for Spark Data. This is because only SparkML models will be used for Spark Data in AutoML and Tuning. As SparkML models run in parallel, there is no need to distribute them with `use_spark` again.\n", "\n", "All the Spark-related arguments are stated below. These arguments are available in both Hyperparameter Tuning and AutoML:\n", "\n", "- `use_spark`: boolean, default=False | Whether to use spark to run the training in parallel spark jobs. This can be used to accelerate training on large models and large datasets, but will incur more overhead in time and thus slow down training in some cases. GPU training is not supported yet when use_spark is True. For Spark clusters, by default, we will launch one trial per executor. However, sometimes we want to launch more trials than the number of executors (e.g., local mode). In this case, we can set the environment variable `FLAML_MAX_CONCURRENT` to override the detected `num_executors`. The final number of concurrent trials will be the minimum of `n_concurrent_trials` and `num_executors`.\n", "- `n_concurrent_trials`: int, default=1 | The number of concurrent trials. When n_concurrent_trials > 1, FLAML performes parallel tuning.\n", "- `force_cancel`: boolean, default=False | Whether to forcely cancel Spark jobs if the search time exceeded the time budget. Spark jobs include parallel tuning jobs and Spark-based model training jobs.\n", "\n", "An example code snippet for using parallel Spark jobs:\n", "\n", "```python\n", "import flaml\n", "\n", "automl_experiment = flaml.AutoML()\n", "automl_settings = {\n", "    \"time_budget\": 30,\n", "    \"metric\": \"r2\",\n", "    \"task\": \"regression\",\n", "    \"n_concurrent_trials\": 2,\n", "    \"use_spark\": True,\n", "    \"force_cancel\": True,  # Activating the force_cancel option can immediately halt Spark jobs once they exceed the allocated time_budget.\n", "}\n", "\n", "automl.fit(\n", "    dataframe=dataframe,\n", "    label=label,\n", "    **automl_settings,\n", ")\n", "```\n", "\n", "[Link to notebook](https://github.com/microsoft/FLAML/blob/main/notebook/integrate_spark.ipynb) | [Open in colab](https://colab.research.google.com/github/microsoft/FLAML/blob/main/notebook/integrate_spark.ipynb)\n", "# Research\n", "\n", "For technical details, please check our research publications.\n", "\n", "- [FLAML: A Fast and Lightweight AutoML Library](https://www.microsoft.com/en-us/research/publication/flaml-a-fast-and-lightweight-automl-library/). <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. MLSys 2021.\n", "\n", "```bibtex\n", "@inproceedings{wang2021flaml,\n", "    title={FLAML: A Fast and Lightweight AutoML Library},\n", "    author={<PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON><PERSON><PERSON>},\n", "    year={2021},\n", "    booktitle={MLSys},\n", "}\n", "```\n", "\n", "- [Frugal Optimization for Cost-related Hyperparameters](https://arxiv.org/abs/2005.01571). <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. AAAI 2021.\n", "\n", "```bibtex\n", "@inproceedings{wu2021cfo,\n", "    title={Frugal Optimization for Cost-related Hyperparameters},\n", "    author={<PERSON><PERSON> and <PERSON> and <PERSON><PERSON>},\n", "    year={2021},\n", "    booktitle={AAAI},\n", "}\n", "```\n", "\n", "- [Economical Hyperparameter Optimization With Blended Search Strategy](https://www.microsoft.com/en-us/research/publication/economical-hyperparameter-optimization-with-blended-search-strategy/). <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. ICLR 2021.\n", "\n", "```bibtex\n", "@inproceedings{wang2021blendsearch,\n", "    title={Economical Hyperparameter Optimization With Blended Search Strategy},\n", "    author={<PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>},\n", "    year={2021},\n", "    booktitle={ICLR},\n", "}\n", "```\n", "\n", "- [An Empirical Study on Hyperparameter Optimization for Fine-Tuning Pre-trained Language Models](https://aclanthology.org/2021.acl-long.178.pdf). <PERSON>, <PERSON>. ACL 2021.\n", "\n", "```bibtex\n", "@inproceedings{liuwang2021hpolm,\n", "    title={An Empirical Study on Hyperparameter Optimization for Fine-Tuning Pre-trained Language Models},\n", "    author={<PERSON> and <PERSON>},\n", "    year={2021},\n", "    booktitle={ACL},\n", "}\n", "```\n", "\n", "- [ChaCha for Online AutoML](https://www.microsoft.com/en-us/research/publication/chacha-for-online-automl/). <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>. ICML 2021.\n", "\n", "```bibtex\n", "@inproceedings{wu2021chacha,\n", "    title={ChaCha for Online AutoML},\n", "    author={<PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON>},\n", "    year={2021},\n", "    booktitle={ICML},\n", "}\n", "```\n", "\n", "- [Fair AutoML](https://arxiv.org/abs/2111.06495). <PERSON><PERSON>, <PERSON>. ArXiv preprint arXiv:2111.06495 (2021).\n", "\n", "```bibtex\n", "@inproceedings{wuwang2021fairautoml,\n", "    title={Fair AutoML},\n", "    author={<PERSON><PERSON> and <PERSON>},\n", "    year={2021},\n", "    booktitle={ArXiv preprint arXiv:2111.06495},\n", "}\n", "```\n", "\n", "- [Mining Robust Default Configurations for Resource-constrained AutoML](https://arxiv.org/abs/2202.09927). <PERSON>, <PERSON>. ArXiv preprint arXiv:2202.09927 (2022).\n", "\n", "```bibtex\n", "@inproceedings{kayaliwang2022default,\n", "    title={Mining Robust Default Configurations for Resource-constrained AutoML},\n", "    author={<PERSON> and <PERSON>},\n", "    year={2022},\n", "    booktitle={ArXiv preprint arXiv:2202.09927},\n", "}\n", "```\n", "\n", "- [Targeted Hyperparameter Optimization with Lexicographic Preferences Over Multiple Objectives](https://openreview.net/forum?id=0Ij9_q567Ma). <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. ICLR 2023 (notable-top-5%).\n", "\n", "```bibtex\n", "@inproceedings{zhang2023targeted,\n", "    title={Targeted Hyperparameter Optimization with Lexicographic Preferences Over Multiple Objectives},\n", "    author={<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON> and <PERSON><PERSON>},\n", "    booktitle={International Conference on Learning Representations},\n", "    year={2023},\n", "    url={https://openreview.net/forum?id=0Ij9_q567Ma},\n", "}\n", "```\n", "\n", "- [Cost-Effective Hyperparameter Optimization for Large Language Model Generation Inference](https://arxiv.org/abs/2303.04673). <PERSON>, <PERSON>, <PERSON>. ArXiv preprint arXiv:2303.04673 (2023).\n", "\n", "```bibtex\n", "@inproceedings{wang2023EcoOptiGen,\n", "    title={Cost-Effective Hyperparameter Optimization for Large Language Model Generation Inference},\n", "    author={<PERSON> and <PERSON> and <PERSON>},\n", "    year={2023},\n", "    booktitle={ArXiv preprint arXiv:2303.04673},\n", "}\n", "```\n", "\n", "- [An Empirical Study on Challenging Math Problem Solving with GPT-4](https://arxiv.org/abs/2306.01337). <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. ArXiv preprint arXiv:2306.01337 (2023).\n", "\n", "```bibtex\n", "@inproceedings{wu2023empirical,\n", "    title={An Empirical Study on Challenging Math Problem Solving with GPT-4},\n", "    author={<PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON><PERSON> and <PERSON>},\n", "    year={2023},\n", "    booktitle={ArXiv preprint arXiv:2306.01337},\n", "}\n", "```\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to ragproxyagent):\n", "\n", "To use FLAML to perform a classification task and use Spark for parallel training with a timeout of 30 seconds and force canceling jobs if the time limit is reached, you can follow the below code snippet:\n", "\n", "```python\n", "import flaml\n", "from flaml.automl.spark.utils import to_pandas_on_spark\n", "from pyspark.ml.feature import VectorAssembler\n", "\n", "# Prepare your data in pandas-on-spark format\n", "data = {\n", "    \"feature1\": [val1, val2, val3, val4],\n", "    \"feature2\": [val5, val6, val7, val8],\n", "    \"target\": [class1, class2, class1, class2],\n", "}\n", "\n", "dataframe = pd.DataFrame(data)\n", "label = \"target\"\n", "psdf = to_pandas_on_spark(dataframe)\n", "\n", "# Prepare your features using VectorAssembler\n", "columns = psdf.columns\n", "feature_cols = [col for col in columns if col != label]\n", "featurizer = VectorAssembler(inputCols=feature_cols, outputCol=\"features\")\n", "psdf = featurizer.transform(psdf)\n", "\n", "# Define AutoML settings and fit the model\n", "automl = flaml.AutoML()\n", "settings = {\n", "    \"time_budget\": 30,\n", "    \"metric\": \"accuracy\",\n", "    \"task\": \"classification\",\n", "    \"estimator_list\": [\"lgbm_spark\"],  # Optional\n", "}\n", "\n", "automl.fit(\n", "    dataframe=psdf,\n", "    label=label,\n", "    **settings,\n", ")\n", "```\n", "\n", "In the code:\n", "- Replace `val1, val2, ..., class1, class2` with your actual data values.\n", "- Ensure the features and target columns are correctly specified in the data dictionary.\n", "- Set the `time_budget` parameter to 30 to limit the training time.\n", "- The `force_cancel` parameter is set to `True` to force cancel Spark jobs if the time limit is exceeded.\n", "\n", "Make sure to adapt the code to your specific dataset and requirements.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mragproxyagent\u001b[0m (to assistant):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to ragproxyagent):\n", "\n", "UPDATE CONTEXT\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[32mUpdating context and resetting conversation.\u001b[0m\n", "VectorDB returns doc_ids:  [['bdfbc921', '7968cf3c']]\n", "VectorDB returns doc_ids:  [['bdfbc921', '7968cf3c']]\n", "VectorDB returns doc_ids:  [['bdfbc921', '7968cf3c']]\n", "VectorDB returns doc_ids:  [['bdfbc921', '7968cf3c']]\n", "\u001b[32mNo more context, will terminate.\u001b[0m\n", "\u001b[33mragproxyagent\u001b[0m (to assistant):\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["# reset the assistant. Always reset the assistant before starting a new conversation.\n", "assistant.reset()\n", "\n", "# given a problem, we use the ragproxyagent to generate a prompt to be sent to the assistant as the initial message.\n", "# the assistant receives the message and generates a response. The response will be sent back to the ragproxyagent for processing.\n", "# The conversation continues until the termination condition is met, in RetrieveChat, the termination condition when no human-in-loop is no code block detected.\n", "# With human-in-loop, the conversation will continue until the user says \"exit\".\n", "code_problem = \"How can I use FLAML to perform a classification task and use spark to do parallel training. Train 30 seconds and force cancel jobs if time limit is reached.\"\n", "chat_result = ragproxyagent.initiate_chat(\n", "    assistant, message=ragproxyagent.message_generator, problem=code_problem\n", ")"]}], "metadata": {"front_matter": {"description": "Explore the use of AutoGen's RetrieveChat for tasks like code generation from docstrings, answering complex questions with human feedback, and exploiting features like Update Context, custom prompts, and few-shot learning.", "tags": ["RAG"]}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "skip_test": "Requires interactive usage", "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 4}