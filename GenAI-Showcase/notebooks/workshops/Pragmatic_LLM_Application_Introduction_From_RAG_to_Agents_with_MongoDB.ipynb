{"cells": [{"cell_type": "markdown", "metadata": {"id": "Y6C56i5W-XQV"}, "source": ["# **Pragmatic LLM Application Development: From RAG Pipleines to AI Agents**\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/workshops/Pragmatic_LLM_Application_Introduction_From_RAG_to_Agents_with_MongoDB.ipynb)\n", "\n", "A practical guide that introduces two forms of LLM Applications: RAG (Retrieval-Augmented Generation) pipelines and AI Agents.\n", "\n", "This guide is designed to take you on a journey that develops your understanding of LLM Applications, starting with implementations without abstraction frameworks, and later introducing the implementation of RAG pipelines, AI agents, and other LLM application components using frameworks and libraries that alleviate the implementation burden for AI Stack Engineers.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "Ivd0AjdtO3Pp"}, "source": ["## Key topics covered:"]}, {"cell_type": "markdown", "metadata": {"id": "ywbYrsbJPIxy"}, "source": ["1. **Document Model and MongoDB Integration**: Introduces the Document model and its integration with MongoDB within LLM applications.\n", "\n", "2. **RAG Pipeline Fundamentals**: Guides you through the key processes within a RAG pipeline, including data embedding, data ingestion, and handling user queries.\n", "\n", "3. **MongoDB Vector Database Integration**: Guides you through the development of a RAG pipeline connected to a MongoDB Vector Database and utilizing OpenAI's models.\n", "\n", "4. **MongoDB Aggregation Pipelines**: Introduces MongoDB Aggregation pipelines and stages for efficient data retrieval implementation within pipelines.\n", "\n", "5. **LLM Abstraction Frameworks**: Showcases the development of RAG pipelines using widely-used LLM abstraction frameworks such as LangChain, LlamaIndex, and HayStack.\n", "\n", "6. **Data Handling in LLM Applications**: Presents methods for handling data in LLM applications using tools such as Pydantic and Pandas.\n", "\n", "7. **AI Agent Implementation**: Introduces the implementation of AI Agents using libraries such as LangChain and LlamaIndex.\n", "\n", "8. **LLM Application Optimization**: Introduces techniques for optimizing LLM Applications, such as prompt compression using the LLMLingua library."]}, {"cell_type": "markdown", "metadata": {"id": "Ju7p8vSUO5_0"}, "source": ["## Who is this for:"]}, {"cell_type": "markdown", "metadata": {"id": "gW_YbBcnQuCy"}, "source": ["- **AI Engineers**: Professionals responsible for developing generative AI applications will find practical guidance on implementing such systems.\n", "- **AI Stack Engineers**: Individuals working with AI Stack tools and libraries will gain insights into the implementation approaches employed by widely adopted libraries, enhancing their understanding and proficiency.\n", "- **Software Engineers**: For those seeking a straightforward introduction to LLM Applications, this guide provides a focused and concise exploration of the subject matter, without unnecessary verbosity or fluff."]}, {"cell_type": "markdown", "metadata": {"id": "90yGs3R-Q38h"}, "source": ["# Table of Content\n", "\n", "[**Part 1: Vanilla RAG Application**](#scrollTo=hlnz3AIYn5DK)\n", "- [1.1 Synthetic Data Creation](#scrollTo=VXlm_J_TokJp)\n", "- [1.2 Embedding Data for Vector Search](#scrollTo=0AOQw0Caosxu)\n", "- [1.3 Data Ingestion into MongoDB Database](#scrollTo=MhO4jWndsWjR)\n", "- [1.4 Vector Search Index Creation](#scrollTo=B8VZ-c4qt92b)\n", "- [1.5 RAG with MongoDB](#scrollTo=EC6nU1NSuFqO)\n", "- [1.6 Handling User Query](#scrollTo=4UaKjc5nugfd)\n", "- [1.7 Handling User Query With Prompt Compression (LLMLingua)](#scrollTo=BKdB25EMukQO)\n", "\n", "[**Part 2: RAG Application With Abstraction Frameworks**](#scrollTo=ALrfaObSteOs)\n", "- [2.1 RAG with LangChain and MongoDB](#scrollTo=DWK6DxuQjmhp)\n", "    - [2.1.3 Prompt Compression with LangChain and LLMLingua](#scrollTo=rnSuWk2cqxtq)\n", "- 2.2 RAG with LlamaIndex and MongoDB\n", "- 2.3 RAG with HayStack and MongoDB\n", "\n", "[**Part 3: AI Agent Application: HR Use Case**]()"]}, {"cell_type": "markdown", "metadata": {"id": "hlnz3AIYn5DK"}, "source": ["# Part 1: Vanilla RAG Application"]}, {"cell_type": "markdown", "metadata": {"id": "rS4JFn_3o5zg"}, "source": ["## Install Libaries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cHLYHpobdSHR"}, "outputs": [], "source": ["! pip install pandas openai pymongo llmlingua"]}, {"cell_type": "markdown", "metadata": {"id": "bVj7IuXcrAuC"}, "source": ["## Set Up OpenAI and MongoDB environment variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5O1afzs8q-8c"}, "outputs": [], "source": ["import os\n", "\n", "# Your OpenAI API key\n", "os.environ[\"OPENAI_API_KEY\"] = \"\"\n", "\n", "# Your MongoDB Atlas connection string\n", "os.environ[\"MONGO_URI\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "BHMumTCCgMzt"}, "outputs": [{"ename": "SyntaxError", "evalue": "EOL while scanning string literal (1411027751.py, line 3)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[1], line 3\u001b[0;36m\u001b[0m\n\u001b[0;31m    openai.api_key = os.environ.get(\"OPENAI_API_KEY\u001b[0m\n\u001b[0m                                                   ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m EOL while scanning string literal\n"]}], "source": ["import openai\n", "\n", "openai.api_key = os.environ.get(\"OPENAI_API_KEY\")\n", "OPEN_AI_MODEL = \"gpt-4o\"\n", "OPEN_AI_EMBEDDING_MODEL = \"text-embedding-3-small\"\n", "OPEN_AI_EMBEDDING_MODEL_DIMENSION = 1536"]}, {"cell_type": "markdown", "metadata": {"id": "VXlm_J_TokJp"}, "source": ["## 1.1 Synthetic Data Creation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hMnZyw5odPbX"}, "outputs": [], "source": ["import random\n", "\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CKbjXNCUdNz5"}, "outputs": [], "source": ["# Define a list of job titles and departments for variety\n", "job_titles = [\n", "    \"Software Engineer\",\n", "    \"Senior Software Engineer\",\n", "    \"Data Scientist\",\n", "    \"Product Manager\",\n", "    \"Project Manager\",\n", "    \"UX Designer\",\n", "    \"QA Engineer\",\n", "    \"DevOps Engineer\",\n", "    \"CTO\",\n", "    \"CEO\",\n", "]\n", "departments = [\n", "    \"IT\",\n", "    \"Engineering\",\n", "    \"Data Science\",\n", "    \"Product\",\n", "    \"Project Management\",\n", "    \"Design\",\n", "    \"Quality Assurance\",\n", "    \"Operations\",\n", "    \"Executive\",\n", "]\n", "\n", "# Define a list of office locations\n", "office_locations = [\n", "    \"Chicago Office\",\n", "    \"New York Office\",\n", "    \"London Office\",\n", "    \"Berlin Office\",\n", "    \"Tokyo Office\",\n", "    \"Sydney Office\",\n", "    \"Toronto Office\",\n", "    \"San Francisco Office\",\n", "    \"Paris Office\",\n", "    \"Singapore Office\",\n", "]\n", "\n", "\n", "# Define a function to create a random employee entry\n", "def create_employee(\n", "    employee_id, first_name, last_name, job_title, department, manager_id=None\n", "):\n", "    return {\n", "        \"employee_id\": employee_id,\n", "        \"first_name\": first_name,\n", "        \"last_name\": last_name,\n", "        \"gender\": random.choice([\"Male\", \"Female\"]),\n", "        \"date_of_birth\": f\"{random.randint(1950, 2000)}-{random.randint(1, 12):02}-{random.randint(1, 28):02}\",\n", "        \"address\": {\n", "            \"street\": f\"{random.randint(100, 999)} Main Street\",\n", "            \"city\": \"Springfield\",\n", "            \"state\": \"IL\",\n", "            \"postal_code\": \"62704\",\n", "            \"country\": \"USA\",\n", "        },\n", "        \"contact_details\": {\n", "            \"email\": f\"{first_name.lower()}.{last_name.lower()}@example.com\",\n", "            \"phone_number\": f\"******-{random.randint(100, 999)}-{random.randint(1000, 9999)}\",\n", "        },\n", "        \"job_details\": {\n", "            \"job_title\": job_title,\n", "            \"department\": department,\n", "            \"hire_date\": f\"{random.randint(2000, 2022)}-{random.randint(1, 12):02}-{random.randint(1, 28):02}\",\n", "            \"employment_type\": \"Full-Time\",\n", "            \"salary\": random.randint(50000, 250000),\n", "            \"currency\": \"USD\",\n", "        },\n", "        \"work_location\": {\n", "            \"nearest_office\": random.choice(office_locations),\n", "            \"is_remote\": random.choice([True, False]),\n", "        },\n", "        \"reporting_manager\": manager_id,\n", "        \"skills\": random.sample(\n", "            [\n", "                \"JavaScript\",\n", "                \"Python\",\n", "                \"Node.js\",\n", "                \"React\",\n", "                \"Django\",\n", "                \"Flask\",\n", "                \"AWS\",\n", "                \"Docker\",\n", "                \"Kubernetes\",\n", "                \"SQL\",\n", "            ],\n", "            4,\n", "        ),\n", "        \"performance_reviews\": [\n", "            {\n", "                \"review_date\": f\"{random.randint(2020, 2023)}-{random.randint(1, 12):02}-{random.randint(1, 28):02}\",\n", "                \"rating\": round(random.uniform(3, 5), 1),\n", "                \"comments\": random.choice(\n", "                    [\n", "                        \"Exceeded expectations in the last project.\",\n", "                        \"Consistently meets performance standards.\",\n", "                        \"Needs improvement in time management.\",\n", "                        \"Outstanding performance and dedication.\",\n", "                    ]\n", "                ),\n", "            },\n", "            {\n", "                \"review_date\": f\"{random.randint(2019, 2022)}-{random.randint(1, 12):02}-{random.randint(1, 28):02}\",\n", "                \"rating\": round(random.uniform(3, 5), 1),\n", "                \"comments\": random.choice(\n", "                    [\n", "                        \"Exceeded expectations in the last project.\",\n", "                        \"Consistently meets performance standards.\",\n", "                        \"Needs improvement in time management.\",\n", "                        \"Outstanding performance and dedication.\",\n", "                    ]\n", "                ),\n", "            },\n", "        ],\n", "        \"benefits\": {\n", "            \"health_insurance\": random.choice(\n", "                [\"Gold Plan\", \"Silver Plan\", \"Bronze Plan\"]\n", "            ),\n", "            \"retirement_plan\": \"401K\",\n", "            \"paid_time_off\": random.randint(15, 30),\n", "        },\n", "        \"emergency_contact\": {\n", "            \"name\": f\"{random.choice(['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'])} {random.choice(['<PERSON><PERSON>', '<PERSON>', '<PERSON>'])}\",\n", "            \"relationship\": random.choice([\"Spouse\", \"Parent\", \"Sibling\", \"Friend\"]),\n", "            \"phone_number\": f\"******-{random.randint(100, 999)}-{random.randint(1000, 9999)}\",\n", "        },\n", "        \"notes\": random.choice(\n", "            [\n", "                \"Promoted to Senior Software Engineer in 2020.\",\n", "                \"Completed leadership training in 2021.\",\n", "                \"Received Employee of the Month award in 2022.\",\n", "                \"Actively involved in company hackathons and innovation challenges.\",\n", "            ]\n", "        ),\n", "    }\n", "\n", "\n", "# Generate 10 employee entries\n", "employees = [\n", "    create_employee(\"E123456\", \"<PERSON>\", \"<PERSON><PERSON>\", \"Software Engineer\", \"IT\", \"M987654\"),\n", "    create_employee(\n", "        \"E123457\", \"<PERSON>\", \"<PERSON><PERSON>\", \"Senior Software Engineer\", \"IT\", \"M987654\"\n", "    ),\n", "    create_employee(\n", "        \"E123458\", \"<PERSON>\", \"Smith\", \"Data Scientist\", \"Data Science\", \"M987655\"\n", "    ),\n", "    create_employee(\n", "        \"E123459\", \"<PERSON>\", \"<PERSON>\", \"Product Manager\", \"Product\", \"M987656\"\n", "    ),\n", "    create_employee(\n", "        \"E123460\", \"<PERSON>\", \"<PERSON>\", \"Project Manager\", \"Project Management\", \"M987657\"\n", "    ),\n", "    create_employee(\"E123461\", \"<PERSON>\", \"<PERSON>\", \"UX Designer\", \"Design\", \"M987658\"),\n", "    create_employee(\n", "        \"E123462\", \"<PERSON>\", \"<PERSON>\", \"QA Engineer\", \"Quality Assurance\", \"M987659\"\n", "    ),\n", "    create_employee(\n", "        \"E123463\", \"<PERSON>\", \"<PERSON>\", \"DevOps Engineer\", \"Operations\", \"M987660\"\n", "    ),\n", "    create_employee(\"E123464\", \"<PERSON>\", \"<PERSON>\", \"CTO\", \"Executive\", None),\n", "    create_employee(\"<PERSON>123465\", \"<PERSON>\", \"<PERSON>\", \"CEO\", \"Executive\", None),\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "julostVFdU_X", "outputId": "d188495c-4f61-41a7-f151-651ae14cad9d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Synthetic employee data has been saved to synthetic_data_employees.csv\n"]}], "source": ["# Convert to DataFrame\n", "df_employees = pd.DataFrame(employees)\n", "\n", "# Save DataFrame to CSV\n", "csv_file_employees = \"synthetic_data_employees.csv\"\n", "df_employees.to_csv(csv_file_employees, index=False)\n", "\n", "print(f\"Synthetic employee data has been saved to {csv_file_employees}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 660}, "id": "X3TLg1BNzK_Y", "outputId": "f04c8d24-79ae-4bab-9734-9cb55ca16e60"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df_employees\",\n  \"rows\": 10,\n  \"fields\": [\n    {\n      \"column\": \"employee_id\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"E123464\",\n          \"E123457\",\n          \"E123461\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"first_name\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"<PERSON>\",\n          \"<PERSON>\",\n          \"<PERSON>\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"last_name\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 9,\n        \"samples\": [\n          \"<PERSON>\",\n          \"<PERSON>\",\n          \"<PERSON>\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"gender\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Female\",\n          \"Male\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"date_of_birth\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"1998-06-13\",\n          \"1985-08-13\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"address\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"contact_details\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"job_details\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"work_location\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"reporting_manager\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 7,\n        \"samples\": [\n          \"M987654\",\n          \"M987655\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"skills\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"performance_reviews\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"benefits\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"emergency_contact\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"notes\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"Received Employee of the Month award in 2022.\",\n          \"Promoted to Senior Software Engineer in 2020.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df_employees"}, "text/html": ["\n", "  <div id=\"df-5849cf3a-b4bd-4190-8edd-6be0ba728e26\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>employee_id</th>\n", "      <th>first_name</th>\n", "      <th>last_name</th>\n", "      <th>gender</th>\n", "      <th>date_of_birth</th>\n", "      <th>address</th>\n", "      <th>contact_details</th>\n", "      <th>job_details</th>\n", "      <th>work_location</th>\n", "      <th>reporting_manager</th>\n", "      <th>skills</th>\n", "      <th>performance_reviews</th>\n", "      <th>benefits</th>\n", "      <th>emergency_contact</th>\n", "      <th>notes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>E123456</td>\n", "      <td>John</td>\n", "      <td><PERSON>e</td>\n", "      <td>Male</td>\n", "      <td>1990-06-26</td>\n", "      <td>{'street': '650 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_numbe...</td>\n", "      <td>{'job_title': 'Software Engineer', 'department...</td>\n", "      <td>{'nearest_office': 'Singapore Office', 'is_rem...</td>\n", "      <td>M987654</td>\n", "      <td>[Node.js, Flask, Docker, JavaScript]</td>\n", "      <td>[{'review_date': '2022-10-23', 'rating': 3.7, ...</td>\n", "      <td>{'health_insurance': 'Silver Plan', 'retiremen...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Friend...</td>\n", "      <td>Actively involved in company hackathons and in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>E123457</td>\n", "      <td>Jane</td>\n", "      <td><PERSON>e</td>\n", "      <td>Male</td>\n", "      <td>1985-08-13</td>\n", "      <td>{'street': '787 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_numbe...</td>\n", "      <td>{'job_title': 'Senior Software Engineer', 'dep...</td>\n", "      <td>{'nearest_office': 'Tokyo Office', 'is_remote'...</td>\n", "      <td>M987654</td>\n", "      <td>[Python, JavaScript, SQL, Docker]</td>\n", "      <td>[{'review_date': '2021-09-03', 'rating': 4.9, ...</td>\n", "      <td>{'health_insurance': 'Silver Plan', 'retiremen...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': '<PERSON><PERSON>...</td>\n", "      <td>Received Employee of the Month award in 2022.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>E123458</td>\n", "      <td>Emily</td>\n", "      <td><PERSON></td>\n", "      <td>Female</td>\n", "      <td>1972-07-22</td>\n", "      <td>{'street': '612 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_nu...</td>\n", "      <td>{'job_title': 'Data Scientist', 'department': ...</td>\n", "      <td>{'nearest_office': 'Paris Office', 'is_remote'...</td>\n", "      <td>M987655</td>\n", "      <td>[<PERSON><PERSON><PERSON>, <PERSON>de.js, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>]</td>\n", "      <td>[{'review_date': '2020-01-26', 'rating': 4.4, ...</td>\n", "      <td>{'health_insurance': 'Gold Plan', 'retirement_...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Spou...</td>\n", "      <td>Received Employee of the Month award in 2022.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>E123459</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Male</td>\n", "      <td>1992-10-27</td>\n", "      <td>{'street': '852 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': 'micha<PERSON>.<EMAIL>', 'phone_...</td>\n", "      <td>{'job_title': 'Product Manager', 'department':...</td>\n", "      <td>{'nearest_office': 'San Francisco Office', 'is...</td>\n", "      <td>M987656</td>\n", "      <td>[A<PERSON>, Node.js, Python, Django]</td>\n", "      <td>[{'review_date': '2023-02-10', 'rating': 4.2, ...</td>\n", "      <td>{'health_insurance': 'Gold Plan', 'retirement_...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Spouse'...</td>\n", "      <td>Actively involved in company hackathons and in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>E123460</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Female</td>\n", "      <td>1962-02-11</td>\n", "      <td>{'street': '713 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_nu...</td>\n", "      <td>{'job_title': 'Project Manager', 'department':...</td>\n", "      <td>{'nearest_office': 'Chicago Office', 'is_remot...</td>\n", "      <td>M987657</td>\n", "      <td>[JavaScript, Flask, Django, SQL]</td>\n", "      <td>[{'review_date': '2023-07-02', 'rating': 3.2, ...</td>\n", "      <td>{'health_insurance': 'Gold Plan', 'retirement_...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Fri...</td>\n", "      <td>Actively involved in company hackathons and in...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-5849cf3a-b4bd-4190-8edd-6be0ba728e26')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -***********\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-5849cf3a-b4bd-4190-8edd-6be0ba728e26 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-5849cf3a-b4bd-4190-8edd-6be0ba728e26');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-d646bbb9-6c54-40e9-8b8a-de48a955fa19\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-d646bbb9-6c54-40e9-8b8a-de48a955fa19')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-d646bbb9-6c54-40e9-8b8a-de48a955fa19 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["  employee_id first_name last_name  gender date_of_birth  \\\n", "0     E123456       <PERSON>    1990-06-26   \n", "1     E123457       <PERSON>    1985-08-13   \n", "2     E123458      <PERSON>    1972-07-22   \n", "3     E123459    <PERSON>    1992-10-27   \n", "4     E123460      <PERSON>    1962-02-11   \n", "\n", "                                             address  \\\n", "0  {'street': '650 Main Street', 'city': 'Springf...   \n", "1  {'street': '787 Main Street', 'city': 'Springf...   \n", "2  {'street': '612 Main Street', 'city': 'Springf...   \n", "3  {'street': '852 Main Street', 'city': 'Springf...   \n", "4  {'street': '713 Main Street', 'city': 'Springf...   \n", "\n", "                                     contact_details  \\\n", "0  {'email': '<EMAIL>', 'phone_numbe...   \n", "1  {'email': '<EMAIL>', 'phone_numbe...   \n", "2  {'email': '<EMAIL>', 'phone_nu...   \n", "3  {'email': 'micha<PERSON>.<EMAIL>', 'phone_...   \n", "4  {'email': '<EMAIL>', 'phone_nu...   \n", "\n", "                                         job_details  \\\n", "0  {'job_title': 'Software Engineer', 'department...   \n", "1  {'job_title': 'Senior Software Engineer', 'dep...   \n", "2  {'job_title': 'Data Scientist', 'department': ...   \n", "3  {'job_title': 'Product Manager', 'department':...   \n", "4  {'job_title': 'Project Manager', 'department':...   \n", "\n", "                                       work_location reporting_manager  \\\n", "0  {'nearest_office': 'Singapore Office', 'is_rem...           M987654   \n", "1  {'nearest_office': 'Tokyo Office', 'is_remote'...           M987654   \n", "2  {'nearest_office': 'Paris Office', 'is_remote'...           M987655   \n", "3  {'nearest_office': 'San Francisco Office', 'is...           M987656   \n", "4  {'nearest_office': 'Chicago Office', 'is_remot...           M987657   \n", "\n", "                                  skills  \\\n", "0   [Node.js, <PERSON>lask, <PERSON>er, JavaScript]   \n", "1      [Python, JavaScript, SQL, Docker]   \n", "2  [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>j<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>]   \n", "3         [<PERSON><PERSON>, Node.js, Python, Djan<PERSON>]   \n", "4       [JavaScript, Flask, Django, SQL]   \n", "\n", "                                 performance_reviews  \\\n", "0  [{'review_date': '2022-10-23', 'rating': 3.7, ...   \n", "1  [{'review_date': '2021-09-03', 'rating': 4.9, ...   \n", "2  [{'review_date': '2020-01-26', 'rating': 4.4, ...   \n", "3  [{'review_date': '2023-02-10', 'rating': 4.2, ...   \n", "4  [{'review_date': '2023-07-02', 'rating': 3.2, ...   \n", "\n", "                                            benefits  \\\n", "0  {'health_insurance': 'Silver Plan', 'retiremen...   \n", "1  {'health_insurance': 'Silver Plan', 'retiremen...   \n", "2  {'health_insurance': 'Gold Plan', 'retirement_...   \n", "3  {'health_insurance': 'Gold Plan', 'retirement_...   \n", "4  {'health_insurance': 'Gold Plan', 'retirement_...   \n", "\n", "                                   emergency_contact  \\\n", "0  {'name': '<PERSON>', 'relationship': 'Friend...   \n", "1  {'name': '<PERSON>', 'relationship': '<PERSON><PERSON>...   \n", "2  {'name': '<PERSON>', 'relationship': 'Spou...   \n", "3  {'name': '<PERSON>', 'relationship': 'Spouse'...   \n", "4  {'name': '<PERSON>', 'relationship': 'Fri...   \n", "\n", "                                               notes  \n", "0  Actively involved in company hackathons and in...  \n", "1      Received Employee of the Month award in 2022.  \n", "2      Received Employee of the Month award in 2022.  \n", "3  Actively involved in company hackathons and in...  \n", "4  Actively involved in company hackathons and in...  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_employees.head()"]}, {"cell_type": "markdown", "metadata": {"id": "0AOQw0Caosxu"}, "source": ["## 1.2 Embedding Data For Vector Search"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1cwqBZMxoruv", "outputId": "370dd4b3-f23f-4535-ec2c-0b7b7309ce14"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here's what an employee string looks like: /n <PERSON>, Male, born on 1990-06-26. Job: Software Engineer in IT. Skills: Node.js, <PERSON>lask, Docker, JavaScript. Reviews: Rated 3.7 on 2022-10-23: Outstanding performance and dedication. Rated 4.9 on 2021-07-24: Needs improvement in time management.. Location: Works at Singapore Office, Remote: True. Notes: Actively involved in company hackathons and innovation challenges.\n"]}], "source": ["# Function to create a string representation of the employee's key attributes for embedding\n", "def create_employee_string(employee):\n", "    job_details = f\"{employee['job_details']['job_title']} in {employee['job_details']['department']}\"\n", "    skills = \", \".join(employee[\"skills\"])\n", "    performance_reviews = \" \".join(\n", "        [\n", "            f\"Rated {review['rating']} on {review['review_date']}: {review['comments']}\"\n", "            for review in employee[\"performance_reviews\"]\n", "        ]\n", "    )\n", "    basic_info = f\"{employee['first_name']} {employee['last_name']}, {employee['gender']}, born on {employee['date_of_birth']}\"\n", "    work_location = f\"Works at {employee['work_location']['nearest_office']}, Remote: {employee['work_location']['is_remote']}\"\n", "    notes = employee[\"notes\"]\n", "\n", "    return f\"{basic_info}. Job: {job_details}. Skills: {skills}. Reviews: {performance_reviews}. Location: {work_location}. Notes: {notes}\"\n", "\n", "\n", "# Example usage with one employee\n", "employee_string = create_employee_string(employees[0])\n", "print(f\"Here's what an employee string looks like: /n {employee_string}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "beUq3DNQsAic"}, "outputs": [], "source": ["# Apply the function to all employees\n", "df_employees[\"employee_string\"] = df_employees.apply(create_employee_string, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YzZaLx5DsGSz", "outputId": "b660b683-01e0-4fc6-dca7-b3af14af1139"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Embeddings generated for employees\n"]}], "source": ["# Generate an embedding using OpenAI's API\n", "def get_embedding(text):\n", "    \"\"\"Generate an embedding for the given text using OpenAI's API.\"\"\"\n", "\n", "    # Check for valid input\n", "    if not text or not isinstance(text, str):\n", "        return None\n", "\n", "    try:\n", "        # Call OpenAI API to get the embedding\n", "        embedding = (\n", "            openai.embeddings.create(\n", "                input=text,\n", "                model=OPEN_AI_EMBEDDING_MODEL,\n", "                dimensions=OPEN_AI_EMBEDDING_MODEL_DIMENSION,\n", "            )\n", "            .data[0]\n", "            .embedding\n", "        )\n", "        return embedding\n", "    except Exception as e:\n", "        print(f\"Error in get_embedding: {e}\")\n", "        return None\n", "\n", "\n", "# Apply the function to generate embeddings for all employees with error handling\n", "try:\n", "    df_employees[\"embedding\"] = df_employees[\"employee_string\"].apply(get_embedding)\n", "    print(\"Embeddings generated for employees\")\n", "except Exception as e:\n", "    print(f\"Error applying embedding function to DataFrame: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 660}, "id": "nM1Ok77SzPYa", "outputId": "36909f7d-00fa-49fc-908c-dae72c17d09a"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df_employees\",\n  \"rows\": 10,\n  \"fields\": [\n    {\n      \"column\": \"employee_id\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"E123464\",\n          \"E123457\",\n          \"E123461\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"first_name\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"<PERSON>\",\n          \"<PERSON>\",\n          \"<PERSON>\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"last_name\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 9,\n        \"samples\": [\n          \"<PERSON>\",\n          \"<PERSON>\",\n          \"<PERSON>\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"gender\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Female\",\n          \"Male\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"date_of_birth\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"1998-06-13\",\n          \"1985-08-13\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"address\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"contact_details\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"job_details\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"work_location\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"reporting_manager\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 7,\n        \"samples\": [\n          \"M987654\",\n          \"M987655\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"skills\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"performance_reviews\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"benefits\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"emergency_contact\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"notes\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"Received Employee of the Month award in 2022.\",\n          \"Promoted to Senior Software Engineer in 2020.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"employee_string\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"Sophia Garcia, Female, born on 1998-06-13. Job: CTO in Executive. Skills: JavaScript, Node.js, Docker, AWS. Reviews: Rated 3.5 on 2023-11-02: Exceeded expectations in the last project. Rated 3.4 on 2020-11-04: Needs improvement in time management.. Location: Works at San Francisco Office, Remote: True. Notes: Promoted to Senior Software Engineer in 2020.\",\n          \"Jane Doe, Male, born on 1985-08-13. Job: Senior Software Engineer in IT. Skills: Python, JavaScript, SQL, Docker. Reviews: Rated 4.9 on 2021-09-03: Needs improvement in time management. Rated 3.8 on 2022-06-07: Exceeded expectations in the last project.. Location: Works at Tokyo Office, Remote: True. Notes: Received Employee of the Month award in 2022.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df_employees"}, "text/html": ["\n", "  <div id=\"df-f8b05e81-49ac-40de-ac42-84f78dc4e9e2\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>employee_id</th>\n", "      <th>first_name</th>\n", "      <th>last_name</th>\n", "      <th>gender</th>\n", "      <th>date_of_birth</th>\n", "      <th>address</th>\n", "      <th>contact_details</th>\n", "      <th>job_details</th>\n", "      <th>work_location</th>\n", "      <th>reporting_manager</th>\n", "      <th>skills</th>\n", "      <th>performance_reviews</th>\n", "      <th>benefits</th>\n", "      <th>emergency_contact</th>\n", "      <th>notes</th>\n", "      <th>employee_string</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>E123456</td>\n", "      <td>John</td>\n", "      <td><PERSON>e</td>\n", "      <td>Male</td>\n", "      <td>1990-06-26</td>\n", "      <td>{'street': '650 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_numbe...</td>\n", "      <td>{'job_title': 'Software Engineer', 'department...</td>\n", "      <td>{'nearest_office': 'Singapore Office', 'is_rem...</td>\n", "      <td>M987654</td>\n", "      <td>[Node.js, Flask, Docker, JavaScript]</td>\n", "      <td>[{'review_date': '2022-10-23', 'rating': 3.7, ...</td>\n", "      <td>{'health_insurance': 'Silver Plan', 'retiremen...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Friend...</td>\n", "      <td>Actively involved in company hackathons and in...</td>\n", "      <td><PERSON>, Male, born on 1990-06-26. Job: Softw...</td>\n", "      <td>[-0.03204594925045967, 0.018745997920632362, 0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>E123457</td>\n", "      <td>Jane</td>\n", "      <td><PERSON>e</td>\n", "      <td>Male</td>\n", "      <td>1985-08-13</td>\n", "      <td>{'street': '787 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_numbe...</td>\n", "      <td>{'job_title': 'Senior Software Engineer', 'dep...</td>\n", "      <td>{'nearest_office': 'Tokyo Office', 'is_remote'...</td>\n", "      <td>M987654</td>\n", "      <td>[Python, JavaScript, SQL, Docker]</td>\n", "      <td>[{'review_date': '2021-09-03', 'rating': 4.9, ...</td>\n", "      <td>{'health_insurance': 'Silver Plan', 'retiremen...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': '<PERSON><PERSON>...</td>\n", "      <td>Received Employee of the Month award in 2022.</td>\n", "      <td><PERSON>, Male, born on 1985-08-13. Job: Sen<PERSON>...</td>\n", "      <td>[-0.0072875600308179855, 0.013525711372494698,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>E123458</td>\n", "      <td>Emily</td>\n", "      <td><PERSON></td>\n", "      <td>Female</td>\n", "      <td>1972-07-22</td>\n", "      <td>{'street': '612 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_nu...</td>\n", "      <td>{'job_title': 'Data Scientist', 'department': ...</td>\n", "      <td>{'nearest_office': 'Paris Office', 'is_remote'...</td>\n", "      <td>M987655</td>\n", "      <td>[<PERSON><PERSON><PERSON>, <PERSON>de.js, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>]</td>\n", "      <td>[{'review_date': '2020-01-26', 'rating': 4.4, ...</td>\n", "      <td>{'health_insurance': 'Gold Plan', 'retirement_...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Spou...</td>\n", "      <td>Received Employee of the Month award in 2022.</td>\n", "      <td><PERSON>, Female, born on 1972-07-22. Job: ...</td>\n", "      <td>[-0.006489230785518885, 0.027***************, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>E123459</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Male</td>\n", "      <td>1992-10-27</td>\n", "      <td>{'street': '852 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': 'micha<PERSON>.<EMAIL>', 'phone_...</td>\n", "      <td>{'job_title': 'Product Manager', 'department':...</td>\n", "      <td>{'nearest_office': 'San Francisco Office', 'is...</td>\n", "      <td>M987656</td>\n", "      <td>[A<PERSON>, Node.js, Python, Django]</td>\n", "      <td>[{'review_date': '2023-02-10', 'rating': 4.2, ...</td>\n", "      <td>{'health_insurance': 'Gold Plan', 'retirement_...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Spouse'...</td>\n", "      <td>Actively involved in company hackathons and in...</td>\n", "      <td><PERSON>, Male, born on 1992-10-27. Job: ...</td>\n", "      <td>[-0.015239119529724121, -0.0020133587531745434...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>E123460</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Female</td>\n", "      <td>1962-02-11</td>\n", "      <td>{'street': '713 Main Street', 'city': 'Springf...</td>\n", "      <td>{'email': '<EMAIL>', 'phone_nu...</td>\n", "      <td>{'job_title': 'Project Manager', 'department':...</td>\n", "      <td>{'nearest_office': 'Chicago Office', 'is_remot...</td>\n", "      <td>M987657</td>\n", "      <td>[JavaScript, Flask, Django, SQL]</td>\n", "      <td>[{'review_date': '2023-07-02', 'rating': 3.2, ...</td>\n", "      <td>{'health_insurance': 'Gold Plan', 'retirement_...</td>\n", "      <td>{'name': '<PERSON>', 'relationship': 'Fri...</td>\n", "      <td>Actively involved in company hackathons and in...</td>\n", "      <td><PERSON>, Female, born on 1962-02-11. Job: ...</td>\n", "      <td>[0.017146248370409012, 0.004429043270647526, 0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f8b05e81-49ac-40de-ac42-84f78dc4e9e2')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -***********\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f8b05e81-49ac-40de-ac42-84f78dc4e9e2 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f8b05e81-49ac-40de-ac42-84f78dc4e9e2');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-6357274e-7f23-4414-b028-c6e976b710da\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-6357274e-7f23-4414-b028-c6e976b710da')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-6357274e-7f23-4414-b028-c6e976b710da button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["  employee_id first_name last_name  gender date_of_birth  \\\n", "0     E123456       <PERSON>    1990-06-26   \n", "1     E123457       <PERSON>    1985-08-13   \n", "2     E123458      <PERSON>    1972-07-22   \n", "3     E123459    <PERSON>    1992-10-27   \n", "4     E123460      <PERSON>    1962-02-11   \n", "\n", "                                             address  \\\n", "0  {'street': '650 Main Street', 'city': 'Springf...   \n", "1  {'street': '787 Main Street', 'city': 'Springf...   \n", "2  {'street': '612 Main Street', 'city': 'Springf...   \n", "3  {'street': '852 Main Street', 'city': 'Springf...   \n", "4  {'street': '713 Main Street', 'city': 'Springf...   \n", "\n", "                                     contact_details  \\\n", "0  {'email': '<EMAIL>', 'phone_numbe...   \n", "1  {'email': '<EMAIL>', 'phone_numbe...   \n", "2  {'email': '<EMAIL>', 'phone_nu...   \n", "3  {'email': 'micha<PERSON>.<EMAIL>', 'phone_...   \n", "4  {'email': '<EMAIL>', 'phone_nu...   \n", "\n", "                                         job_details  \\\n", "0  {'job_title': 'Software Engineer', 'department...   \n", "1  {'job_title': 'Senior Software Engineer', 'dep...   \n", "2  {'job_title': 'Data Scientist', 'department': ...   \n", "3  {'job_title': 'Product Manager', 'department':...   \n", "4  {'job_title': 'Project Manager', 'department':...   \n", "\n", "                                       work_location reporting_manager  \\\n", "0  {'nearest_office': 'Singapore Office', 'is_rem...           M987654   \n", "1  {'nearest_office': 'Tokyo Office', 'is_remote'...           M987654   \n", "2  {'nearest_office': 'Paris Office', 'is_remote'...           M987655   \n", "3  {'nearest_office': 'San Francisco Office', 'is...           M987656   \n", "4  {'nearest_office': 'Chicago Office', 'is_remot...           M987657   \n", "\n", "                                  skills  \\\n", "0   [Node.js, <PERSON>lask, <PERSON>er, JavaScript]   \n", "1      [Python, JavaScript, SQL, Docker]   \n", "2  [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>j<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>]   \n", "3         [<PERSON><PERSON>, Node.js, Python, Djan<PERSON>]   \n", "4       [JavaScript, Flask, Django, SQL]   \n", "\n", "                                 performance_reviews  \\\n", "0  [{'review_date': '2022-10-23', 'rating': 3.7, ...   \n", "1  [{'review_date': '2021-09-03', 'rating': 4.9, ...   \n", "2  [{'review_date': '2020-01-26', 'rating': 4.4, ...   \n", "3  [{'review_date': '2023-02-10', 'rating': 4.2, ...   \n", "4  [{'review_date': '2023-07-02', 'rating': 3.2, ...   \n", "\n", "                                            benefits  \\\n", "0  {'health_insurance': 'Silver Plan', 'retiremen...   \n", "1  {'health_insurance': 'Silver Plan', 'retiremen...   \n", "2  {'health_insurance': 'Gold Plan', 'retirement_...   \n", "3  {'health_insurance': 'Gold Plan', 'retirement_...   \n", "4  {'health_insurance': 'Gold Plan', 'retirement_...   \n", "\n", "                                   emergency_contact  \\\n", "0  {'name': '<PERSON>', 'relationship': 'Friend...   \n", "1  {'name': '<PERSON>', 'relationship': '<PERSON><PERSON>...   \n", "2  {'name': '<PERSON>', 'relationship': 'Spou...   \n", "3  {'name': '<PERSON>', 'relationship': 'Spouse'...   \n", "4  {'name': '<PERSON>', 'relationship': 'Fri...   \n", "\n", "                                               notes  \\\n", "0  Actively involved in company hackathons and in...   \n", "1      Received Employee of the Month award in 2022.   \n", "2      Received Employee of the Month award in 2022.   \n", "3  Actively involved in company hackathons and in...   \n", "4  Actively involved in company hackathons and in...   \n", "\n", "                                     employee_string  \\\n", "0  <PERSON>, Male, born on 1990-06-26. Job: Softw...   \n", "1  <PERSON>, Male, born on 1985-08-13. Job: Senio...   \n", "2  <PERSON>, Female, born on 1972-07-22. Job: ...   \n", "3  <PERSON>, Male, born on 1992-10-27. Job: ...   \n", "4  <PERSON>, Female, born on 1962-02-11. Job: ...   \n", "\n", "                                           embedding  \n", "0  [-0.03204594925045967, 0.018745997920632362, 0...  \n", "1  [-0.0072875600308179855, 0.013525711372494698,...  \n", "2  [-0.006489230785518885, 0.027***************, ...  \n", "3  [-0.015239119529724121, -0.0020133587531745434...  \n", "4  [0.017146248370409012, 0.004429043270647526, 0...  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Observe the new 'embedding' coloumn\n", "df_employees.head()"]}, {"cell_type": "markdown", "metadata": {"id": "MhO4jWndsWjR"}, "source": ["## 1.3 Data Ingestion into MongoDB Database\n", "\n", "**Steps to creating a MongoDB Database**\n", "- [Register for a free MongoDB Atlas Account](https://www.mongodb.com/cloud/atlas/register?utm_campaign=devrel&utm_source=workshop&utm_medium=organic_social&utm_content=rag%20to%20agents%20notebook&utm_term=richmond.alake)\n", "- [Create a Cluster](https://www.mongodb.com/docs/guides/atlas/cluster/)\n", "- [Get your connection string](https://www.mongodb.com/docs/guides/atlas/connection-string/)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4Pyd7qkrsYWA"}, "outputs": [], "source": ["MONGO_URI = os.environ.get(\"MONGO_URI\")\n", "\n", "OPENAI_API_KEY = os.environ.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**To be able to connect your notebook to MongoDB Atlas, you need to your IP Access List**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get your notebook's IP Address\n", "!curl ifconfig.me"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_HVOMMPIsYWH"}, "outputs": [], "source": ["from pymongo.mongo_client import MongoClient"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MZAnbELDsl_c"}, "outputs": [], "source": ["DATABASE_NAME = \"demo_company_employees\"\n", "COLLECTION_NAME = \"employees_records\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "psvw-xixsxCf"}, "outputs": [], "source": ["def get_mongo_client(mongo_uri):\n", "    \"\"\"Establish connection to the MongoDB.\"\"\"\n", "\n", "    # gateway to interacting with a MongoDB database cluster\n", "    client = MongoClient(mongo_uri, appname=\"devrel.showcase.workshop.rag_to_agent\")\n", "    print(\"Connection to MongoDB successful\")\n", "    return client"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2PKMkm18syb7", "outputId": "cb12686a-53cf-4c1e-fba8-6651d15e14fc"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection to MongoDB successful\n"]}], "source": ["if not MONGO_URI:\n", "    print(\"MONGO_URI not set in environment variables\")\n", "\n", "mongo_client = get_mongo_client(MONGO_URI)\n", "\n", "# Pymongo client of database and collection\n", "db = mongo_client.get_database(DATABASE_NAME)\n", "collection = db.get_collection(COLLECTION_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "V6SGOyBXzAYL"}, "outputs": [], "source": ["documents = df_employees.to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fTraqF-jBR08", "outputId": "aed8bbf6-c86d-491b-9123-372da2ac9c65"}, "outputs": [{"data": {"text/plain": ["DeleteResult({'n': 10, 'electionId': ObjectId('7fffffff0000000000000027'), 'opTime': {'ts': Timestamp(1718207302, 10), 't': 39}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1718207302, 10), 'signature': {'hash': b\"\\x8a\\x85$\\xbf\\xed'\\xc5\\xf8\\xe6\\x1eJ5@w8\\xf6\\x82\\xf3\\x16u\", 'keyId': 7320226449804230662}}, 'operationTime': Timestamp(1718207302, 10)}, acknowledged=True)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Clean up collection of exisiting record\n", "collection.delete_many({})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "C2Zg5yDAs1LQ", "outputId": "1aa47a39-3a17-43c1-f897-83679e3f23c2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data ingestion into MongoDB completed\n"]}], "source": ["# Ingest data into MongoDB Database\n", "collection.insert_many(documents)\n", "print(\"Data ingestion into MongoDB completed\")"]}, {"cell_type": "markdown", "metadata": {"id": "B8VZ-c4qt92b"}, "source": ["## 1.4 Vector Index Creation\n", "\n", "- [Create an Atlas Vector Search Index](https://www.mongodb.com/docs/compass/current/indexes/create-vector-search-index/)\n", "\n", "- If you are following this notebook ensure that you are creating a vector search index for the right database(demo_company_employees) and collection(employees_records)\n", "\n", "Below is the vector search index definition for this notebook\n", "\n", "```json\n", "{\n", "  \"fields\": [\n", "    {\n", "      \"numDimensions\": 1536,\n", "      \"path\": \"embedding\",\n", "      \"similarity\": \"cosine\",\n", "      \"type\": \"vector\"\n", "    }\n", "  ]\n", "}\n", "```\n", "\n", "- Give your vector search index the name \"vector_index\" if you are following this notebook\n"]}, {"cell_type": "markdown", "metadata": {"id": "EC6nU1NSuFqO"}, "source": ["## 1.5 RAG with MongoDB"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "496k9PvZuN6H"}, "outputs": [], "source": ["def vector_search(user_query, collection, vector_index=\"vector_index\"):\n", "    \"\"\"\n", "    Perform a vector search in the MongoDB collection based on the user query.\n", "\n", "    Args:\n", "    user_query (str): The user's query string.\n", "    db (MongoClient.database): The database object.\n", "    collection (MongoCollection): The MongoDB collection to search.\n", "    additional_stages (list): Additional aggregation stages to include in the pipeline.\n", "\n", "    Returns:\n", "    list: A list of matching documents.\n", "    \"\"\"\n", "\n", "    # Generate embedding for the user query\n", "    query_embedding = get_embedding(user_query)\n", "\n", "    if query_embedding is None:\n", "        return \"Invalid query or embedding generation failed.\"\n", "\n", "    # Define the vector search stage\n", "    vector_search_stage = {\n", "        \"$vectorSearch\": {\n", "            \"index\": vector_index,  # specifies the index to use for the search\n", "            \"queryVector\": query_embedding,  # the vector representing the query\n", "            \"path\": \"embedding\",  # field in the documents containing the vectors to search against\n", "            \"numCandidates\": 150,  # number of candidate matches to consider\n", "            \"limit\": 5,  # return top 20 matches\n", "        }\n", "    }\n", "\n", "    # Define the aggregate pipeline with the vector search stage and additional stages\n", "    pipeline = [vector_search_stage]\n", "\n", "    # Execute the search\n", "    results = collection.aggregate(pipeline)\n", "\n", "    return list(results)"]}, {"cell_type": "markdown", "metadata": {"id": "4UaKjc5nugfd"}, "source": ["## 1.6 Handling User Query"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KFObFgOEuiJ3"}, "outputs": [], "source": ["def handle_user_query(query, collection):\n", "    get_knowledge = vector_search(query, collection)\n", "\n", "    # Concatenate the search results to reflect the employee profile\n", "    search_result = \"\"\n", "\n", "    for result in get_knowledge:\n", "        reporting_manager = result.get(\"reporting_manager\")\n", "        if isinstance(reporting_manager, dict):\n", "            manager_id = reporting_manager.get(\"manager_id\", \"N/A\")\n", "        else:\n", "            manager_id = \"N/A\"\n", "\n", "        employee_profile = f\"\"\"\n", "      Employee ID: {result.get('employee_id', 'N/A')}\n", "      Name: {result.get('first_name', 'N/A')} {result.get('last_name', 'N/A')}\n", "      Gender: {result.get('gender', 'N/A')}\n", "      Date of Birth: {result.get('date_of_birth', 'N/A')}\n", "      Address: {result.get('address', {}).get('street', 'N/A')}, {result.get('address', {}).get('city', 'N/A')}, {result.get('address', {}).get('state', 'N/A')}, {result.get('address', {}).get('postal_code', 'N/A')}, {result.get('address', {}).get('country', 'N/A')}\n", "      Contact Details: Email - {result.get('contact_details', {}).get('email', 'N/A')}, Phone - {result.get('contact_details', {}).get('phone_number', 'N/A')}\n", "      Job Details: Title - {result.get('job_details', {}).get('job_title', 'N/A')}, Department - {result.get('job_details', {}).get('department', 'N/A')}, Hire Date - {result.get('job_details', {}).get('hire_date', 'N/A')}, Type - {result.get('job_details', {}).get('employment_type', 'N/A')}, Salary - {result.get('job_details', {}).get('salary', 'N/A')} {result.get('job_details', {}).get('currency', 'N/A')}\n", "      Work Location: Nearest Office - {result.get('work_location', {}).get('nearest_office', 'N/A')}, Remote - {result.get('work_location', {}).get('is_remote', 'N/A')}\n", "      Reporting Manager: ID - {manager_id}\n", "      Skills: {', '.join(result.get('skills', ['N/A']))}\n", "      Performance Reviews: {', '.join([f\"Date: {review.get('review_date', 'N/A')}, Rating: {review.get('rating', 'N/A')}, Comments: {review.get('comments', 'N/A')}\" for review in result.get('performance_reviews', [])])}\n", "      Benefits: Health Insurance - {result.get('benefits', {}).get('health_insurance', 'N/A')}, Retirement Plan - {result.get('benefits', {}).get('retirement_plan', 'N/A')}, PTO - {result.get('benefits', {}).get('paid_time_off', 'N/A')} days\n", "      Emergency Contact: Name - {result.get('emergency_contact', {}).get('name', 'N/A')}, Relationship - {result.get('emergency_contact', {}).get('relationship', 'N/A')}, Phone - {result.get('emergency_contact', {}).get('phone_number', 'N/A')}\n", "      Notes: {result.get('notes', 'N/A')}\n", "      \"\"\"\n", "        search_result += employee_profile + \"\\n\"\n", "\n", "    prompt = (\n", "        \"Answer this user query: \"\n", "        + query\n", "        + \" with the following context: \"\n", "        + search_result\n", "    )\n", "    print(\"Uncompressed Prompt:\\n\")\n", "    print(prompt)\n", "\n", "    completion = openai.chat.completions.create(\n", "        model=OPEN_AI_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"You are an Human Resource System within a corporate company.\",\n", "            },\n", "            {\"role\": \"user\", \"content\": prompt},\n", "        ],\n", "    )\n", "\n", "    return (completion.choices[0].message.content), search_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "suRAJc411uZh", "outputId": "29bb1f06-0bb8-419a-cda2-72d91ab10bb6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Uncompressed Prompt:\n", "\n", "Answer this user query: Who is the CEO? with the following context: \n", "Response: Please provide the name of your company or any additional context that will help me identify the current CEO.\n"]}], "source": ["# Conduct query with retrival of sources\n", "query = \"Who is the CEO?\"\n", "response, source_information = handle_user_query(query, collection)\n", "\n", "print(f\"Response: {response}\")"]}, {"cell_type": "markdown", "metadata": {"id": "BKdB25EMukQO"}, "source": ["## 1.7 Handling User Query (With Prompt Compression)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NGaKMrPH_szB"}, "outputs": [], "source": ["# Uncomment and run the following line if a hardware accelerator(gpu) is available in your development environment:\n", "# ! pip install optimum auto-gptq"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 264, "referenced_widgets": ["2bec76bc0a61410a9d13bfa19cf1e8fe", "3a58ddbbd0d84265a16775b72a4c1700", "6c7980f7fe89499fbc802b96e7912ec9", "98b166b52ceb4c0983afb941b2fa3d89", "9b1a5bd5f30f49fcb482f66a8be0a3c4", "0a4842103ee643819288478d10e1f5bf", "019719ef505e4195a3949a3377b56d30", "bd97594b64314022996f832ede459a57", "a68cd234da99493cb972e5cf2dd7876a", "22c56a8202cb464a96fa10fec9530a3b", "5017f719739744d9b79f5a7319ae66aa", "e7720af430484ac9b6f043b5a7b0caf7", "5868884d768b439cb4acaabfba7c923f", "9f69551508804a6f8e98141b1fdc69cd", "8385e62b7f614135ad4caecfa1acd6e5", "bfc151053b514af784bab696b319fe9c", "661a42f00e52448ca2da806a682471b9", "34b5c14eb8d5458d88021619fd922870", "818bd02163c54e499dee383d132d4edf", "d37b5e7152c9487b8b1f69061734378d", "f2793c25172342ba9bf0764fe0d2ff9f", "be0fdf7ee4b64bcb8e9c4885ee31c236", "d30cb1946dd240f29cd1bfe134175c3e", "5722cd31511743228a3a9b1cfaa6046b", "a718c10ba3b34003bc77347add93d510", "778da85e2f964d3ea7d182f02ef9b157", "8ac8fda081464c07bf5ba56eeda46cb0", "6dafd1fac8e3403fa7952ab26a9c2b88", "5d0b70bbf7a347ae978a6ef420b3d24c", "667a3931517447998a36f9e2c008f167", "9537cf878f724a2ca3f32159df928854", "72f00a37ef474b2e8995a0adb1ed14f5", "a5f9f7e2dce949a0b8f155d139e63bcc", "485d120e97d84490a748637ffcf9acfc", "2ca04d5d0d1f4119b8a09378d607027b", "be66d6ca168b47b285bf87508d41b0a7", "748d0ce8cebf419da635760804eda8ad", "ffae8e5bf50f4d27a97ba71f54cf8dbe", "51d6af68ab104106b27f1a36679307af", "11ec0174c492444a80781491eba487a9", "954c2fb207e8435087a041757e7f4db9", "4172588542704c22a9dc3d18d85d005b", "8a1a1c2c99ce4503b45a2f8aacbbd0aa", "571d73c95bf94f55afb95ec211db04b9", "b57672e0a393488a90bd710e6f5142df", "40f6e6d8d31e4a80b564f9680fc3086f", "b32cc92d88034412a7565144f2c87e6c", "bb767daff1024bd2b7b24fcbedd44dc2", "b95955e7a17c4da19a6265d8f14fc2dd", "269551fabedf43b6b4908c3a2689e349", "8771925543b34e6ca9ee17c54376a96e", "3305584369b34cbc849657a2f139d9aa", "2313dec83052473db2a816c55b13c541", "a6ba05bb53224bfbb5066a2e45374b6a", "bdcca25b302c467daabe44031c77b5fa", "98b02f8ee4e64bc7961726eb8579ed43", "76955346f2bf47c3a4f0e1310fcaf1e0", "7c8dce3661f44eb792f8a77149bc9121", "7693215e866040199c7c1fe4d4a5c95b", "49998b99218049c6924788fe63495164", "505fb9d7c0fa4f5ebe66c9de5e28303b", "cc6e978269d2426eb1f6645079fb1f4f", "63c5ce6a7549461a94f4fa331d407cc0", "7dc3b352b49e4f0d98103a1d9c651c02", "7a413b5421a644c1a9485417328a2287", "3d08f5c00f484a06b912b6fe39b8b5dd"]}, "id": "mPTEz_vRRxds", "outputId": "29746a13-b982-483f-8391-b1df802976f9"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2bec76bc0a61410a9d13bfa19cf1e8fe", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/875 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e7720af430484ac9b6f043b5a7b0caf7", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/1.19k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d30cb1946dd240f29cd1bfe134175c3e", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt:   0%|          | 0.00/996k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "485d120e97d84490a748637ffcf9acfc", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/2.92M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b57672e0a393488a90bd710e6f5142df", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/125 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "98b02f8ee4e64bc7961726eb8579ed43", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/709M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from llmlingua import PromptCompressor\n", "\n", "llm_lingua = PromptCompressor(\n", "    model_name=\"microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank\",\n", "    model_config={\"revision\": \"main\"},\n", "    use_llmlingua2=True,\n", "    device_map=\"cpu\",  # change to 'cuda' if gpu is availabe on device\n", ")\n", "\n", "\n", "# Function definition\n", "def compress_query_prompt(context):\n", "    compressed_prompt = llm_lingua.compress_prompt(\n", "        str(context),\n", "        rate=0.33,\n", "        force_tokens=[\"!\", \".\", \"?\", \"\\n\"],\n", "        drop_consecutive=True,\n", "    )\n", "\n", "    print(\"------\")\n", "    print(compressed_prompt)\n", "    print(\"-------\")\n", "\n", "    return compressed_prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WtG8-7bF4qI-"}, "outputs": [], "source": ["import pprint\n", "\n", "\n", "def handle_user_query_with_compression(query, collection):\n", "    get_knowledge = vector_search(query, collection)\n", "\n", "    # Concatenate the search results to reflect the employee profile\n", "    search_result = \"\"\n", "\n", "    for result in get_knowledge:\n", "        employee_profile = f\"\"\"\n", "      Employee ID: {result.get('employee_id', 'N/A')}\n", "      Name: {result.get('first_name', 'N/A')} {result.get('last_name', 'N/A')}\n", "      Gender: {result.get('gender', 'N/A')}\n", "      Date of Birth: {result.get('date_of_birth', 'N/A')}\n", "      Address: {result.get('address', {}).get('street', 'N/A')}, {result.get('address', {}).get('city', 'N/A')}, {result.get('address', {}).get('state', 'N/A')}, {result.get('address', {}).get('postal_code', 'N/A')}, {result.get('address', {}).get('country', 'N/A')}\n", "      Contact Details: Email - {result.get('contact_details', {}).get('email', 'N/A')}, Phone - {result.get('contact_details', {}).get('phone_number', 'N/A')}\n", "      Job Details: Title - {result.get('job_details', {}).get('job_title', 'N/A')}, Department - {result.get('job_details', {}).get('department', 'N/A')}, Hire Date - {result.get('job_details', {}).get('hire_date', 'N/A')}, Type - {result.get('job_details', {}).get('employment_type', 'N/A')}, Salary - {result.get('job_details', {}).get('salary', 'N/A')} {result.get('job_details', {}).get('currency', 'N/A')}\n", "      Work Location: Nearest Office - {result.get('work_location', {}).get('nearest_office', 'N/A')}, Remote - {result.get('work_location', {}).get('is_remote', 'N/A')}\n", "      Skills: {', '.join(result.get('skills', ['N/A']))}\n", "      Performance Reviews: {', '.join([f\"Date: {review.get('review_date', 'N/A')}, Rating: {review.get('rating', 'N/A')}, Comments: {review.get('comments', 'N/A')}\" for review in result.get('performance_reviews', [])])}\n", "      Benefits: Health Insurance - {result.get('benefits', {}).get('health_insurance', 'N/A')}, Retirement Plan - {result.get('benefits', {}).get('retirement_plan', 'N/A')}, PTO - {result.get('benefits', {}).get('paid_time_off', 'N/A')} days\n", "      Emergency Contact: Name - {result.get('emergency_contact', {}).get('name', 'N/A')}, Relationship - {result.get('emergency_contact', {}).get('relationship', 'N/A')}, Phone - {result.get('emergency_contact', {}).get('phone_number', 'N/A')}\n", "      Notes: {result.get('notes', 'N/A')}\n", "      \"\"\"\n", "        search_result += employee_profile + \"\\n\"\n", "\n", "    # Prepare information for compression\n", "    query_info = {\n", "        \"demonstration_str\": search_result,  # Results from information retrieval process\n", "        \"instruction\": \"Write a high-quality answer for the given question using only the provided search results.\",\n", "        \"question\": query,\n", "    }\n", "\n", "    # Compress the query prompt\n", "    compressed_prompt = compress_query_prompt(query_info)\n", "\n", "    prompt = f\"Answer this user query: {query} with the following context:\\n{compressed_prompt}\"\n", "    print(\"Compressed Prompt:\\n\")\n", "    pprint.pprint(prompt)\n", "\n", "    completion = openai.chat.completions.create(\n", "        model=OPEN_AI_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"You are an Human Resource System within a corporate company.\",\n", "            },\n", "            {\"role\": \"user\", \"content\": prompt},\n", "        ],\n", "    )\n", "\n", "    return (completion.choices[0].message.content), search_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QLak6FTC6FS7", "outputId": "d8a1e7a3-5925-4aea-9b46-217a3076bbff"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (1434 > 512). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["------\n", "{'compressed_prompt': 'Employee ID E123465 <PERSON> Birth 1952 - 01 - 05 959 Main Street Springfield IL 62704. martinez. 555 - 675 - 3033 CEO 2015 09 16 53606 USD Sydney. Performance Reviews 2020 - 10 - 09. 5 time management. Benefits Health Insurance Silver Plan Retirement Plan 401K PTO 28 Emergency Michael <PERSON> - 555 - 465 - 9759 Received Employee of Month award 2022. Employee ID E123463 <PERSON> 1960 - 03 - 22 523 Main Street Springfield 62704. 928 5679 DevOps Engineer Department Operations 2001 07 28 227846 USD Toronto Docker AWS 2022 12 28. 5. 10 19 3. 6 expectations last project.Benefits Health Insurance - Gold Plan Retirement Plan 401K 26 days Emergency Contact Michael Smith Relationship Parent 555 - 613 - 9745 Completed leadership training 2021. Employee ID E123464 <PERSON> Birth 1998 - 06 - 13 374 Main Street Springfield IL 62704. 2568 CTO Executive 2011 06 17 128311 USD Nearest Office San Francisco. Performance 2023 - 11 - 02 3. 5. 2020 11 04. 4 time management. Gold Plan 401K 29 days Emily Doe Relationship Spouse 2565 Senior Software Engineer 2020. Employee ID E123456 <PERSON> Birth 1990 06 - 26 650 Main Street Springfield 62704. 2182 Software Engineer 68688 USD Office Singapore.Flask Docker JavaScript Performance Reviews 2022 - 10 - 23 3. 7 performance dedication. 07 24 4. 9 time management. Health Insurance Silver Plan 401K 28 days Emergency Contact Jane <PERSON> - 555 - 765 5544. Employee ID E123461 <PERSON> <PERSON> 1955 - 06 - 17 462 Main Street Springfield IL 62704 robert. johnson. 449 3367 Designer Design 2006 10 - 08 235758 USD Singapore SQL Kubernetes AWS Python Performance 2021 11 06 4. 6 expectations. 2020 02 - 22 4. 5. Health Insurance Bronze Plan 401K PTO 29 days Jane Smith ************ leadership training 2021. answer.?', 'compressed_prompt_list': ['Employee ID E123465 Olivia Martinez Female Birth 1952 - 01 - 05 959 Main Street Springfield IL 62704. martinez. 555 - 675 - 3033 CEO 2015 09 16 53606 USD Sydney. Performance Reviews 2020 - 10 - 09. 5 time management. Benefits Health Insurance Silver Plan Retirement Plan 401K PTO 28 Emergency Michael Doe - 555 - 465 - 9759 Received Employee of Month award 2022. Employee ID E123463 Chris Lee Male 1960 - 03 - 22 523 Main Street Springfield 62704. 928 5679 DevOps Engineer Department Operations 2001 07 28 227846 USD Toronto Docker AWS 2022 12 28. 5. 10 19 3. 6 expectations last project.Benefits Health Insurance - Gold Plan Retirement Plan 401K 26 days Emergency Contact Michael Smith Relationship Parent 555 - 613 - 9745 Completed leadership training 2021. Employee ID E123464 Sophia Garcia Birth 1998 - 06 - 13 374 Main Street Springfield IL 62704. 2568 CTO Executive 2011 06 17 128311 USD Nearest Office San Francisco. Performance 2023 - 11 - 02 3. 5. 2020 11 04. 4 time management. Gold Plan 401K 29 days Emily Doe Relationship Spouse 2565 Senior Software Engineer 2020. Employee ID E123456 John Doe Birth 1990 06 - 26 650 Main Street Springfield 62704. 2182 Software Engineer 68688 USD Office Singapore.Flask Docker JavaScript Performance Reviews 2022 - 10 - 23 3. 7 performance dedication. 07 24 4. 9 time management. Health Insurance Silver Plan 401K 28 days Emergency Contact Jane Smith - 555 - 765 5544. Employee ID E123461 Robert Johnson 1955 - 06 - 17 462 Main Street Springfield IL 62704 robert. johnson. 449 3367 Designer Design 2006 10 - 08 235758 USD Singapore SQL Kubernetes AWS Python Performance 2021 11 06 4. 6 expectations. 2020 02 - 22 4. 5. Health Insurance Bronze Plan 401K PTO 29 days Jane Smith ************ leadership training 2021. answer.?'], 'origin_tokens': 1344, 'compressed_tokens': 527, 'ratio': '2.6x', 'rate': '39.2%', 'saving': ', Saving $0.0 in GPT-4.'}\n", "-------\n", "Compressed Prompt:\n", "\n", "('Answer this user query: Who is the CEO? with the following context:\\n'\n", " \"{'compressed_prompt': 'Employee ID E123465 <PERSON> Birth 1952 \"\n", " '- 01 - 05 959 Main Street Springfield IL 62704. martinez. 555 - 675 - 3033 '\n", " 'CEO 2015 09 16 53606 USD Sydney. Performance Reviews 2020 - 10 - 09. 5 time '\n", " 'management. Benefits Health Insurance Silver Plan Retirement Plan 401K PTO '\n", " '28 Emergency <PERSON> - 555 - 465 - 9759 Received Employee of Month award '\n", " '2022. Employee ID E123463 <PERSON> 1960 - 03 - 22 523 Main Street '\n", " 'Springfield 62704. 928 5679 DevOps Engineer Department Operations 2001 07 28 '\n", " '227846 USD Toronto Docker AWS 2022 12 28. 5. 10 19 3. 6 expectations last '\n", " 'project.Benefits Health Insurance - Gold Plan Retirement Plan 401K 26 days '\n", " 'Emergency Contact <PERSON> Relationship Parent 555 - 613 - 9745 '\n", " 'Completed leadership training 2021. Employee ID E123464 <PERSON> '\n", " '1998 - 06 - 13 374 Main Street Springfield IL 62704. 2568 CTO Executive 2011 '\n", " '06 17 128311 USD Nearest Office San Francisco. Performance 2023 - 11 - 02 3. '\n", " '5. 2020 11 04. 4 time management. Gold Plan 401K 29 days <PERSON> '\n", " 'Relationship Spouse 2565 Senior Software Engineer 2020. Employee ID E123456 '\n", " '<PERSON> Birth 1990 06 - 26 650 Main Street Springfield 62704. 2182 Software '\n", " 'Engineer 68688 USD Office Singapore.Flask Docker JavaScript Performance '\n", " 'Reviews 2022 - 10 - 23 3. 7 performance dedication. 07 24 4. 9 time '\n", " 'management. Health Insurance Silver Plan 401K 28 days Emergency Contact Jane '\n", " 'Smith - 555 - 765 5544. Employee ID E123461 <PERSON> 1955 - 06 - 17 '\n", " '462 Main Street Springfield IL 62704 robert. johnson. 449 3367 Designer '\n", " 'Design 2006 10 - 08 235758 USD Singapore SQL Kubernetes AWS Python '\n", " 'Performance 2021 11 06 4. 6 expectations. 2020 02 - 22 4. 5. Health '\n", " 'Insurance Bronze Plan 401K PTO 29 days <PERSON> ************ leadership '\n", " \"training 2021. answer.?', 'compressed_prompt_list': ['Employee ID E123465 \"\n", " '<PERSON> Birth 1952 - 01 - 05 959 Main Street Springfield IL '\n", " '62704. martinez. 555 - 675 - 3033 CEO 2015 09 16 53606 USD Sydney. '\n", " 'Performance Reviews 2020 - 10 - 09. 5 time management. Benefits Health '\n", " 'Insurance Silver Plan Retirement Plan 401K PTO 28 Emergency <PERSON> - '\n", " '555 - 465 - 9759 Received Employee of Month award 2022. Employee ID E123463 '\n", " '<PERSON> 1960 - 03 - 22 523 Main Street Springfield 62704. 928 5679 '\n", " 'DevOps Engineer Department Operations 2001 07 28 227846 USD Toronto Docker '\n", " 'AWS 2022 12 28. 5. 10 19 3. 6 expectations last project.Benefits Health '\n", " 'Insurance - Gold Plan Retirement Plan 401K 26 days Emergency Contact Michael '\n", " 'Smith Relationship Parent 555 - 613 - 9745 Completed leadership training '\n", " '2021. Employee ID E123464 <PERSON> Birth 1998 - 06 - 13 374 Main Street '\n", " 'Springfield IL 62704. 2568 CTO Executive 2011 06 17 128311 USD Nearest '\n", " 'Office San Francisco. Performance 2023 - 11 - 02 3. 5. 2020 11 04. 4 time '\n", " 'management. Gold Plan 401K 29 days <PERSON> Relationship Spouse 2565 Senior '\n", " 'Software Engineer 2020. Employee ID E123456 <PERSON> Birth 1990 06 - 26 650 '\n", " 'Main Street Springfield 62704. 2182 Software Engineer 68688 USD Office '\n", " 'Singapore.Flask Docker JavaScript Performance Reviews 2022 - 10 - 23 3. 7 '\n", " 'performance dedication. 07 24 4. 9 time management. Health Insurance Silver '\n", " 'Plan 401K 28 days Emergency Contact <PERSON> - 555 - 765 5544. Employee ID '\n", " 'E123461 <PERSON> 1955 - 06 - 17 462 Main Street Springfield IL 62704 '\n", " 'robert. johnson. 449 3367 Designer Design 2006 10 - 08 235758 USD Singapore '\n", " 'SQL Kubernetes AWS Python Performance 2021 11 06 4. 6 expectations. 2020 02 '\n", " '- 22 4. 5. Health Insurance Bronze Plan 401K PTO 29 days <PERSON> 555 687 '\n", " \"6856 leadership training 2021. answer.?'], 'origin_tokens': 1344, \"\n", " \"'compressed_tokens': 527, 'ratio': '2.6x', 'rate': '39.2%', 'saving': ', \"\n", " \"Saving $0.0 in GPT-4.'}\")\n", "Response: The CEO of the company is <PERSON>.\n"]}], "source": ["# Conduct query with retrival of sources\n", "query = \"Who is the CEO?\"\n", "response, source_information = handle_user_query_with_compression(query, collection)\n", "\n", "print(f\"Response: {response}\")"]}, {"cell_type": "markdown", "metadata": {"id": "ALrfaObSteOs"}, "source": ["# Part 2: RAG Application: HR Use Case (POLM AI Stack)\n"]}, {"cell_type": "markdown", "metadata": {"id": "DWK6DxuQjmhp"}, "source": ["### RAG with Lang<PERSON>in and MongoDB"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "szCe-LoBktkA", "outputId": "38216e97-c4a3-457a-8988-d1c06c0a8391"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m974.0/974.0 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m65.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m314.7/314.7 kB\u001b[0m \u001b[31m30.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m125.2/125.2 kB\u001b[0m \u001b[31m15.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.2/49.2 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.7/142.7 kB\u001b[0m \u001b[31m14.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install --upgrade --quiet langchain langchain-mongodb langchain-openai langchain_community pymongo"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZYCjE5x6ljZ9"}, "outputs": [], "source": ["from langchain_mongodb import MongoDBAtlasVectorSearch\n", "from langchain_openai import ChatOpenAI, OpenAIEmbeddings\n", "\n", "embedding_model = OpenAIEmbeddings(\n", "    model=OPEN_AI_EMBEDDING_MODEL, dimensions=OPEN_AI_EMBEDDING_MODEL_DIMENSION\n", ")\n", "\n", "# Vector Store Creation\n", "vector_store = MongoDBAtlasVectorSearch.from_connection_string(\n", "    connection_string=MONGO_URI,\n", "    namespace=DATABASE_NAME + \".\" + COLLECTION_NAME,\n", "    embedding=embedding_model,\n", "    index_name=\"vector_index\",\n", "    text_key=\"employee_string\",\n", ")\n", "\n", "retriever = vector_store.as_retriever(search_type=\"similarity\", search_kwargs={\"k\": 5})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4s1bVeteo39y"}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate\n", "\n", "# Define a prompt template\n", "template = \"\"\"\n", "Use the following pieces of context to answer the question at the end.\n", "If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "{context}\n", "Question: {question}\n", "\"\"\"\n", "custom_rag_prompt = PromptTemplate.from_template(template)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZiUFO8sqo5AZ"}, "outputs": [], "source": ["llm = ChatOpenAI()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UNQcG5jLqRkD"}, "outputs": [], "source": ["def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "8iPDTWQio86X"}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "# Construct a chain to answer questions on your data\n", "rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | custom_rag_prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")\n", "# Prompt the chain\n", "question = \"Who is the CEO??\"\n", "answer = rag_chain.invoke(question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "26uyQIMHpAhv", "outputId": "b3b43bf9-ba33-4c63-edff-08b2c2598bae"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Question: Who is the CEO??\n", "Answer: <PERSON> is the CEO.\n"]}], "source": ["print(\"Question: \" + question)\n", "print(\"Answer: \" + answer)"]}, {"cell_type": "markdown", "metadata": {"id": "rnSuWk2cqxtq"}, "source": ["#### Prompt Compression with <PERSON><PERSON><PERSON><PERSON> and LLMLingua"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6mPKl0vLrbFg"}, "outputs": [], "source": ["from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain_community.document_compressors import LLMLinguaCompressor"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "referenced_widgets": ["2736f54b4c69460aa26f282b57df6062", "49f54ca0c5554a4fa83475d25ac1de86", "c3ade9cc493a4c8095ad61b2d6e10a68", "d4a2bea3de76466baccaa58bea0e4e10", "2c175f0a69fd4145b32d96319f8458a0", "ca2d3143b86a4d859075432856e6e3f1", "b96b41705fb442a19a4fab35a148308f", "d1af2b459b614e60b67a0cfec1dc8820", "768c62a7bb6c439f9796b074b7e7bcfa", "c41416abc37d4b3e8f25c09dfaa4b279", "1c7824f63f2b4830a912c994491949ea", "03ef423561244611bb84194ff5e16e62", "cfa56fe3e8344d929de0d039ddccf123", "f539aa41eec4432a9b8913cb2728d8f3", "4e27764e680e4d6b9d3cf382fbaf4af3", "c3d2e5e0424d40a7ae3f558273752b0c", "1a20f211e2134ecfbaf93cb0360ffb01", "5b74c17e75fb497299e9c1530a728c31", "c2a04b12483046a19a4e976733c6d0c0", "ec7b5206d22b4adb9a4f7af6dafaebb5", "ddd46e7d4d3b4aed85a79be1495ef390", "937c1c93228845db9317d299d08cdbde", "e7c87974b78049eaaf39349c583c5b17", "eb43ca8e12854bab83f3006444931965", "d341cee699fb484e915e5f3c4ecc8747", "c0591a9484134dd3b457b9668208167e", "32c5f0f4904d4892b1af67ad7c24bc9d", "75827958f1364e36a264500a3212be5f", "5153b484addd455d808df819c8cea810", "1bc7bcda3df64b5aae373b0549199b11", "707aee13704849ab9d773ef023718a9c", "e20d091e71f6408681ccd65fcddaae43", "d2f86a96f1cb4a79aeccbb108b75545b", "e501066eb3004cf4b41d8688217955fe", "9a3981bd1e3641ee945e53fbb6ddc1fa", "cb131570e5f9484aba06aa617e36ef4c", "01b0df8bdef24414a58e66a22c153c27", "ba9f5e4371b44f1dadc915ce8bc723d3", "b046a671c0d34b9680f60479fa448a24", "6c2b4e78356545a99908d877187e004e", "710be8bf3d4a491d894abd3df47ba431", "84dbdafd20664eae8107360a0d4414fa", "fab87ba2f882420fa196cbccf8f0e527", "81ed26eace8a410bb32cf07184bbf5a6", "fabe74c986d148c0b4677627826377d1", "9027bfdc32ef4f5da50d1b8db94064c4", "04ea4736ba124bf8b7bf134034df99cf", "2663f37beb344e7f820553e62f75f8eb", "f126e6c8f6ed4798973562c9f545f2bb", "8af2e6048b19443a823ac73484bbe78e", "56460f46535b44e6882f7c69a5f28b15", "425118acf1a942388d06b8df85969e16", "e29d42772ac14455b7e865f3564d884d", "25ee621e6df94981ae56a0c3ba014b77", "c6f1ff2e984646d9a54ef416c84ae86c", "ee8f79e18ea748618f8e680943360a05", "2843b96f076247f288890bd01e9bbb9f", "80d3feb5c6df496b8eb1384f9e679c0d", "4b759c4c9cb14b8899a25ef227f7344b", "9f4a0a4ec0d44c39b72d3122829dc833", "49bd1bbd3248497786b53ec084269aea", "988c76ffa0d44bc29f7c976c471fc2bb", "1188156d723149998bfad83e08367b31", "3b42ec9d44864160aea9a60fea759dc5", "a0f6f95b032a4cb6bd183946641ada74", "216e8c43ac764438829913a23d837d22", "d97cc41e6c8d4c19b1bec11467be06bb", "811e576944f64c389bc9d3597f29f60a", "00d416447b384df9a6693aabc1d7b066", "f844ce795f944faead3c53de7abbc839", "fac7d27c4f274302ae5302d0d7bae26f", "3aa58481baad48108ace10d930c9b67a", "cd6dd979e9264438b833ee502920a8c4", "f524a94f5e17404fbb4136d8353d7e83", "deef824e229a4dda8f5b101d01b539e5", "a4d38b15d8994408b5210b9cee1af094", "9a48da0564b74ed1bfc1a3ac2d4c8104"]}, "id": "yoUBTzP7rgsj", "outputId": "755b31cb-54b9-4767-98aa-d16d0f3ad63f"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2736f54b4c69460aa26f282b57df6062", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/665 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03ef423561244611bb84194ff5e16e62", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/26.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e7c87974b78049eaaf39349c583c5b17", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.json:   0%|          | 0.00/1.04M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e501066eb3004cf4b41d8688217955fe", "version_major": 2, "version_minor": 0}, "text/plain": ["merges.txt:   0%|          | 0.00/456k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fabe74c986d148c0b4677627826377d1", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/1.36M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ee8f79e18ea748618f8e680943360a05", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/548M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d97cc41e6c8d4c19b1bec11467be06bb", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/124 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compressor = LLMLinguaCompressor(model_name=\"openai-community/gpt2\", device_map=\"cpu\")\n", "compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=compressor, base_retriever=retriever\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wK8Sn3C6rjKr", "outputId": "1237d0e6-c2dc-40ce-db9c-0af2b8095854"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(page_content='<PERSON>, Female, born on 1952-01-05. Job: CEO in Executive. Skills: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>las<PERSON>, Node.js. Reviews: Rated 4.5 on 2020-10-09: Needs improvement in time management. Rated 3.2 on 2021-01-08: Consistently meets performance standards.. Location: Works at Sydney Office, Remote: False. Notes: Received Employee of the Month award in 2022.', metadata={'_id': {'$oid': '6669c346ce0888213014cce4'}, 'employee_id': 'E123465', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'gender': 'Female', 'date_of_birth': '1952-01-05', 'address': {'street': '959 Main Street', 'city': 'Springfield', 'state': 'IL', 'postal_code': '62704', 'country': 'USA'}, 'contact_details': {'email': '<EMAIL>', 'phone_number': '******-675-3033'}, 'job_details': {'job_title': 'CEO', 'department': 'Executive', 'hire_date': '2015-09-16', 'employment_type': 'Full-Time', 'salary': 53606, 'currency': 'USD'}, 'work_location': {'nearest_office': 'Sydney Office', 'is_remote': False}, 'reporting_manager': None, 'skills': ['Docker', 'React', 'Flask', 'Node.js'], 'performance_reviews': [{'review_date': '2020-10-09', 'rating': 4.5, 'comments': 'Needs improvement in time management.'}, {'review_date': '2021-01-08', 'rating': 3.2, 'comments': 'Consistently meets performance standards.'}], 'benefits': {'health_insurance': 'Silver Plan', 'retirement_plan': '401K', 'paid_time_off': 28}, 'emergency_contact': {'name': 'Michael Doe', 'relationship': 'Friend', 'phone_number': '******-465-9759'}, 'notes': 'Received Employee of the Month award in 2022.'}), Document(page_content='<#ref#> Johnson Female, born 1955-06-17. Job UX in Design. Skills: SQL, Kubernetes, AWS Python. Reviews Rated 4.6 on 2021-11-06: Exceeded expectations in the last project Rated 4.5 on 2020-0222: Exceeded expectations in the last.. Location: at Singapore Office, Remote: True. Notes: Completed leadership in 2021', metadata={'_id': {'$oid': '6669c346ce0888213014cce0'}, 'employee_id': 'E123461', 'first_name': 'Robert', 'last_name': 'Johnson', 'gender': 'Female', 'date_of_birth': '1955-06-17', 'address': {'street': '462 Main Street', 'city': 'Springfield', 'state': 'IL', 'postal_code': '62704', 'country': 'USA'}, 'contact_details': {'email': '<EMAIL>', 'phone_number': '******-449-3367'}, 'job_details': {'job_title': 'UX Designer', 'department': 'Design', 'hire_date': '2006-10-08', 'employment_type': 'Full-Time', 'salary': 235758, 'currency': 'USD'}, 'work_location': {'nearest_office': 'Singapore Office', 'is_remote': True}, 'reporting_manager': 'M987658', 'skills': ['SQL', 'Kubernetes', 'AWS', 'Python'], 'performance_reviews': [{'review_date': '2021-11-06', 'rating': 4.6, 'comments': 'Exceeded expectations in the last project.'}, {'review_date': '2020-02-22', 'rating': 4.5, 'comments': 'Exceeded expectations in the last project.'}], 'benefits': {'health_insurance': 'Bronze Plan', 'retirement_plan': '401K', 'paid_time_off': 29}, 'emergency_contact': {'name': 'Jane Smith', 'relationship': 'Spouse', 'phone_number': '******-687-6856'}, 'notes': 'Completed leadership training in 2021.'}), Document(page_content='##>, born 19980613 Executive.: JavaScriptjs Docker, AWS Reviews Rated. 2023-11-: Exceeded expectations in last project Rated. 202011-04 improvement management Location at San Office Remote: Notes:oted to Engineer <#ref#'), Document(page_content='ref> Job in. SQL AWS,. on--: Exceeded expectations in the last project.. Location: Works at Toronto Office, Remote: True. Notes: Completed leadership training in 2021.', metadata={'_id': {'$oid': '6669c346ce0888213014cce2'}, 'employee_id': 'E123463', 'first_name': 'Chris', 'last_name': 'Lee', 'gender': 'Male', 'date_of_birth': '1960-03-22', 'address': {'street': '523 Main Street', 'city': 'Springfield', 'state': 'IL', 'postal_code': '62704', 'country': 'USA'}, 'contact_details': {'email': '<EMAIL>', 'phone_number': '******-928-5679'}, 'job_details': {'job_title': 'DevOps Engineer', 'department': 'Operations', 'hire_date': '2001-07-28', 'employment_type': 'Full-Time', 'salary': 227846, 'currency': 'USD'}, 'work_location': {'nearest_office': 'Toronto Office', 'is_remote': True}, 'reporting_manager': 'M987660', 'skills': ['Docker', 'SQL', 'AWS', 'Kubernetes'], 'performance_reviews': [{'review_date': '2022-12-28', 'rating': 4.5, 'comments': 'Consistently meets performance standards.'}, {'review_date': '2020-10-19', 'rating': 3.6, 'comments': 'Exceeded expectations in the last project.'}], 'benefits': {'health_insurance': 'Gold Plan', 'retirement_plan': '401K', 'paid_time_off': 26}, 'emergency_contact': {'name': 'Michael Smith', 'relationship': 'Parent', 'phone_number': '******-613-9745'}, 'notes': 'Completed leadership training in 2021.'})]\n"]}], "source": ["compressed_docs = compression_retriever.invoke(\"Who is the CEO?\")\n", "print(compressed_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5zS6dy9Xs5zs"}, "outputs": [], "source": ["from langchain.chains import RetrievalQA\n", "\n", "chain = RetrievalQA.from_chain_type(llm=llm, retriever=compression_retriever)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MbWb-U6Qs63d", "outputId": "40988519-3ec5-47e6-c0f0-5c07eb01e3e1"}, "outputs": [{"data": {"text/plain": ["{'query': 'Who is the CEO?', 'result': '<PERSON> is the CEO.'}"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"query\": \"Who is the CEO?\"})"]}, {"cell_type": "markdown", "metadata": {"id": "yztKKzUBjutu"}, "source": ["### RAG with LlamaIndex and MongoDB (Coming Soon)"]}, {"cell_type": "markdown", "metadata": {"id": "cXayuAxdvvJY"}, "source": ["### RAG with Hay<PERSON><PERSON><PERSON> and MongoDB (Coming Soon)"]}, {"cell_type": "markdown", "metadata": {"id": "v17DmdWrtljW"}, "source": ["# Part 3: AI Agent Application: HR Use Case (POLM AI Stack)\n"]}, {"cell_type": "markdown", "metadata": {"id": "Zb-cV52MtXLO"}, "source": ["### AI Agents with <PERSON><PERSON><PERSON><PERSON><PERSON> and MongoDB (Coming Soon)"]}, {"cell_type": "markdown", "metadata": {"id": "ZTrJzcZVtgqT"}, "source": ["### AI Agents with LlamaIndex and MongoDB (Coming Soon)"]}, {"cell_type": "markdown", "metadata": {"id": "IfhbUrS6tisA"}, "source": ["### AI Agents with HayStack and MongoDB (Coming Soon)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jaWcmx11tlJ6"}, "outputs": [], "source": []}], "metadata": {"colab": {"collapsed_sections": ["VXlm_J_TokJp", "0AOQw0Caosxu", "4UaKjc5nugfd", "ALrfaObSteOs", "v17DmdWrtljW"], "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"00d416447b384df9a6693aabc1d7b066": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f524a94f5e17404fbb4136d8353d7e83", "max": 124, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_deef824e229a4dda8f5b101d01b539e5", "value": 124}}, "019719ef505e4195a3949a3377b56d30": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "01b0df8bdef24414a58e66a22c153c27": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fab87ba2f882420fa196cbccf8f0e527", "placeholder": "​", "style": "IPY_MODEL_81ed26eace8a410bb32cf07184bbf5a6", "value": " 456k/456k [00:00&lt;00:00, 1.20MB/s]"}}, "03ef423561244611bb84194ff5e16e62": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cfa56fe3e8344d929de0d039ddccf123", "IPY_MODEL_f539aa41eec4432a9b8913cb2728d8f3", "IPY_MODEL_4e27764e680e4d6b9d3cf382fbaf4af3"], "layout": "IPY_MODEL_c3d2e5e0424d40a7ae3f558273752b0c"}}, "04ea4736ba124bf8b7bf134034df99cf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_425118acf1a942388d06b8df85969e16", "max": 1355256, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e29d42772ac14455b7e865f3564d884d", "value": 1355256}}, "0a4842103ee643819288478d10e1f5bf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1188156d723149998bfad83e08367b31": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "11ec0174c492444a80781491eba487a9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1a20f211e2134ecfbaf93cb0360ffb01": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1bc7bcda3df64b5aae373b0549199b11": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1c7824f63f2b4830a912c994491949ea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "216e8c43ac764438829913a23d837d22": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "22c56a8202cb464a96fa10fec9530a3b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2313dec83052473db2a816c55b13c541": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "25ee621e6df94981ae56a0c3ba014b77": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2663f37beb344e7f820553e62f75f8eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_25ee621e6df94981ae56a0c3ba014b77", "placeholder": "​", "style": "IPY_MODEL_c6f1ff2e984646d9a54ef416c84ae86c", "value": " 1.36M/1.36M [00:00&lt;00:00, 6.55MB/s]"}}, "269551fabedf43b6b4908c3a2689e349": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2736f54b4c69460aa26f282b57df6062": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_49f54ca0c5554a4fa83475d25ac1de86", "IPY_MODEL_c3ade9cc493a4c8095ad61b2d6e10a68", "IPY_MODEL_d4a2bea3de76466baccaa58bea0e4e10"], "layout": "IPY_MODEL_2c175f0a69fd4145b32d96319f8458a0"}}, "2843b96f076247f288890bd01e9bbb9f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_49bd1bbd3248497786b53ec084269aea", "placeholder": "​", "style": "IPY_MODEL_988c76ffa0d44bc29f7c976c471fc2bb", "value": "model.safetensors: 100%"}}, "2bec76bc0a61410a9d13bfa19cf1e8fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3a58ddbbd0d84265a16775b72a4c1700", "IPY_MODEL_6c7980f7fe89499fbc802b96e7912ec9", "IPY_MODEL_98b166b52ceb4c0983afb941b2fa3d89"], "layout": "IPY_MODEL_9b1a5bd5f30f49fcb482f66a8be0a3c4"}}, "2c175f0a69fd4145b32d96319f8458a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2ca04d5d0d1f4119b8a09378d607027b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_51d6af68ab104106b27f1a36679307af", "placeholder": "​", "style": "IPY_MODEL_11ec0174c492444a80781491eba487a9", "value": "tokenizer.json: 100%"}}, "32c5f0f4904d4892b1af67ad7c24bc9d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3305584369b34cbc849657a2f139d9aa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "34b5c14eb8d5458d88021619fd922870": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3a58ddbbd0d84265a16775b72a4c1700": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0a4842103ee643819288478d10e1f5bf", "placeholder": "​", "style": "IPY_MODEL_019719ef505e4195a3949a3377b56d30", "value": "config.json: 100%"}}, "3aa58481baad48108ace10d930c9b67a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3b42ec9d44864160aea9a60fea759dc5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3d08f5c00f484a06b912b6fe39b8b5dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "40f6e6d8d31e4a80b564f9680fc3086f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_269551fabedf43b6b4908c3a2689e349", "placeholder": "​", "style": "IPY_MODEL_8771925543b34e6ca9ee17c54376a96e", "value": "special_tokens_map.json: 100%"}}, "4172588542704c22a9dc3d18d85d005b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "425118acf1a942388d06b8df85969e16": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "485d120e97d84490a748637ffcf9acfc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2ca04d5d0d1f4119b8a09378d607027b", "IPY_MODEL_be66d6ca168b47b285bf87508d41b0a7", "IPY_MODEL_748d0ce8cebf419da635760804eda8ad"], "layout": "IPY_MODEL_ffae8e5bf50f4d27a97ba71f54cf8dbe"}}, "49998b99218049c6924788fe63495164": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "49bd1bbd3248497786b53ec084269aea": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "49f54ca0c5554a4fa83475d25ac1de86": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ca2d3143b86a4d859075432856e6e3f1", "placeholder": "​", "style": "IPY_MODEL_b96b41705fb442a19a4fab35a148308f", "value": "config.json: 100%"}}, "4b759c4c9cb14b8899a25ef227f7344b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a0f6f95b032a4cb6bd183946641ada74", "placeholder": "​", "style": "IPY_MODEL_216e8c43ac764438829913a23d837d22", "value": " 548M/548M [00:03&lt;00:00, 197MB/s]"}}, "4e27764e680e4d6b9d3cf382fbaf4af3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ddd46e7d4d3b4aed85a79be1495ef390", "placeholder": "​", "style": "IPY_MODEL_937c1c93228845db9317d299d08cdbde", "value": " 26.0/26.0 [00:00&lt;00:00, 1.90kB/s]"}}, "5017f719739744d9b79f5a7319ae66aa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "505fb9d7c0fa4f5ebe66c9de5e28303b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5153b484addd455d808df819c8cea810": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "51d6af68ab104106b27f1a36679307af": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "56460f46535b44e6882f7c69a5f28b15": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "571d73c95bf94f55afb95ec211db04b9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5722cd31511743228a3a9b1cfaa6046b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6dafd1fac8e3403fa7952ab26a9c2b88", "placeholder": "​", "style": "IPY_MODEL_5d0b70bbf7a347ae978a6ef420b3d24c", "value": "vocab.txt: 100%"}}, "5868884d768b439cb4acaabfba7c923f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_661a42f00e52448ca2da806a682471b9", "placeholder": "​", "style": "IPY_MODEL_34b5c14eb8d5458d88021619fd922870", "value": "tokenizer_config.json: 100%"}}, "5b74c17e75fb497299e9c1530a728c31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5d0b70bbf7a347ae978a6ef420b3d24c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "63c5ce6a7549461a94f4fa331d407cc0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "661a42f00e52448ca2da806a682471b9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "667a3931517447998a36f9e2c008f167": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6c2b4e78356545a99908d877187e004e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6c7980f7fe89499fbc802b96e7912ec9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bd97594b64314022996f832ede459a57", "max": 875, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a68cd234da99493cb972e5cf2dd7876a", "value": 875}}, "6dafd1fac8e3403fa7952ab26a9c2b88": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "707aee13704849ab9d773ef023718a9c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "710be8bf3d4a491d894abd3df47ba431": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "72f00a37ef474b2e8995a0adb1ed14f5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "748d0ce8cebf419da635760804eda8ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8a1a1c2c99ce4503b45a2f8aacbbd0aa", "placeholder": "​", "style": "IPY_MODEL_571d73c95bf94f55afb95ec211db04b9", "value": " 2.92M/2.92M [00:00&lt;00:00, 5.08MB/s]"}}, "75827958f1364e36a264500a3212be5f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "768c62a7bb6c439f9796b074b7e7bcfa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7693215e866040199c7c1fe4d4a5c95b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7a413b5421a644c1a9485417328a2287", "placeholder": "​", "style": "IPY_MODEL_3d08f5c00f484a06b912b6fe39b8b5dd", "value": " 709M/709M [01:04&lt;00:00, 7.07MB/s]"}}, "76955346f2bf47c3a4f0e1310fcaf1e0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_505fb9d7c0fa4f5ebe66c9de5e28303b", "placeholder": "​", "style": "IPY_MODEL_cc6e978269d2426eb1f6645079fb1f4f", "value": "model.safetensors: 100%"}}, "778da85e2f964d3ea7d182f02ef9b157": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_72f00a37ef474b2e8995a0adb1ed14f5", "placeholder": "​", "style": "IPY_MODEL_a5f9f7e2dce949a0b8f155d139e63bcc", "value": " 996k/996k [00:00&lt;00:00, 1.26MB/s]"}}, "7a413b5421a644c1a9485417328a2287": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c8dce3661f44eb792f8a77149bc9121": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_63c5ce6a7549461a94f4fa331d407cc0", "max": 709388104, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7dc3b352b49e4f0d98103a1d9c651c02", "value": 709388104}}, "7dc3b352b49e4f0d98103a1d9c651c02": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "80d3feb5c6df496b8eb1384f9e679c0d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1188156d723149998bfad83e08367b31", "max": 548105171, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3b42ec9d44864160aea9a60fea759dc5", "value": 548105171}}, "811e576944f64c389bc9d3597f29f60a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3aa58481baad48108ace10d930c9b67a", "placeholder": "​", "style": "IPY_MODEL_cd6dd979e9264438b833ee502920a8c4", "value": "generation_config.json: 100%"}}, "818bd02163c54e499dee383d132d4edf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "81ed26eace8a410bb32cf07184bbf5a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8385e62b7f614135ad4caecfa1acd6e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f2793c25172342ba9bf0764fe0d2ff9f", "placeholder": "​", "style": "IPY_MODEL_be0fdf7ee4b64bcb8e9c4885ee31c236", "value": " 1.19k/1.19k [00:00&lt;00:00, 73.0kB/s]"}}, "84dbdafd20664eae8107360a0d4414fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8771925543b34e6ca9ee17c54376a96e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8a1a1c2c99ce4503b45a2f8aacbbd0aa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ac8fda081464c07bf5ba56eeda46cb0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8af2e6048b19443a823ac73484bbe78e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9027bfdc32ef4f5da50d1b8db94064c4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8af2e6048b19443a823ac73484bbe78e", "placeholder": "​", "style": "IPY_MODEL_56460f46535b44e6882f7c69a5f28b15", "value": "tokenizer.json: 100%"}}, "937c1c93228845db9317d299d08cdbde": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9537cf878f724a2ca3f32159df928854": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "954c2fb207e8435087a041757e7f4db9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "988c76ffa0d44bc29f7c976c471fc2bb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "98b02f8ee4e64bc7961726eb8579ed43": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_76955346f2bf47c3a4f0e1310fcaf1e0", "IPY_MODEL_7c8dce3661f44eb792f8a77149bc9121", "IPY_MODEL_7693215e866040199c7c1fe4d4a5c95b"], "layout": "IPY_MODEL_49998b99218049c6924788fe63495164"}}, "98b166b52ceb4c0983afb941b2fa3d89": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22c56a8202cb464a96fa10fec9530a3b", "placeholder": "​", "style": "IPY_MODEL_5017f719739744d9b79f5a7319ae66aa", "value": " 875/875 [00:00&lt;00:00, 50.7kB/s]"}}, "9a3981bd1e3641ee945e53fbb6ddc1fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b046a671c0d34b9680f60479fa448a24", "placeholder": "​", "style": "IPY_MODEL_6c2b4e78356545a99908d877187e004e", "value": "merges.txt: 100%"}}, "9a48da0564b74ed1bfc1a3ac2d4c8104": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9b1a5bd5f30f49fcb482f66a8be0a3c4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f4a0a4ec0d44c39b72d3122829dc833": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f69551508804a6f8e98141b1fdc69cd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_818bd02163c54e499dee383d132d4edf", "max": 1191, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d37b5e7152c9487b8b1f69061734378d", "value": 1191}}, "a0f6f95b032a4cb6bd183946641ada74": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a4d38b15d8994408b5210b9cee1af094": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a5f9f7e2dce949a0b8f155d139e63bcc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a68cd234da99493cb972e5cf2dd7876a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a6ba05bb53224bfbb5066a2e45374b6a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a718c10ba3b34003bc77347add93d510": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_667a3931517447998a36f9e2c008f167", "max": 995526, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9537cf878f724a2ca3f32159df928854", "value": 995526}}, "b046a671c0d34b9680f60479fa448a24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b32cc92d88034412a7565144f2c87e6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3305584369b34cbc849657a2f139d9aa", "max": 125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2313dec83052473db2a816c55b13c541", "value": 125}}, "b57672e0a393488a90bd710e6f5142df": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_40f6e6d8d31e4a80b564f9680fc3086f", "IPY_MODEL_b32cc92d88034412a7565144f2c87e6c", "IPY_MODEL_bb767daff1024bd2b7b24fcbedd44dc2"], "layout": "IPY_MODEL_b95955e7a17c4da19a6265d8f14fc2dd"}}, "b95955e7a17c4da19a6265d8f14fc2dd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b96b41705fb442a19a4fab35a148308f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ba9f5e4371b44f1dadc915ce8bc723d3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bb767daff1024bd2b7b24fcbedd44dc2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a6ba05bb53224bfbb5066a2e45374b6a", "placeholder": "​", "style": "IPY_MODEL_bdcca25b302c467daabe44031c77b5fa", "value": " 125/125 [00:00&lt;00:00, 6.57kB/s]"}}, "bd97594b64314022996f832ede459a57": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bdcca25b302c467daabe44031c77b5fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "be0fdf7ee4b64bcb8e9c4885ee31c236": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "be66d6ca168b47b285bf87508d41b0a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_954c2fb207e8435087a041757e7f4db9", "max": 2919362, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4172588542704c22a9dc3d18d85d005b", "value": 2919362}}, "bfc151053b514af784bab696b319fe9c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c0591a9484134dd3b457b9668208167e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e20d091e71f6408681ccd65fcddaae43", "placeholder": "​", "style": "IPY_MODEL_d2f86a96f1cb4a79aeccbb108b75545b", "value": " 1.04M/1.04M [00:00&lt;00:00, 2.65MB/s]"}}, "c2a04b12483046a19a4e976733c6d0c0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c3ade9cc493a4c8095ad61b2d6e10a68": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d1af2b459b614e60b67a0cfec1dc8820", "max": 665, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_768c62a7bb6c439f9796b074b7e7bcfa", "value": 665}}, "c3d2e5e0424d40a7ae3f558273752b0c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c41416abc37d4b3e8f25c09dfaa4b279": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6f1ff2e984646d9a54ef416c84ae86c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ca2d3143b86a4d859075432856e6e3f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cb131570e5f9484aba06aa617e36ef4c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_710be8bf3d4a491d894abd3df47ba431", "max": 456318, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_84dbdafd20664eae8107360a0d4414fa", "value": 456318}}, "cc6e978269d2426eb1f6645079fb1f4f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cd6dd979e9264438b833ee502920a8c4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cfa56fe3e8344d929de0d039ddccf123": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1a20f211e2134ecfbaf93cb0360ffb01", "placeholder": "​", "style": "IPY_MODEL_5b74c17e75fb497299e9c1530a728c31", "value": "tokenizer_config.json: 100%"}}, "d1af2b459b614e60b67a0cfec1dc8820": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d2f86a96f1cb4a79aeccbb108b75545b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d30cb1946dd240f29cd1bfe134175c3e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5722cd31511743228a3a9b1cfaa6046b", "IPY_MODEL_a718c10ba3b34003bc77347add93d510", "IPY_MODEL_778da85e2f964d3ea7d182f02ef9b157"], "layout": "IPY_MODEL_8ac8fda081464c07bf5ba56eeda46cb0"}}, "d341cee699fb484e915e5f3c4ecc8747": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1bc7bcda3df64b5aae373b0549199b11", "max": 1042301, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_707aee13704849ab9d773ef023718a9c", "value": 1042301}}, "d37b5e7152c9487b8b1f69061734378d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d4a2bea3de76466baccaa58bea0e4e10": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c41416abc37d4b3e8f25c09dfaa4b279", "placeholder": "​", "style": "IPY_MODEL_1c7824f63f2b4830a912c994491949ea", "value": " 665/665 [00:00&lt;00:00, 47.9kB/s]"}}, "d97cc41e6c8d4c19b1bec11467be06bb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_811e576944f64c389bc9d3597f29f60a", "IPY_MODEL_00d416447b384df9a6693aabc1d7b066", "IPY_MODEL_f844ce795f944faead3c53de7abbc839"], "layout": "IPY_MODEL_fac7d27c4f274302ae5302d0d7bae26f"}}, "ddd46e7d4d3b4aed85a79be1495ef390": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "deef824e229a4dda8f5b101d01b539e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e20d091e71f6408681ccd65fcddaae43": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e29d42772ac14455b7e865f3564d884d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e501066eb3004cf4b41d8688217955fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9a3981bd1e3641ee945e53fbb6ddc1fa", "IPY_MODEL_cb131570e5f9484aba06aa617e36ef4c", "IPY_MODEL_01b0df8bdef24414a58e66a22c153c27"], "layout": "IPY_MODEL_ba9f5e4371b44f1dadc915ce8bc723d3"}}, "e7720af430484ac9b6f043b5a7b0caf7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5868884d768b439cb4acaabfba7c923f", "IPY_MODEL_9f69551508804a6f8e98141b1fdc69cd", "IPY_MODEL_8385e62b7f614135ad4caecfa1acd6e5"], "layout": "IPY_MODEL_bfc151053b514af784bab696b319fe9c"}}, "e7c87974b78049eaaf39349c583c5b17": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_eb43ca8e12854bab83f3006444931965", "IPY_MODEL_d341cee699fb484e915e5f3c4ecc8747", "IPY_MODEL_c0591a9484134dd3b457b9668208167e"], "layout": "IPY_MODEL_32c5f0f4904d4892b1af67ad7c24bc9d"}}, "eb43ca8e12854bab83f3006444931965": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_75827958f1364e36a264500a3212be5f", "placeholder": "​", "style": "IPY_MODEL_5153b484addd455d808df819c8cea810", "value": "vocab.json: 100%"}}, "ec7b5206d22b4adb9a4f7af6dafaebb5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ee8f79e18ea748618f8e680943360a05": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2843b96f076247f288890bd01e9bbb9f", "IPY_MODEL_80d3feb5c6df496b8eb1384f9e679c0d", "IPY_MODEL_4b759c4c9cb14b8899a25ef227f7344b"], "layout": "IPY_MODEL_9f4a0a4ec0d44c39b72d3122829dc833"}}, "f126e6c8f6ed4798973562c9f545f2bb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f2793c25172342ba9bf0764fe0d2ff9f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f524a94f5e17404fbb4136d8353d7e83": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f539aa41eec4432a9b8913cb2728d8f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2a04b12483046a19a4e976733c6d0c0", "max": 26, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ec7b5206d22b4adb9a4f7af6dafaebb5", "value": 26}}, "f844ce795f944faead3c53de7abbc839": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a4d38b15d8994408b5210b9cee1af094", "placeholder": "​", "style": "IPY_MODEL_9a48da0564b74ed1bfc1a3ac2d4c8104", "value": " 124/124 [00:00&lt;00:00, 6.78kB/s]"}}, "fab87ba2f882420fa196cbccf8f0e527": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fabe74c986d148c0b4677627826377d1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9027bfdc32ef4f5da50d1b8db94064c4", "IPY_MODEL_04ea4736ba124bf8b7bf134034df99cf", "IPY_MODEL_2663f37beb344e7f820553e62f75f8eb"], "layout": "IPY_MODEL_f126e6c8f6ed4798973562c9f545f2bb"}}, "fac7d27c4f274302ae5302d0d7bae26f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ffae8e5bf50f4d27a97ba71f54cf8dbe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}