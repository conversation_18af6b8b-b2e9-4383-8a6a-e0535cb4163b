{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/advanced_techniques/retrieval_strategies_mongodb_llamaindex.ipynb)\n", "\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://www.mongodb.com/developer/products/atlas/optimize-relevance-mongodb-llamaindex/?utm_campaign=devrel&utm_source=cross-post&utm_medium=organic_social&utm_content=https%3A%2F%2Fgithub.com%2Fmongodb-developer%2FGenAI-Showcase&utm_term=apoorva.joshi)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Optimizing for relevance using MongoDB and LlamaIndex\n", "\n", "In this notebook, we will explore and tune different retrieval options in MongoDB's LlamaIndex integration to get the most relevant results."]}, {"cell_type": "markdown", "metadata": {"id": "TScxhzzCoi9q"}, "source": ["## Step 1: Install libraries\n", "\n", "- **pymongo**: Python package to interact with MongoDB databases and collections\n", "<p>\n", "- **llama-index**: Python package for the LlamaIndex LLM framework\n", "<p>\n", "- **llama-index-llms-openai**: Python package to use OpenAI models via their LlamaIndex integration \n", "<p>\n", "- **llama-index-vector-stores-mongodb**: Python package for MongoDB’s LlamaIndex integration "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "PqqPt3h_UbeG"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m23.2.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m24.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip install -qU pymongo llama-index llama-index-llms-openai llama-index-vector-stores-mongodb datasets openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Setup prerequisites\n", "\n", "- **Set the MongoDB connection string**: Follow the steps [here](https://www.mongodb.com/docs/manual/reference/connection-string/) to get the connection string from the Atlas UI.\n", "\n", "- **Set the OpenAI API key**: Steps to obtain an API key as [here](https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Bs3Safw_Uj00", "outputId": "5644eb4e-1132-483c-a8ac-b8fce85da591"}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "from pymongo import MongoClient"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "g0GJ9efPUtfA", "outputId": "1bc3addc-a31e-4a16-9dba-d3486679a419"}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter your OpenAI API key: \")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "qa2Bn-N-pp9a"}, "outputs": [], "source": ["MONGODB_URI = getpass.getpass(\"Enter your MongoDB URI: \")\n", "mongodb_client = MongoClient(\n", "    MONGODB_URI, appname=\"devrel.showcase.retrieval_strategies_llamaindex\"\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "rn4FIfvSo33q"}, "source": ["## Step 3: Load and process the dataset"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datasets import load_dataset\n", "from llama_index.core import Document"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jMYkRQwiVag2", "outputId": "e26784b4-e0fe-48d4-b8e3-9bff5a0c3ad0"}, "outputs": [], "source": ["data = load_dataset(\"MongoDB/embedded_movies\", split=\"train\")\n", "data = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>plot</th>\n", "      <th>runtime</th>\n", "      <th>genres</th>\n", "      <th>fullplot</th>\n", "      <th>directors</th>\n", "      <th>writers</th>\n", "      <th>countries</th>\n", "      <th>poster</th>\n", "      <th>languages</th>\n", "      <th>cast</th>\n", "      <th>title</th>\n", "      <th>num_mflix_comments</th>\n", "      <th>rated</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>type</th>\n", "      <th>metacritic</th>\n", "      <th>plot_embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>199.0</td>\n", "      <td>[Action]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[0.0007293965299999999, -0.026834568000000003,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>22.0</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>0</td>\n", "      <td>TV-G</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.022837115, -0.022941574000000003, 0.014937...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>101.0</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>[USA]</td>\n", "      <td>None</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[0.00023330492999999998, -0.028511643000000003...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>88.0</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>None</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>The Black Pirate</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.005927917, -0.033394486, 0.0015323418, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>58.0</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[<PERSON>]</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>[USA]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[English]</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>For Heaven's Sake</td>\n", "      <td>0</td>\n", "      <td>PASSED</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>movie</td>\n", "      <td>NaN</td>\n", "      <td>[-0.0059373598, -0.026604708, -0.0070914757000...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                plot  runtime  \\\n", "0  Young <PERSON> is left a lot of money when her ...    199.0   \n", "1  A penniless young man tries to save an heiress...     22.0   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...    101.0   \n", "3  Seeking revenge, an athletic young man joins t...     88.0   \n", "4  An irresponsible young millionaire changes his...     58.0   \n", "\n", "                       genres  \\\n", "0                    [Action]   \n", "1     [Comedy, Short, Action]   \n", "2  [Action, Adventure, Drama]   \n", "3         [Adventure, Action]   \n", "4   [Action, Comedy, Romance]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "3  A nobleman vows to avenge the death of his fat...   \n", "4  The Uptown Boy, <PERSON><PERSON> (<PERSON>) is a...   \n", "\n", "                              directors  \\\n", "0  [<PERSON>, <PERSON>]   \n", "1       [<PERSON>, <PERSON>]   \n", "2                      [<PERSON>]   \n", "3                       [<PERSON>]   \n", "4                          [<PERSON>]   \n", "\n", "                                             writers countries  \\\n", "0  [<PERSON> (screenplay), <PERSON>...     [USA]   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]     [USA]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...     [USA]   \n", "3  [<PERSON> (story), <PERSON> (a...     [USA]   \n", "4  [<PERSON> (story), <PERSON> (story), <PERSON>.     [USA]   \n", "\n", "                                              poster  languages  \\\n", "0  https://m.media-amazon.com/images/M/MV5BMzgxOD...  [English]   \n", "1  https://m.media-amazon.com/images/M/MV5BNzE1OW...  [English]   \n", "2                                               None  [English]   \n", "3  https://m.media-amazon.com/images/M/MV5BMzU0ND...       None   \n", "4  https://m.media-amazon.com/images/M/MV5BMTcxMT...  [English]   \n", "\n", "                                                cast                  title  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...  The Perils of Pauline   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     From Hand to Mouth   \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>             <PERSON>   \n", "3  [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>..       The Black Pirate   \n", "4  [<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.      For Heaven's Sake   \n", "\n", "   num_mflix_comments   rated                                         imdb  \\\n", "0                   0    None    {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1                   0    TV-G   {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2                   0    None   {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "3                   1    None  {'id': 16654, 'rating': 7.2, 'votes': 1146}   \n", "4                   0  PASSED   {'id': 16895, 'rating': 7.6, 'votes': 918}   \n", "\n", "                                              awards   type  metacritic  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "3    {'nominations': 0, 'text': '1 win.', 'wins': 1}  movie         NaN   \n", "4  {'nominations': 1, 'text': '1 nomination.', 'w...  movie         NaN   \n", "\n", "                                      plot_embedding  \n", "0  [0.0007293965299999999, -0.026834568000000003,...  \n", "1  [-0.022837115, -0.022941574000000003, 0.014937...  \n", "2  [0.00023330492999999998, -0.028511643000000003...  \n", "3  [-0.005927917, -0.033394486, 0.0015323418, -0....  \n", "4  [-0.0059373598, -0.026604708, -0.0070914757000...  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# Fill Nones in the dataframe\n", "data = data.fillna({\"genres\": \"[]\", \"languages\": \"[]\", \"cast\": \"[]\", \"imdb\": \"{}\"})"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["documents = []\n", "\n", "for _, row in data.iterrows():\n", "    # Extract required fields\n", "    title = row[\"title\"]\n", "    rating = row[\"imdb\"].get(\"rating\", 0)\n", "    languages = row[\"languages\"]\n", "    cast = row[\"cast\"]\n", "    genres = row[\"genres\"]\n", "    # Create the metadata attribute\n", "    metadata = {\"title\": title, \"rating\": rating, \"languages\": languages}\n", "    # Create the text attribute\n", "    text = f\"Title: {title}\\nPlot: {row['fullplot']}\\nCast: {', '.join(item for item in cast)}\\nGenres: {', '.join(item for item in  genres)}\\nLanguages: {', '.join(item for item in languages)}\\nRating: {rating}\"\n", "    documents.append(Document(text=text, metadata=metadata))"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: The Perils of Pauline\n", "Plot: Young <PERSON> is left a lot of money when her wealthy uncle dies. However, her uncle's secretary has been named as her guardian until she marries, at which time she will officially take possession of her inheritance. Meanwhile, her \"guardian\" and his confederates constantly come up with schemes to get rid of <PERSON> so that he can get his hands on the money himself.\n", "Cast: <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "Genres: Action\n", "Languages: English\n", "Rating: 7.6\n"]}], "source": ["print(documents[0].text)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'title': 'The Perils of <PERSON>', 'rating': 7.6, 'languages': ['English']}\n"]}], "source": ["print(documents[0].metadata)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Create MongoDB Atlas vector store"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["from llama_index.core import StorageContext, VectorStoreIndex\n", "from llama_index.core.settings import Settings\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "from llama_index.vector_stores.mongodb import MongoDBAtlasVectorSearch\n", "from pymongo.errors import OperationFailure\n", "from pymongo.operations import SearchIndexModel"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["Settings.embed_model = OpenAIEmbedding(model=\"text-embedding-3-small\")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["VS_INDEX_NAME = \"vector_index\"\n", "FTS_INDEX_NAME = \"fts_index\"\n", "DB_NAME = \"llamaindex\"\n", "COLLECTION_NAME = \"hybrid_search\"\n", "collection = mongodb_client[DB_NAME][COLLECTION_NAME]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["vector_store = MongoDBAtlasVectorSearch(\n", "    mongodb_client,\n", "    db_name=DB_NAME,\n", "    collection_name=COLLECTION_NAME,\n", "    vector_index_name=VS_INDEX_NAME,\n", "    fulltext_index_name=FTS_INDEX_NAME,\n", "    embedding_key=\"embedding\",\n", "    text_key=\"text\",\n", ")\n", "# If the collection has documents with embeddings already, create the vector store index from the vector store\n", "if collection.count_documents({}) > 0:\n", "    vector_store_index = VectorStoreIndex.from_vector_store(vector_store)\n", "# If the collection does not have documents, embed and ingest them into the vector store\n", "else:\n", "    vector_store_context = StorageContext.from_defaults(vector_store=vector_store)\n", "    vector_store_index = VectorStoreIndex.from_documents(\n", "        documents, storage_context=vector_store_context, show_progress=True\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Create Atlas Search indexes"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["vs_model = SearchIndexModel(\n", "    definition={\n", "        \"fields\": [\n", "            {\n", "                \"type\": \"vector\",\n", "                \"path\": \"embedding\",\n", "                \"numDimensions\": 1536,\n", "                \"similarity\": \"cosine\",\n", "            },\n", "            {\"type\": \"filter\", \"path\": \"metadata.rating\"},\n", "            {\"type\": \"filter\", \"path\": \"metadata.languages\"},\n", "        ]\n", "    },\n", "    name=VS_INDEX_NAME,\n", "    type=\"vectorSearch\",\n", ")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["fts_model = SearchIndexModel(\n", "    definition={\"mappings\": {\"dynamic\": False, \"fields\": {\"text\": {\"type\": \"string\"}}}},\n", "    name=FTS_INDEX_NAME,\n", "    type=\"search\",\n", ")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Duplicate index found for model <pymongo.operations.SearchIndexModel object at 0x31d4c33d0>. Skipping index creation.\n", "Duplicate index found for model <pymongo.operations.SearchIndexModel object at 0x31d4c1c60>. Skipping index creation.\n"]}], "source": ["for model in [vs_model, fts_model]:\n", "    try:\n", "        collection.create_search_index(model=model)\n", "    except OperationFailure:\n", "        print(f\"Duplicate index found for model {model}. Skipping index creation.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Get movie recommendations"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def get_recommendations(query: str, mode: str, **kwargs) -> None:\n", "    \"\"\"\n", "    Get movie recommendations\n", "\n", "    Args:\n", "        query (str): User query\n", "        mode (str): Retrieval mode. One of (default, text_search, hybrid)\n", "    \"\"\"\n", "    query_engine = vector_store_index.as_query_engine(\n", "        similarity_top_k=5, vector_store_query_mode=mode, **kwargs\n", "    )\n", "    response = query_engine.query(query)\n", "    nodes = response.source_nodes\n", "    for node in nodes:\n", "        title = node.metadata[\"title\"]\n", "        rating = node.metadata[\"rating\"]\n", "        score = node.score\n", "        print(f\"Title: {title} | Rating: {rating} | Relevance Score: {score}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Full-text search"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: Hellboy II: The Golden Army | Rating: 7.0 | Relevance Score: 5.93734884262085\n", "Title: The Matrix Revolutions | Rating: 6.7 | Relevance Score: 4.574477195739746\n", "Title: The Matrix | Rating: 8.7 | Relevance Score: 4.387373924255371\n", "Title: Go with <PERSON> Jamil | Rating: 6.9 | Relevance Score: 3.5394840240478516\n", "Title: Terminator Salvation | Rating: 6.7 | Relevance Score: 3.3378987312316895\n"]}], "source": ["get_recommendations(\n", "    query=\"Action movies about humans fighting machines\",\n", "    mode=\"text_search\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Vector search"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: Death Machine | Rating: 5.7 | Relevance Score: 0.7407287359237671\n", "Title: Real Steel | Rating: 7.1 | Relevance Score: 0.7364246845245361\n", "Title: Soldier | Rating: 5.9 | Relevance Score: 0.7282171249389648\n", "Title: Terminator 3: Rise of the Machines | Rating: 6.4 | Relevance Score: 0.7266112565994263\n", "Title: Last Action Hero | Rating: 6.2 | Relevance Score: 0.7250100374221802\n"]}], "source": ["get_recommendations(\n", "    query=\"Action movies about humans fighting machines\", mode=\"default\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hybrid search"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: Hellboy II: The Golden Army | Rating: 7.0 | Relevance Score: 0.5\n", "Title: Death Machine | Rating: 5.7 | Relevance Score: 0.5\n", "Title: The Matrix Revolutions | Rating: 6.7 | Relevance Score: 0.25\n", "Title: Real Steel | Rating: 7.1 | Relevance Score: 0.25\n", "Title: Soldier | Rating: 5.9 | Relevance Score: 0.16666666666666666\n"]}], "source": ["# Vector and full-text search weighted equal by default\n", "get_recommendations(query=\"Action movies about humans fighting machines\", mode=\"hybrid\")"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: Death Machine | Rating: 5.7 | Relevance Score: 0.7\n", "Title: Real Steel | Rating: 7.1 | Relevance Score: 0.35\n", "Title: Hellboy II: The Golden Army | Rating: 7.0 | Relevance Score: 0.30000000000000004\n", "Title: Soldier | Rating: 5.9 | Relevance Score: 0.2333333333333333\n", "Title: Terminator 3: Rise of the Machines | Rating: 6.4 | Relevance Score: 0.175\n"]}], "source": ["# Higher alpha, vector search dominates\n", "get_recommendations(\n", "    query=\"Action movies about humans fighting machines\",\n", "    mode=\"hybrid\",\n", "    alpha=0.7,\n", ")"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: Hellboy II: The Golden Army | Rating: 7.0 | Relevance Score: 0.7\n", "Title: The Matrix Revolutions | Rating: 6.7 | Relevance Score: 0.35\n", "Title: Death Machine | Rating: 5.7 | Relevance Score: 0.3\n", "Title: The Matrix | Rating: 8.7 | Relevance Score: 0.2333333333333333\n", "Title: Go with <PERSON> Jamil | Rating: 6.9 | Relevance Score: 0.175\n"]}], "source": ["# Lower alpha, full-text search dominates\n", "get_recommendations(\n", "    query=\"Action movies about humans fighting machines\",\n", "    mode=\"hybrid\",\n", "    alpha=0.3,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Combining metadata filters with search"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["from llama_index.core.vector_stores import (\n", "    FilterCondition,\n", "    FilterOperator,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    MetadataFilters,\n", ")"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["filters = MetadataFilters(\n", "    filters=[\n", "        MetadataFilter(key=\"metadata.rating\", value=7, operator=FilterOperator.GT),\n", "        MetadataFilter(\n", "            key=\"metadata.languages\", value=\"English\", operator=FilterOperator.EQ\n", "        ),\n", "    ],\n", "    condition=FilterCondition.AND,\n", ")"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: Real Steel | Rating: 7.1 | Relevance Score: 0.7\n", "Title: T2 3-D: Battle Across Time | Rating: 7.8 | Relevance Score: 0.35\n", "Title: The Matrix | Rating: 8.7 | Relevance Score: 0.30000000000000004\n", "Title: Predator | Rating: 7.8 | Relevance Score: 0.2333333333333333\n", "Title: Transformers | Rating: 7.1 | Relevance Score: 0.175\n"]}], "source": ["get_recommendations(\n", "    query=\"Action movies about humans fighting machines\",\n", "    mode=\"hybrid\",\n", "    alpha=0.7,\n", "    filters=filters,\n", ")"]}], "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"00135b96c1e34abf94352e5d14dfbfc2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0b639c296a6e42e883957f4053e08881": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "13eec1cf9f3b4e27995eb7735bbf43aa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "148567c981e74f1a9b840fb5463f6c1f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2001c71b7c0649ad94991dc00c2c1c2b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "30ccab778b894d8c86359fb850ee76f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b7df766690574c09b4942e0d27151171", "max": 5183, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e65a397cb2e44371886c3f51362a9bc6", "value": 5183}}, "33ef6c005a52428cb00a9e7ccb0e6b2c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "350c3f298a7b414c8ab6ea4492fb98c3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35b668058eca435a86829f32ca421859": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33ef6c005a52428cb00a9e7ccb0e6b2c", "max": 2816079, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b8c4d550a4fb475d8a66c1e5deefb1f2", "value": 2816079}}, "4950b546681b4c8cbec0a9c3acf08c37": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_350c3f298a7b414c8ab6ea4492fb98c3", "placeholder": "​", "style": "IPY_MODEL_6275b672934d4cc383cc4c18f3dfe4b7", "value": "100%"}}, "4cbd2428f91c40d092e1c3bc80171123": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fbf3da22c9954c3ab5995fff682084ba", "max": 57638, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6a61062dbe92469889f767985c4f5b59", "value": 57638}}, "51c3a472109243c681898fb32aeda7d7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f22b82b8010a4a79b0b42908966cc89e", "IPY_MODEL_35b668058eca435a86829f32ca421859", "IPY_MODEL_84d25add023044d68f383b81dacaf462"], "layout": "IPY_MODEL_c3375ea1a272481babcaece7f79b428e"}}, "5b4d7df8ac4e4a788d7684f47f1d1b76": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5f822791ad0243d99cffb09f57b6257d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_13eec1cf9f3b4e27995eb7735bbf43aa", "placeholder": "​", "style": "IPY_MODEL_8bda824cef9c493b83704d511554954c", "value": "datasets/fiqa.zip: 100%"}}, "6275b672934d4cc383cc4c18f3dfe4b7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6770f34c4be644cda13221e47d00ca28": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6a3ffc1cb8764532b215d51cae6e44be": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6a61062dbe92469889f767985c4f5b59": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "72b0800f217f4559aea1c0db64d6594c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e71944737601445a9e8a1f39fe32d445", "placeholder": "​", "style": "IPY_MODEL_edd9d4c3787f44e2a6d7fe43dec354f2", "value": " 57638/57638 [00:00&lt;00:00, 91199.90it/s]"}}, "7350acfbe3bd4e1cb4ff49290a6cd58f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "73cddc3fa8bb4495b335018fae3b063e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4950b546681b4c8cbec0a9c3acf08c37", "IPY_MODEL_30ccab778b894d8c86359fb850ee76f2", "IPY_MODEL_c25ebc49169a4fccae65c84ba71b50c7"], "layout": "IPY_MODEL_00135b96c1e34abf94352e5d14dfbfc2"}}, "84d25add023044d68f383b81dacaf462": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c45d82a40d2c4096b6c00b6c93290add", "placeholder": "​", "style": "IPY_MODEL_9cbf8f18e9dd4cd3acc274ad3f4868ae", "value": " 2.69M/2.69M [00:00&lt;00:00, 6.83MiB/s]"}}, "879141a9900d4741985af9ee5f230760": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5f822791ad0243d99cffb09f57b6257d", "IPY_MODEL_f9596cf74c4b428594a0be76406d96be", "IPY_MODEL_b014d38bd40740a18eaf90a6d2f69439"], "layout": "IPY_MODEL_6a3ffc1cb8764532b215d51cae6e44be"}}, "8bda824cef9c493b83704d511554954c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "983b3ad86d71468c9efc7e01926c70e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9903eb80686c492aa8a5e3190ccc798a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9cbf8f18e9dd4cd3acc274ad3f4868ae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b014d38bd40740a18eaf90a6d2f69439": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2001c71b7c0649ad94991dc00c2c1c2b", "placeholder": "​", "style": "IPY_MODEL_0b639c296a6e42e883957f4053e08881", "value": " 17.1M/17.1M [00:06&lt;00:00, 2.10MiB/s]"}}, "b446bbe72b8344dab8c5b637ff3e48bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b7df766690574c09b4942e0d27151171": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b8c4d550a4fb475d8a66c1e5deefb1f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c25ebc49169a4fccae65c84ba71b50c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7350acfbe3bd4e1cb4ff49290a6cd58f", "placeholder": "​", "style": "IPY_MODEL_5b4d7df8ac4e4a788d7684f47f1d1b76", "value": " 5183/5183 [00:00&lt;00:00, 45467.14it/s]"}}, "c2c384a4406b4b9f9dfc57779d7246ee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c3375ea1a272481babcaece7f79b428e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c45d82a40d2c4096b6c00b6c93290add": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e260dd2233ff479db1471ec42f0b907a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e65a397cb2e44371886c3f51362a9bc6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e71944737601445a9e8a1f39fe32d445": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "edd9d4c3787f44e2a6d7fe43dec354f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ef9546a04f6d47e081b7021376e1fdab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f2be4ffe3b984e9989af25faceb3c9fc", "IPY_MODEL_4cbd2428f91c40d092e1c3bc80171123", "IPY_MODEL_72b0800f217f4559aea1c0db64d6594c"], "layout": "IPY_MODEL_983b3ad86d71468c9efc7e01926c70e6"}}, "f22b82b8010a4a79b0b42908966cc89e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6770f34c4be644cda13221e47d00ca28", "placeholder": "​", "style": "IPY_MODEL_c2c384a4406b4b9f9dfc57779d7246ee", "value": "datasets/scifact.zip: 100%"}}, "f2be4ffe3b984e9989af25faceb3c9fc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e260dd2233ff479db1471ec42f0b907a", "placeholder": "​", "style": "IPY_MODEL_b446bbe72b8344dab8c5b637ff3e48bf", "value": "100%"}}, "f9596cf74c4b428594a0be76406d96be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9903eb80686c492aa8a5e3190ccc798a", "max": 17948027, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_148567c981e74f1a9b840fb5463f6c1f", "value": 17948027}}, "fbf3da22c9954c3ab5995fff682084ba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}