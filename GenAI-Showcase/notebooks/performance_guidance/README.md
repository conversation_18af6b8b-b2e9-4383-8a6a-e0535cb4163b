Performance guidance showing how MongoDB Atlas Vector Search compares against other vector databases.

Jupyter Notebooks comparing MongoDB Atlas Vector Search with other vector databases and search engines.

| Title | Notebook |
|-------|-------|
| Vector Database Comparison For AI Workloads: Elasticsearch vs MongoDB Atlas Vector Search | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/performance_guidance/ai_workload_database_architecture_mongodb_elastic.ipynb) |
| AI Database Performance Comparison For AI Workloads: PostgreSQL/PgVector vs MongoDB Atlas Vector Search | [![View Notebook](https://img.shields.io/badge/view-notebook-orange?logo=jupyter)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/performance_guidance/vector_database_performance_guidance_mongondb_pgvector.ipynb) |
