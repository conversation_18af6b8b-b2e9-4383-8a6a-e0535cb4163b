# Ada and MongoDB GenAI showcases

## Introduction
Welcome to the GenAI Showcase repository! This repository contains code and resources for sample AI projects that have been developed for various verticals through the MongoDB MAAP program. In this section, we explore different AI technologies and their applications across diverse industries.

## About Ada
[Ada](https://www.ada.cx/) is an AI customer service company making service extraordinary for everyone. A transformation partner and platform that helps enterprise companies deliver experiences people love by accelerating a businesses’ AI maturity and improving Agent performance.

## Industry Showcases
In this section, we highlight some of the industry-specific use cases and success stories where our AI solutions have made a significant impact:

### Regulatory Compliance
- **AI-Powered Transaction Compliance Monitoring System with Document Ingestion**: In this use case, we are showcasing the foundation of a compliance monitoring system that leverages MongoDB's vector search capabilities, Voyage AI embedding models, and advanced LLMs to automate regulatory checks on financial transactions.
