## MongoDB's GenAI Showcase

Welcome to MongoDB's Generative AI Showcase Repository!

Whether you are just starting out on your Generative AI journey, or looking to build advanced GenAI applications, we've got you covered. This repository has an exhaustive list of examples and sample applications that cover Retrieval-Augmented Generation (RAG), AI Agents, and industry-specific use cases.

Discover how MongoDB integrates into RAG pipelines and AI Agents, serving as a vector database, operational database, and memory provider.

## Contents

This repo mainly contains:

| Folder | Description |
|--------|-------------|
| [`notebooks`](notebooks/README.md) | Jupyter notebooks examples for RAG, agentic applications, evaluations etc. |
| [`apps`](apps/README.md) | JavaScript and Python apps and demos |
| [`partners`](partners/README.md) | Contributions from our AI partners |
| [`tools`](tools/README.md) | Utility tools for embeddings generation and data processing |
| [`mcp`](mcp/README.md) | Model Context Protocol (MCP) servers for AI assistant integration |
| [`misc`](misc/README.md) | Additional utilities and experimental features |

## Learning Paths

### 🚀 **Beginner Path**
Start here if you're new to GenAI with MongoDB:
1. [Basic RAG with LlamaIndex](notebooks/rag/building_RAG_with_LlamaIndex_and_MongoDB_Vector_Database.ipynb)
2. [Simple Chatbot with OpenAI](notebooks/rag/chat_with_pdf_mongodb_openai_langchain_POLM_AI_Stack.ipynb)
3. [Local RAG PDF App](apps/local-rag-pdf/)

### 🎯 **Intermediate Path**
Ready for more advanced concepts:
1. [Advanced Retrieval Strategies](notebooks/advanced_techniques/retrieval_strategies_mongodb_llamaindex.ipynb)
2. [Function Calling with MongoDB](notebooks/advanced_techniques/function_calling_mongodb_as_a_toolbox.ipynb)
3. [AI Agents with LangGraph](notebooks/agents/agentic_rag_factory_safety_assistant_with_langgraph_langchain_mongodb.ipynb)

### 🏆 **Expert Path**
For advanced practitioners:
1. [Vector Quantization](notebooks/advanced_techniques/quantized_vector_ingestion_with_cohere_and_mongodb.ipynb)
2. [Multi-Agent Systems](notebooks/agents/smolagents_multi-agent_micro_agents.ipynb)
3. [Performance Optimization](notebooks/performance_guidance/vector_database_performance_guidance_mongondb_pgvector.ipynb)

## Getting Started

You will need to connect to a MongoDB cluster to run any of the apps or examples in this repo. Follow these steps to get set up:

* Register for a [free MongoDB Atlas account](https://www.mongodb.com/cloud/atlas/register)
* [Create a new database cluster](https://www.mongodb.com/docs/guides/atlas/cluster/)
* [Obtain the connection string](https://www.mongodb.com/docs/guides/atlas/connection-string/) for your database cluster

## Contributing

We welcome contributions! Please read our [Contribution Guidelines](CONTRIBUTING.md) for more information on how to participate.

## License

This project is licensed under the [MIT License](LICENSE).

## Getting Support

As you work through these examples, if you encounter any problems, please [open a new issue](https://github.com/mongodb-developer/GenAI-Showcase/issues/new).

## Additional Resources

* [AI Learning Hub](https://www.mongodb.com/resources/use-cases/artificial-intelligence?utm_campaign=ai_learning_hub&utm_source=github&utm_medium=referral)
* [GenAI Community Forum](https://www.mongodb.com/community/forums/c/generative-ai/162)
* [Tutorials and code examples from our official docs](https://github.com/mongodb/docs-notebooks)
