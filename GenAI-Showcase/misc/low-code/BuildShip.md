# BuildShip AI Agent for Rental Booking Workflow

This guide demonstrates how to create an AI agent for handling rental booking workflows using BuildShip's no-code platform with MongoDB Aggregation and Insert integrations.

![Agent](./Buildship-BookingAgent.png)

The workflow above shows the BuildShip interface where a REST API endpoint (/bookingAgent) triggers a Claude AI Assistant that processes booking requests. The assistant is connected to MongoDB operations including an Aggregate Collection node that queries the sample_airbnb.listingsAndReviews collection, enabling the AI to search and filter available properties based on user criteria.

### Youtube link to a live walkthrough

[![AI Show Video](https://img.youtube.com/vi/tmdtMyVIb60/0.jpg)](https://www.youtube.com/watch?v=tmdtMyVIb60)

## Quick Start

1. Access the pre-built workflow by visiting [this BuildShip remix link](https://buildship.app/remix/a9a9fcdf-4636-4640-bfd1-d149f6326728)
2. Click "Duplicate & Remix" to create your own copy of the workflow

## Workflow Overview

This AI agent workflow demonstrates how to:
- Process rental booking requests using natural language
- Utilize MongoDB Aggregation to search available properties
- Insert new bookings into MongoDB
- Handle the complete rental booking lifecycle

### Key Components

1. **MongoDB Aggregation Tool**
   - Searches available properties based on user criteria
   - Filters by date range, location, and amenities
   - Returns matching properties with availability

2. **MongoDB Insert Integration**
   - Creates new booking records
   - Stores customer information
   - Manages booking status and confirmation

### Workflow Steps

1. User submits rental inquiry
2. AI agent processes natural language request
3. MongoDB Aggregation searches available properties
4. AI presents options to user
5. User selects property
6. MongoDB Insert creates booking record
7. AI confirms booking details

## Customization

This workflow serves as a versatile template for creating various types of AI agents powered by MongoDB. You can:

### Adapt for Different Use Cases
- Transform into a customer service agent using MongoDB for ticket management
- Create an inventory management agent with MongoDB for stock tracking
- Build a content recommendation agent using MongoDB aggregation pipelines
- Develop a data analysis agent leveraging MongoDB's analytical capabilities

### Modify Core Components
- Customize the property search criteria
- Adjust the booking validation rules
- Add additional MongoDB operations
- Integrate with other tools and services

### Extend MongoDB Integration
- Implement complex aggregation pipelines for advanced queries
- Add MongoDB Atlas Search for full-text search capabilities
- Utilize MongoDB Change Streams for real-time updates
- Incorporate MongoDB Charts for data visualization

## Benefits

- Natural language processing for booking requests
- Automated property availability checks
- Structured data storage in MongoDB
- Scalable booking management system
- Easy to customize and extend
- Template for building various MongoDB-powered AI agents
