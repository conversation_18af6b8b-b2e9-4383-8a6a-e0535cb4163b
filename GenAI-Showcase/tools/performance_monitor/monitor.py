"""
Performance monitoring utilities for GenAI applications with MongoDB.

This module provides tools to monitor and analyze the performance of
RAG applications, vector searches, and LLM interactions.
"""

import time
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import pymongo
from contextlib import contextmanager


@dataclass
class PerformanceMetric:
    """Data class for storing performance metrics."""
    operation: str
    duration_ms: float
    timestamp: datetime
    metadata: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None


class PerformanceMonitor:
    """Monitor and track performance metrics for GenAI applications."""
    
    def __init__(self, 
                 mongo_uri: Optional[str] = None,
                 database: str = "performance_monitoring",
                 collection: str = "metrics",
                 log_to_console: bool = True):
        """
        Initialize the performance monitor.
        
        Args:
            mongo_uri: MongoDB connection string for storing metrics
            database: Database name for metrics storage
            collection: Collection name for metrics storage
            log_to_console: Whether to log metrics to console
        """
        self.metrics: List[PerformanceMetric] = []
        self.log_to_console = log_to_console
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Setup MongoDB connection if provided
        self.mongo_client = None
        self.collection = None
        if mongo_uri:
            try:
                self.mongo_client = pymongo.MongoClient(mongo_uri)
                self.collection = self.mongo_client[database][collection]
                self.logger.info("Connected to MongoDB for metrics storage")
            except Exception as e:
                self.logger.warning(f"Failed to connect to MongoDB: {e}")
    
    @contextmanager
    def measure(self, operation: str, **metadata):
        """
        Context manager to measure operation performance.
        
        Args:
            operation: Name of the operation being measured
            **metadata: Additional metadata to store with the metric
        
        Example:
            with monitor.measure("vector_search", query_type="similarity"):
                results = collection.aggregate(pipeline)
        """
        start_time = time.time()
        error_message = None
        success = True
        
        try:
            yield
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            duration_ms = (time.time() - start_time) * 1000
            
            metric = PerformanceMetric(
                operation=operation,
                duration_ms=duration_ms,
                timestamp=datetime.now(),
                metadata=metadata,
                success=success,
                error_message=error_message
            )
            
            self._record_metric(metric)
    
    def _record_metric(self, metric: PerformanceMetric):
        """Record a performance metric."""
        self.metrics.append(metric)
        
        if self.log_to_console:
            status = "✅" if metric.success else "❌"
            self.logger.info(
                f"{status} {metric.operation}: {metric.duration_ms:.2f}ms"
            )
        
        # Store in MongoDB if available
        if self.collection:
            try:
                self.collection.insert_one(asdict(metric))
            except Exception as e:
                self.logger.warning(f"Failed to store metric in MongoDB: {e}")
    
    def get_metrics(self, 
                   operation: Optional[str] = None,
                   last_n: Optional[int] = None) -> List[PerformanceMetric]:
        """
        Get recorded metrics with optional filtering.
        
        Args:
            operation: Filter by operation name
            last_n: Return only the last n metrics
        
        Returns:
            List of performance metrics
        """
        metrics = self.metrics
        
        if operation:
            metrics = [m for m in metrics if m.operation == operation]
        
        if last_n:
            metrics = metrics[-last_n:]
        
        return metrics
    
    def get_summary(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """
        Get performance summary statistics.
        
        Args:
            operation: Filter by operation name
        
        Returns:
            Dictionary with summary statistics
        """
        metrics = self.get_metrics(operation)
        
        if not metrics:
            return {"message": "No metrics available"}
        
        durations = [m.duration_ms for m in metrics if m.success]
        
        if not durations:
            return {"message": "No successful operations"}
        
        return {
            "operation": operation or "all",
            "total_operations": len(metrics),
            "successful_operations": len(durations),
            "success_rate": len(durations) / len(metrics) * 100,
            "avg_duration_ms": sum(durations) / len(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "total_duration_ms": sum(durations)
        }
    
    def export_metrics(self, filepath: str, format: str = "json"):
        """
        Export metrics to file.
        
        Args:
            filepath: Path to export file
            format: Export format ("json" or "csv")
        """
        if format == "json":
            with open(filepath, 'w') as f:
                json.dump([asdict(m) for m in self.metrics], f, 
                         indent=2, default=str)
        elif format == "csv":
            import pandas as pd
            df = pd.DataFrame([asdict(m) for m in self.metrics])
            df.to_csv(filepath, index=False)
        else:
            raise ValueError("Format must be 'json' or 'csv'")
        
        self.logger.info(f"Exported {len(self.metrics)} metrics to {filepath}")


# Convenience functions for common operations
def monitor_vector_search(monitor: PerformanceMonitor):
    """Decorator for monitoring vector search operations."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with monitor.measure("vector_search", 
                               function=func.__name__,
                               args_count=len(args),
                               kwargs_keys=list(kwargs.keys())):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def monitor_llm_call(monitor: PerformanceMonitor):
    """Decorator for monitoring LLM API calls."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with monitor.measure("llm_call",
                               function=func.__name__,
                               args_count=len(args),
                               kwargs_keys=list(kwargs.keys())):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def monitor_embedding_generation(monitor: PerformanceMonitor):
    """Decorator for monitoring embedding generation."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with monitor.measure("embedding_generation",
                               function=func.__name__,
                               args_count=len(args),
                               kwargs_keys=list(kwargs.keys())):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Example usage
if __name__ == "__main__":
    # Initialize monitor
    monitor = PerformanceMonitor()
    
    # Example: Monitor a vector search operation
    with monitor.measure("vector_search", query_type="similarity", k=5):
        time.sleep(0.1)  # Simulate operation
    
    # Example: Monitor an LLM call
    with monitor.measure("llm_call", model="gpt-3.5-turbo", tokens=150):
        time.sleep(0.5)  # Simulate operation
    
    # Get summary
    summary = monitor.get_summary()
    print(json.dumps(summary, indent=2))
    
    # Export metrics
    monitor.export_metrics("performance_metrics.json")
