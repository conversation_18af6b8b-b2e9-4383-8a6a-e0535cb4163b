# Performance Monitor

A comprehensive performance monitoring tool for GenAI applications using MongoDB Atlas Vector Search.

## 🎯 Features

- **Real-time Monitoring**: Track performance of vector searches, LLM calls, and embedding generation
- **MongoDB Integration**: Store metrics in MongoDB for historical analysis
- **Statistical Analysis**: Generate performance summaries and insights
- **Export Capabilities**: Export metrics to JSON or CSV formats
- **Decorator Support**: Easy integration with existing code using decorators

## 🚀 Quick Start

### Basic Usage

```python
from monitor import PerformanceMonitor

# Initialize monitor
monitor = PerformanceMonitor()

# Monitor an operation
with monitor.measure("vector_search", query_type="similarity", k=5):
    # Your vector search code here
    results = collection.aggregate(pipeline)

# Get performance summary
summary = monitor.get_summary()
print(summary)
```

### With MongoDB Storage

```python
monitor = PerformanceMonitor(
    mongo_uri="mongodb+srv://user:<EMAIL>/",
    database="performance_monitoring",
    collection="metrics"
)
```

### Using Decorators

```python
@monitor_vector_search(monitor)
def search_similar_documents(query_vector, k=5):
    # Your search logic
    return results

@monitor_llm_call(monitor)
def generate_response(prompt):
    # Your LLM call
    return response
```

## 📊 Metrics Collected

- **Operation Duration**: Time taken for each operation
- **Success Rate**: Percentage of successful operations
- **Error Tracking**: Capture and analyze failures
- **Metadata**: Custom metadata for detailed analysis
- **Timestamps**: When operations occurred

## 🔧 Configuration

### Environment Variables

```bash
export MONGO_URI="mongodb+srv://user:<EMAIL>/"
export PERFORMANCE_DB="performance_monitoring"
export METRICS_COLLECTION="metrics"
```

### Programmatic Configuration

```python
monitor = PerformanceMonitor(
    mongo_uri=os.getenv("MONGO_URI"),
    database="performance_monitoring",
    collection="metrics",
    log_to_console=True
)
```

## 📈 Analysis Examples

### Get Operation Summary

```python
# Summary for all operations
summary = monitor.get_summary()

# Summary for specific operation
vector_summary = monitor.get_summary("vector_search")
```

### Export Metrics

```python
# Export to JSON
monitor.export_metrics("metrics.json", format="json")

# Export to CSV
monitor.export_metrics("metrics.csv", format="csv")
```

### Filter Metrics

```python
# Get last 10 vector search operations
recent_searches = monitor.get_metrics("vector_search", last_n=10)

# Get all LLM call metrics
llm_metrics = monitor.get_metrics("llm_call")
```

## 🎨 Integration Examples

### RAG Application

```python
from monitor import PerformanceMonitor, monitor_vector_search, monitor_llm_call

monitor = PerformanceMonitor()

@monitor_vector_search(monitor)
def retrieve_documents(query_embedding):
    pipeline = [
        {
            "$vectorSearch": {
                "index": "vector_index",
                "path": "embedding",
                "queryVector": query_embedding,
                "numCandidates": 100,
                "limit": 5
            }
        }
    ]
    return list(collection.aggregate(pipeline))

@monitor_llm_call(monitor)
def generate_answer(context, question):
    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": f"Context: {context}"},
            {"role": "user", "content": question}
        ]
    )
    return response.choices[0].message.content

def rag_pipeline(question):
    # Monitor the entire RAG pipeline
    with monitor.measure("rag_pipeline", question_length=len(question)):
        # Generate embedding
        with monitor.measure("embedding_generation"):
            query_embedding = get_embedding(question)
        
        # Retrieve documents
        documents = retrieve_documents(query_embedding)
        
        # Generate answer
        context = "\n".join([doc["text"] for doc in documents])
        answer = generate_answer(context, question)
        
        return answer
```

### Performance Dashboard

```python
def create_performance_dashboard():
    """Create a simple performance dashboard."""
    
    # Get summaries for different operations
    operations = ["vector_search", "llm_call", "embedding_generation", "rag_pipeline"]
    
    dashboard = {}
    for op in operations:
        summary = monitor.get_summary(op)
        if "avg_duration_ms" in summary:
            dashboard[op] = {
                "avg_duration": f"{summary['avg_duration_ms']:.2f}ms",
                "success_rate": f"{summary['success_rate']:.1f}%",
                "total_operations": summary['total_operations']
            }
    
    return dashboard

# Print dashboard
dashboard = create_performance_dashboard()
for operation, metrics in dashboard.items():
    print(f"\n{operation.upper()}:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value}")
```

## 🔍 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**:
   - Check connection string
   - Verify network access
   - Ensure proper authentication

2. **High Memory Usage**:
   - Limit metrics retention
   - Export and clear metrics regularly
   - Use MongoDB storage instead of in-memory

3. **Performance Overhead**:
   - Disable console logging in production
   - Use sampling for high-frequency operations
   - Batch MongoDB writes

### Best Practices

1. **Metric Retention**:
   ```python
   # Clear old metrics periodically
   if len(monitor.metrics) > 10000:
       monitor.metrics = monitor.metrics[-5000:]
   ```

2. **Sampling**:
   ```python
   import random
   
   # Sample 10% of operations
   if random.random() < 0.1:
       with monitor.measure("operation"):
           # Your code here
   ```

3. **Batch Processing**:
   ```python
   # Process metrics in batches
   for batch in chunks(operations, batch_size=100):
       with monitor.measure("batch_processing", batch_size=len(batch)):
           process_batch(batch)
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
