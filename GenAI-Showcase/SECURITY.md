# Security Policy

## Supported Versions

We actively maintain security for the following versions:

| Version | Supported          |
| ------- | ------------------ |
| main    | :white_check_mark: |

## Reporting a Vulnerability

We take security vulnerabilities seriously. If you discover a security vulnerability, please follow these steps:

### 🔒 Private Disclosure

**DO NOT** create a public GitHub issue for security vulnerabilities.

Instead, please report security vulnerabilities through one of these channels:

1. **GitHub Security Advisories** (Preferred):
   - Go to the [Security tab](https://github.com/mongodb-developer/GenAI-Showcase/security)
   - Click "Report a vulnerability"
   - Fill out the private vulnerability report

2. **Email**: Send details to the maintainers listed in [CONTRIBUTING.md](CONTRIBUTING.md)

### 📋 What to Include

When reporting a vulnerability, please include:

- **Description**: Clear description of the vulnerability
- **Impact**: Potential impact and attack scenarios
- **Reproduction**: Step-by-step instructions to reproduce
- **Affected Components**: Which files/applications are affected
- **Suggested Fix**: If you have ideas for remediation

### ⏱️ Response Timeline

- **Initial Response**: Within 48 hours
- **Status Update**: Within 7 days
- **Resolution**: Varies based on complexity, typically 30-90 days

## Security Best Practices

### 🔐 API Keys and Credentials

**Never commit API keys or credentials to the repository.**

✅ **Good Practices:**
```python
import os
api_key = os.getenv("OPENAI_API_KEY")
mongo_uri = os.getenv("MONGO_URI")
```

❌ **Bad Practices:**
```python
api_key = "sk-1234567890abcdef"  # Never do this!
mongo_uri = "***********************************..."  # Never do this!
```

### 🛡️ Input Validation

Always validate and sanitize user inputs:

```python
def validate_query(query: str) -> str:
    # Sanitize input
    if len(query) > 1000:
        raise ValueError("Query too long")
    
    # Remove potentially dangerous characters
    import re
    query = re.sub(r'[<>"\']', '', query)
    return query.strip()
```

### 🔒 MongoDB Security

1. **Connection Security**:
   - Use MongoDB Atlas with IP whitelisting
   - Enable authentication
   - Use TLS/SSL connections

2. **Query Safety**:
   - Use parameterized queries
   - Validate collection and database names
   - Implement proper error handling

```python
# Good: Parameterized query
collection.find({"field": user_input})

# Bad: String concatenation
collection.find(f"{{'field': '{user_input}'}}")  # Vulnerable to injection
```

### 🌐 Web Application Security

For web applications in this repository:

1. **CORS Configuration**:
   ```python
   # Configure CORS properly
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["http://localhost:3000"],  # Specific origins
       allow_credentials=True,
       allow_methods=["GET", "POST"],
       allow_headers=["*"],
   )
   ```

2. **Rate Limiting**:
   ```python
   from slowapi import Limiter
   
   limiter = Limiter(key_func=get_remote_address)
   
   @app.post("/query")
   @limiter.limit("10/minute")
   async def query_endpoint(request: Request):
       # Your endpoint logic
   ```

3. **Input Sanitization**:
   - Validate file uploads
   - Sanitize user queries
   - Implement size limits

### 🔍 Code Review Checklist

Before submitting PRs, ensure:

- [ ] No hardcoded credentials
- [ ] Input validation implemented
- [ ] Error messages don't leak sensitive information
- [ ] Dependencies are up to date
- [ ] Proper authentication/authorization
- [ ] Secure file handling

### 📦 Dependency Security

1. **Regular Updates**:
   ```bash
   pip install --upgrade pip
   pip-audit  # Check for known vulnerabilities
   ```

2. **Pinned Versions**:
   ```txt
   # requirements.txt
   openai==1.3.0  # Pin specific versions
   pymongo==4.6.0
   ```

3. **Security Scanning**:
   - Use GitHub Dependabot
   - Run `pip-audit` regularly
   - Monitor security advisories

### 🚨 Common Vulnerabilities to Avoid

1. **Code Injection**:
   - Never use `eval()` or `exec()` with user input
   - Validate all user inputs

2. **Path Traversal**:
   ```python
   # Good: Validate file paths
   import os
   def safe_file_path(filename):
       return os.path.join("/safe/directory", os.path.basename(filename))
   ```

3. **Information Disclosure**:
   - Don't expose stack traces to users
   - Sanitize error messages
   - Use proper logging levels

4. **Insecure Deserialization**:
   - Avoid `pickle` with untrusted data
   - Use JSON for data exchange
   - Validate serialized data

### 🔧 Security Tools

Recommended tools for security testing:

1. **Static Analysis**:
   ```bash
   pip install bandit
   bandit -r . -f json -o security-report.json
   ```

2. **Dependency Scanning**:
   ```bash
   pip install pip-audit
   pip-audit
   ```

3. **Secrets Scanning**:
   ```bash
   pip install detect-secrets
   detect-secrets scan --all-files
   ```

## Acknowledgments

We appreciate security researchers and community members who help keep this project secure. Contributors who responsibly disclose vulnerabilities will be acknowledged (with their permission) in our security advisories.

## Questions?

If you have questions about this security policy, please reach out to the maintainers listed in [CONTRIBUTING.md](CONTRIBUTING.md).
