{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# [Title of Your Notebook]\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/mongodb-developer/GenAI-Showcase/blob/main/notebooks/[category]/[your-notebook].ipynb)\n", "[![View Article](https://img.shields.io/badge/View%20Article-blue)](https://your-article-link.com)\n", "\n", "## 📋 Overview\n", "\n", "Brief description of what this notebook demonstrates.\n", "\n", "### 🎯 What You'll Learn\n", "- Key concept 1\n", "- Key concept 2\n", "- Key concept 3\n", "\n", "### 🛠️ Technologies Used\n", "- MongoDB Atlas Vector Search\n", "- [Framework/Library]\n", "- [LLM Provider]\n", "\n", "### ⏱️ Estimated Time\n", "15-20 minutes\n", "\n", "### 📋 Prerequisites\n", "- MongoDB Atlas account\n", "- [API Provider] API key\n", "- Basic Python knowledge"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Setup\n", "\n", "### Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install pymongo openai pandas numpy\n", "# Add other specific dependencies here"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pymongo\n", "import pandas as pd\n", "import numpy as np\n", "from typing import List, Dict, Any\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add other imports here"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Configuration\n", "\n", "⚠️ **Security Note**: Never commit API keys to the repository. Use environment variables or secure storage."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "MONGO_URI = os.getenv(\"MONGO_URI\", \"your-mongodb-connection-string\")\n", "API_KEY = os.getenv(\"OPENAI_API_KEY\", \"your-api-key\")\n", "\n", "# Database configuration\n", "DATABASE_NAME = \"your_database\"\n", "COLLECTION_NAME = \"your_collection\"\n", "VECTOR_INDEX_NAME = \"vector_index\"\n", "\n", "# Validate configuration\n", "if MONGO_URI == \"your-mongodb-connection-string\":\n", "    print(\"⚠️  Please set your MongoDB connection string\")\n", "if API_KEY == \"your-api-key\":\n", "    print(\"⚠️  Please set your API key\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔌 MongoDB Connection\n", "\n", "Connect to MongoDB Atlas and set up the database and collection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_mongo_client(mongo_uri: str) -> pymongo.MongoClient:\n", "    \"\"\"Establish connection to MongoDB.\"\"\"\n", "    try:\n", "        client = pymongo.MongoClient(mongo_uri)\n", "        # Test the connection\n", "        client.admin.command('ping')\n", "        print(\"✅ Connected to MongoDB successfully\")\n", "        return client\n", "    except Exception as e:\n", "        print(f\"❌ Failed to connect to MongoDB: {e}\")\n", "        raise\n", "\n", "# Connect to MongoDB\n", "client = get_mongo_client(MONGO_URI)\n", "database = client[DATABASE_NAME]\n", "collection = database[COLLECTION_NAME]\n", "\n", "print(f\"📊 Database: {DATABASE_NAME}\")\n", "print(f\"📁 Collection: {COLLECTION_NAME}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Data Preparation\n", "\n", "Prepare and load your data for the demonstration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Your data preparation code here\n", "# This could include:\n", "# - Loading sample data\n", "# - Data preprocessing\n", "# - Generating embeddings\n", "# - Inserting data into MongoDB\n", "\n", "def prepare_sample_data():\n", "    \"\"\"Prepare sample data for the demonstration.\"\"\"\n", "    # Add your data preparation logic here\n", "    pass\n", "\n", "# Prepare data\n", "prepare_sample_data()\n", "print(f\"📈 Collection contains {collection.count_documents({})} documents\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Vector Search Setup\n", "\n", "Set up vector search index and demonstrate search capabilities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_vector_search_index():\n", "    \"\"\"Create vector search index if it doesn't exist.\"\"\"\n", "    # Vector search index definition\n", "    index_definition = {\n", "        \"fields\": [\n", "            {\n", "                \"type\": \"vector\",\n", "                \"path\": \"embedding\",\n", "                \"numDimensions\": 1536,  # Adjust based on your embedding model\n", "                \"similarity\": \"cosine\"\n", "            }\n", "        ]\n", "    }\n", "    \n", "    try:\n", "        # Note: In practice, create the index through Atlas UI or Atlas CLI\n", "        print(f\"📋 Vector search index definition for '{VECTOR_INDEX_NAME}':\")\n", "        print(index_definition)\n", "        print(\"\\n💡 Create this index in MongoDB Atlas to enable vector search\")\n", "    except Exception as e:\n", "        print(f\"⚠️  Index creation note: {e}\")\n", "\n", "create_vector_search_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Main Implementation\n", "\n", "Core implementation of your GenAI application."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Your main implementation code here\n", "# This could include:\n", "# - RAG pipeline\n", "# - Agent implementation\n", "# - Evaluation framework\n", "# - Advanced techniques\n", "\n", "def main_function():\n", "    \"\"\"Main function demonstrating the key concept.\"\"\"\n", "    # Add your implementation here\n", "    pass\n", "\n", "# Run main implementation\n", "result = main_function()\n", "print(f\"✅ Implementation completed: {result}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 Testing and Validation\n", "\n", "Test the implementation with sample queries and validate results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test cases\n", "test_cases = [\n", "    \"Sample query 1\",\n", "    \"Sample query 2\",\n", "    \"Sample query 3\"\n", "]\n", "\n", "def run_tests(test_cases: List[str]):\n", "    \"\"\"Run test cases and display results.\"\"\"\n", "    results = []\n", "    \n", "    for i, test_case in enumerate(test_cases, 1):\n", "        print(f\"\\n🧪 Test Case {i}: {test_case}\")\n", "        \n", "        try:\n", "            # Run your test logic here\n", "            result = \"Sample result\"  # Replace with actual test\n", "            results.append({\"query\": test_case, \"result\": result, \"success\": True})\n", "            print(f\"✅ Result: {result}\")\n", "        except Exception as e:\n", "            results.append({\"query\": test_case, \"error\": str(e), \"success\": False})\n", "            print(f\"❌ Error: {e}\")\n", "    \n", "    return results\n", "\n", "# Run tests\n", "test_results = run_tests(test_cases)\n", "success_rate = sum(1 for r in test_results if r[\"success\"]) / len(test_results) * 100\n", "print(f\"\\n📊 Test Summary: {success_rate:.1f}% success rate\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Performance Analysis\n", "\n", "Analyze the performance of your implementation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from typing import Dict\n", "\n", "def performance_benchmark() -> Dict[str, float]:\n", "    \"\"\"Run performance benchmarks.\"\"\"\n", "    metrics = {}\n", "    \n", "    # Benchmark different operations\n", "    operations = {\n", "        \"vector_search\": lambda: \"sample_operation\",  # Replace with actual operations\n", "        \"llm_call\": lambda: \"sample_operation\",\n", "        \"end_to_end\": lambda: \"sample_operation\"\n", "    }\n", "    \n", "    for operation_name, operation_func in operations.items():\n", "        start_time = time.time()\n", "        \n", "        try:\n", "            # Run operation multiple times for average\n", "            for _ in range(5):\n", "                operation_func()\n", "            \n", "            avg_time = (time.time() - start_time) / 5\n", "            metrics[operation_name] = avg_time\n", "            print(f\"⚡ {operation_name}: {avg_time:.3f}s average\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ {operation_name} failed: {e}\")\n", "            metrics[operation_name] = None\n", "    \n", "    return metrics\n", "\n", "# Run performance benchmark\n", "performance_metrics = performance_benchmark()\n", "print(f\"\\n📊 Performance Summary: {performance_metrics}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Takeaways\n", "\n", "### What We Learned\n", "- Key insight 1\n", "- Key insight 2\n", "- Key insight 3\n", "\n", "### Best Practices\n", "- Best practice 1\n", "- Best practice 2\n", "- Best practice 3\n", "\n", "### Performance Considerations\n", "- Performance tip 1\n", "- Performance tip 2\n", "- Performance tip 3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧹 Cleanup\n", "\n", "Clean up resources and close connections."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cleanup code\n", "def cleanup():\n", "    \"\"\"Clean up resources.\"\"\"\n", "    try:\n", "        # Close MongoDB connection\n", "        client.close()\n", "        print(\"✅ MongoDB connection closed\")\n", "        \n", "        # Add other cleanup tasks here\n", "        \n", "    except Exception as e:\n", "        print(f\"⚠️  Cleanup warning: {e}\")\n", "\n", "# Run cleanup\n", "cleanup()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔗 Next Steps\n", "\n", "### Related Notebooks\n", "- [Related Notebook 1](../category/notebook1.ipynb)\n", "- [Related Notebook 2](../category/notebook2.ipynb)\n", "\n", "### Additional Resources\n", "- [MongoDB Atlas Vector Search Documentation](https://docs.atlas.mongodb.com/atlas-vector-search/)\n", "- [Framework Documentation](https://framework-docs.com)\n", "- [API Provider Documentation](https://api-docs.com)\n", "\n", "### Suggested Improvements\n", "- Enhancement 1\n", "- Enhancement 2\n", "- Enhancement 3\n", "\n", "---\n", "\n", "**📝 Note**: This notebook is part of the [MongoDB GenAI Showcase](https://github.com/mongodb-developer/GenAI-Showcase). \n", "\n", "**🤝 Contributing**: Found an issue or want to contribute? Please [open an issue](https://github.com/mongodb-developer/GenAI-Showcase/issues) or submit a pull request.\n", "\n", "**⭐ Star**: If you found this helpful, please star the repository!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}