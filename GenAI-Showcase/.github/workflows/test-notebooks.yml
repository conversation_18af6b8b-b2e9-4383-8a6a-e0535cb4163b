name: Test Notebooks

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run weekly to catch dependency issues
    - cron: '0 0 * * 0'

jobs:
  test-notebooks:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
        notebook-category: [rag, agents, evals]
      fail-fast: false
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install nbval pytest jupyter
        # Install common dependencies
        pip install pymongo openai langchain llama-index pandas numpy
    
    - name: Test notebooks syntax
      run: |
        # Test notebook syntax without execution (faster)
        find notebooks/${{ matrix.notebook-category }} -name "*.ipynb" -exec jupyter nbconvert --to script --stdout {} \; > /dev/null
    
    - name: Test selected notebooks execution
      env:
        # Use mock/test credentials for CI
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY_TEST }}
        MONGO_URI: ${{ secrets.MONGO_URI_TEST }}
      run: |
        # Only test notebooks that don't require external APIs
        # Add specific notebooks that can run in CI environment
        pytest --nbval-lax notebooks/${{ matrix.notebook-category }}/building_RAG_with_LlamaIndex_and_MongoDB_Vector_Database.ipynb || true

  test-apps:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [local-rag-pdf, embeddings_generator]
      fail-fast: false
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Test app dependencies
      run: |
        if [ -f "apps/${{ matrix.app }}/requirements.txt" ]; then
          pip install -r apps/${{ matrix.app }}/requirements.txt
        elif [ -f "tools/${{ matrix.app }}/requirements.txt" ]; then
          pip install -r tools/${{ matrix.app }}/requirements.txt
        fi
    
    - name: Run basic import tests
      run: |
        cd apps/${{ matrix.app }} 2>/dev/null || cd tools/${{ matrix.app }}
        python -c "import sys; sys.path.append('.'); 
        try:
            import app
            print('✅ App imports successfully')
        except ImportError as e:
            print(f'⚠️  Import warning: {e}')
        except Exception as e:
            print(f'ℹ️  Note: {e}')
        " || true

  lint-and-format:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install pre-commit
      run: |
        python -m pip install --upgrade pip
        pip install pre-commit
    
    - name: Run pre-commit
      run: pre-commit run --all-files
