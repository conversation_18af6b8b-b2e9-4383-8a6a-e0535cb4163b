---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 📍 Location
- **Notebook/App**: (e.g., `notebooks/rag/building_RAG_with_LlamaIndex_and_MongoDB_Vector_Database.ipynb`)
- **Cell/Line**: (if applicable)

## 🔄 Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## 💭 Expected Behavior
A clear and concise description of what you expected to happen.

## 📸 Screenshots/Error Messages
If applicable, add screenshots or paste error messages to help explain your problem.

```
Paste error message here
```

## 🖥️ Environment
- **OS**: (e.g., macOS 13.0, Ubuntu 20.04, Windows 11)
- **Python Version**: (e.g., 3.9.7)
- **Browser**: (if web app, e.g., Chrome 91.0)
- **MongoDB Version**: (e.g., Atlas 6.0, Local 7.0)

## 📦 Dependencies
- **Key Package Versions**:
  ```
  openai==1.3.0
  pymongo==4.6.0
  llama-index==0.9.0
  ```

## 🔧 Configuration
- **MongoDB Setup**: (Atlas/Local, cluster tier)
- **API Provider**: (OpenAI, Anthropic, etc.)
- **Vector Index**: (configured/not configured)

## 🔍 Additional Context
Add any other context about the problem here.

## ✅ Checklist
- [ ] I have searched existing issues to avoid duplicates
- [ ] I have provided all requested information
- [ ] I have tested with the latest version
- [ ] I have checked the documentation
