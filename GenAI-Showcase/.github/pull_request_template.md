## 📋 Pull Request Description

### 🎯 Type of Change
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test addition/improvement

### 📝 Summary
Brief description of what this PR does.

### 🔗 Related Issues
Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

### 🧪 Testing
**How has this been tested?**
- [ ] Unit tests
- [ ] Integration tests
- [ ] Manual testing
- [ ] Notebook execution
- [ ] App functionality testing

**Test Configuration:**
- Python version:
- MongoDB version:
- Key dependencies:

### 📸 Screenshots/Demo
If applicable, add screenshots or demo links.

### ✅ Checklist
**Before submitting this PR, please make sure:**

#### Code Quality
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] My changes generate no new warnings
- [ ] I have run `pre-commit run --all-files` successfully

#### Documentation
- [ ] I have updated the documentation accordingly
- [ ] I have added/updated README files for new apps/notebooks
- [ ] I have added docstrings to new functions/classes
- [ ] I have updated the main README if needed

#### Security & Best Practices
- [ ] I have not committed any API keys or sensitive information
- [ ] I have followed security best practices
- [ ] I have validated user inputs where applicable
- [ ] I have used environment variables for configuration

#### Testing & Compatibility
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have tested with multiple Python versions (if applicable)
- [ ] I have tested with different MongoDB configurations (if applicable)

#### Dependencies
- [ ] I have updated requirements.txt files if needed
- [ ] I have pinned dependency versions appropriately
- [ ] I have checked for security vulnerabilities in new dependencies

### 🔄 Breaking Changes
**Does this PR introduce any breaking changes?**
- [ ] Yes (please describe below)
- [ ] No

If yes, describe the breaking changes and migration path:

### 📊 Performance Impact
**Does this change affect performance?**
- [ ] Improves performance
- [ ] No performance impact
- [ ] May impact performance (please describe)

### 🎯 Target Audience
**Who is the target audience for this change?**
- [ ] Beginners
- [ ] Intermediate users
- [ ] Advanced users
- [ ] All users

### 📚 Learning Resources
**Are there any learning resources related to this change?**
- Documentation links:
- Tutorial links:
- Related examples:

### 🤝 Collaboration
**Additional reviewers needed:**
- [ ] MongoDB expert
- [ ] AI/ML expert
- [ ] Security review
- [ ] Documentation review

### 💬 Additional Notes
Any additional information that reviewers should know.
