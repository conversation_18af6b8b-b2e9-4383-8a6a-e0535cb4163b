{"name": "mongostory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/xai": "latest", "@aws-sdk/credential-providers": "latest", "@hookform/resolvers": "^3.9.1", "@mongodb-js/zstd": "latest", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@tailwindcss/postcss": "^4.0.12", "@tailwindcss/typography": "latest", "@types/react": "latest", "@types/react-dom": "latest", "ai": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto": "latest", "embla-carousel-react": "8.5.1", "gcp-metadata": "latest", "input-otp": "1.4.1", "jsonwebtoken": "latest", "kerberos": "latest", "lucide-react": "^0.454.0", "mongodb": "latest", "mongodb-client-encryption": "latest", "nanoid": "latest", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^19", "react-day-picker": "9.6.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-markdown": "latest", "react-markdown-editor-lite": "latest", "react-resizable-panels": "^2.1.7", "recharts": "latest", "snappy": "latest", "socks": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss": "latest", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "voyage-ai-provider": "latest", "zod": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}