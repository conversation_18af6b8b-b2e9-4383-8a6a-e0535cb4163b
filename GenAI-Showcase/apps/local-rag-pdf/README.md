# Local RAG PDF Application

A local Retrieval-Augmented Generation (RAG) application that processes PDF documents and enables intelligent question-answering using MongoDB Atlas Vector Search.

## 🚀 Features

- **PDF Processing**: Upload and process PDF documents for semantic search
- **Vector Search**: Leverage MongoDB Atlas Vector Search for efficient document retrieval
- **Local Deployment**: Run entirely on your local machine
- **Interactive UI**: Simple web interface for document upload and querying
- **Configurable Models**: Support for multiple embedding and LLM providers

## 📋 Prerequisites

- Python 3.9+
- MongoDB Atlas cluster with Vector Search enabled
- OpenAI API key (or other supported LLM provider)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/mongodb-developer/GenAI-Showcase.git
   cd GenAI-Showcase/apps/local-rag-pdf
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**:
   ```bash
   cp config.yaml.example config.yaml
   # Edit config.yaml with your MongoDB and API credentials
   ```

## ⚙️ Configuration

Edit `config.yaml` to configure your application:

```yaml
mongodb:
  uri: "your-mongodb-atlas-connection-string"
  database: "rag_database"
  collection: "documents"
  vector_index: "vector_index"

llm:
  provider: "openai"  # or "anthropic", "cohere"
  api_key: "your-api-key"
  model: "gpt-3.5-turbo"

embeddings:
  provider: "openai"
  model: "text-embedding-3-small"
  dimensions: 1536
```

## 🚀 Usage

1. **Start the application**:
   ```bash
   python app.py
   ```

2. **Access the web interface**:
   Open your browser to `http://localhost:8501`

3. **Upload PDF documents**:
   - Use the file uploader to add PDF documents
   - Wait for processing and indexing to complete

4. **Ask questions**:
   - Enter questions about your uploaded documents
   - Get AI-powered answers with source citations

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PDF Upload    │───▶│   Text Chunking  │───▶│   Embeddings    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Query    │───▶│  Vector Search   │◀───│  MongoDB Atlas  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│   LLM Response  │◀───│  Context Retrieval│
└─────────────────┘    └──────────────────┘
```

## 🔧 Customization

### Adding New LLM Providers

Extend the `rag_module.py` to support additional LLM providers:

```python
def get_llm(provider: str, config: dict):
    if provider == "your_provider":
        return YourProviderLLM(**config)
    # ... existing providers
```

### Custom Chunking Strategies

Modify the text chunking logic in `rag_module.py`:

```python
def chunk_text(text: str, strategy: str = "recursive"):
    if strategy == "custom":
        return custom_chunking_logic(text)
    # ... existing strategies
```

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**:
   - Verify your connection string is correct
   - Ensure your IP is whitelisted in MongoDB Atlas
   - Check network connectivity

2. **Vector Index Not Found**:
   - Create a vector search index in MongoDB Atlas
   - Ensure the index name matches your configuration

3. **API Rate Limits**:
   - Implement rate limiting in your configuration
   - Consider using different API tiers

### Debug Mode

Enable debug logging by setting:
```bash
export LOG_LEVEL=DEBUG
python app.py
```

## 📊 Performance Tips

- **Chunk Size**: Experiment with different chunk sizes (512-2048 tokens)
- **Overlap**: Use 10-20% overlap between chunks
- **Batch Processing**: Process multiple documents in batches
- **Caching**: Enable embedding caching for repeated queries

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../../LICENSE) file for details.

## 🔗 Related Examples

- [Building RAG with LlamaIndex](../../notebooks/rag/building_RAG_with_LlamaIndex_and_MongoDB_Vector_Database.ipynb)
- [Advanced RAG Techniques](../../notebooks/advanced_techniques/)
- [RAG Evaluation](../../notebooks/evals/)
