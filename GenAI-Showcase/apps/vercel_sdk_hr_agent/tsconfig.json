{"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es2018", "esnext.asynciterable", "dom"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "dist", "resolveJsonModule": true, "moduleResolution": "node", "allowImportingTsExtensions": true, "sourceMap": true, "declaration": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "allowJs": true, "noEmit": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}