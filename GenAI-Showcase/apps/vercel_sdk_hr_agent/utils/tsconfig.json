{"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es2018", "esnext.asynciterable", "dom"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "dist", "resolveJsonModule": true, "moduleResolution": "node", "sourceMap": true, "declaration": true, "allowSyntheticDefaultImports": true, "baseUrl": "."}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}