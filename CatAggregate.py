from pymongo import MongoClient
# Connect to MongoDB Atlas
client = MongoClient("mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority")
collection = client["Cat2"]["Doctors_Notes"]

# Grab a reference note with an existing embedding
reference_note = collection.find_one({"embedding": {"$exists": True}})

if not reference_note:
    raise Exception("No reference note found with an embedding.")

query_vector = reference_note["embedding"]

# Run vector search using your named index "notes"
pipeline = [
    {
        "$vectorSearch": {
            "index": "notes",  # Your vector index name
            "path": "embedding",
            "queryVector": query_vector,
            "numCandidates": 100,
            "limit": 5
        }
    },
    {
        "$project": {
            "noteId": 1,
            "text": 1,
            "similarityScore": { "$meta": "vectorSearchScore" }
        }
    }
]
# Execute aggregation
results = list(collection.aggregate(pipeline))

# Display results
for result in results:
    print(f"\nScore: {result['similarityScore']:.4f}")
    print(f"Note: {result['text']}")
