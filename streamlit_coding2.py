import streamlit as st
from pymongo import MongoClient
import pandas as pd

# MongoDB Config
MONGO_URI = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
DB_NAME = "Cat2"
NOTES_COLLECTION = "Doctors_Notes"
FLAGS_COLLECTION = "Coding_Flags"

# Connect to MongoDB
client = MongoClient(MONGO_URI)
db = client[DB_NAME]
notes_col = db[NOTES_COLLECTION]
flags_col = db[FLAGS_COLLECTION]

# Streamlit App
st.title("Clinical Coding Flag Explorer")

# Sidebar metric for total flags
total_flags = flags_col.count_documents({})
st.sidebar.metric("Total Coding Flags", total_flags)

# Fetch encounterIds that have flags
flagged_ids = flags_col.distinct("encounterId")
all_ids = notes_col.distinct("encounterId")

def label_with_flag_status(eid):
    return f"{eid} ✅" if eid in flagged_ids else f"{eid} ❌"

labeled_ids = [label_with_flag_status(eid) for eid in sorted(all_ids)]
selected_label = st.selectbox("Select Encounter ID", labeled_ids)
selected_encounter = selected_label.split()[0]  # Extract raw ID

if selected_encounter:
    # Show Doctor's Note
    note = notes_col.find_one({"encounterId": selected_encounter})
    st.subheader("Doctor's Note")
    st.write(note["text"] if note else "No note found.")

    # Show Coding Flag
    flag = flags_col.find_one({"encounterId": selected_encounter})
    st.subheader("Coding Flag")
    if flag:
        st.markdown(f"**Suggested Code:** `{flag['suggestedCode']}`")
        st.markdown(f"**Confidence:** `{flag['confidence']}`")
        st.markdown(f"**Reason:** {flag['reason']}")
        st.markdown(f"**Source:** _{flag['source']}_")
    else:
        st.info("No coding flag found for this encounter.")

