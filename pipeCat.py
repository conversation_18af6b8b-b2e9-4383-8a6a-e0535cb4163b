from pipecat.pipeline.pipeline import Pipeline
from pipecat.input_sources.microphone_input import MicrophoneInput
from pipecat.output_sinks.speaker_output import SpeakerOutput
from pipecat.modules.openai_chat_module import OpenAIChat
from pymongo import MongoClient
import datetime

# MongoDB setup
mongo_client = MongoClient("mongodb://localhost:27017/")
db = mongo_client["chatbot"]
collection = db["conversations"]

# Define the pipeline
pipeline = Pipeline(
    input=MicrophoneInput(),
    output=SpeakerOutput(),
    modules=[
        OpenAIChat(model="gpt-3.5-turbo")
    ]
)

# Add logging to MongoDB
@pipeline.on_message
def log_message(message):
    doc = {
        "timestamp": datetime.datetime.utcnow(),
        "user": message.user,
        "text": message.text,
        "role": message.role
    }
    collection.insert_one(doc)

# Run the pipeline
pipeline.run()

